<template>
  <div class="checkbox-manager">
    <!-- 选项管理弹窗 -->
    <el-dialog
      :title="getDialogTitle()"
      :visible.sync="dialogVisible"
      width="600px"
      append-to-body
    >
      <div class="checkbox-list">
        <div v-for="(option, index) in options" :key="index" class="option-item">
          <span>{{ option }}</span>
          <el-button
            type="danger"
            size="mini"
            icon="el-icon-delete"
            @click="deleteOption(option)"
          />
        </div>
        <div v-if="options.length === 0" class="no-data">
          暂无选项数据
        </div>
      </div>

      <div class="add-options">
        <el-input
          v-model="newOptions"
          type="textarea"
          :rows="3"
          placeholder="请输入要添加的选项，多个选项用逗号分隔"
        />
        <el-button type="primary" style="margin-top: 15px" @click="addOptions">批量添加</el-button>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">关闭</el-button>
      </div>
    </el-dialog>

    <!-- 添加按钮 -->
    <el-button
      type="primary"
      size="small"
      icon="el-icon-setting"
      @click="showDialog"
    >管理选项</el-button>
  </div>
</template>

<script>
import { getOptionsByFieldName, batchAddOptions, deleteOption } from '@/api/tumaiOaXz'

export default {
  name: 'CheckboxManager',
  props: {
    fieldName: {
      type: String,
      required: true
    },
    fieldLabel: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      options: [],
      newOptions: ''
    }
  },
  methods: {
    // 显示对话框并加载数据
    showDialog() {
      this.dialogVisible = true
      this.loadOptions()
    },

    // 加载选项数据
    async loadOptions() {
      try {
        const response = await getOptionsByFieldName(this.fieldName)
        this.options = response || []
      } catch (error) {
        console.error('加载选项数据失败:', error)
        this.$message.error('加载选项数据失败')
      }
    },

    // 批量添加选项
    async addOptions() {
      if (!this.newOptions.trim()) {
        this.$message.warning('请输入要添加的选项')
        return
      }

      try {
        const dto = {
          fieldName: this.fieldName,
          options: this.newOptions
        }

        await batchAddOptions(dto)
        this.$message.success('添加成功')
        this.newOptions = ''
        this.loadOptions()
        this.$emit('options-updated')
      } catch (error) {
        console.error('添加选项失败:', error)
        this.$message.error('添加选项失败: ' + (error.message || '未知错误'))
      }
    },

    // 删除选项
    async deleteOption(option) {
      this.$confirm('确认删除该选项?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          // 这里需要先查询该选项的ID
          // 为了简化，假设我们有一个通过选项值删除的接口
          await deleteOption(option)
          this.$message.success('删除成功')
          this.loadOptions()
          this.$emit('options-updated')
        } catch (error) {
          console.error('删除选项失败:', error)
          this.$message.error('删除选项失败')
        }
      }).catch(() => {})
    },

    // 获取对话框标题
    getDialogTitle() {
      return this.fieldLabel + '选项管理'
    }
  }
}
</script>

  <style scoped>
  .checkbox-manager {
    display: inline-block;
    margin-left: 10px;
  }

  .checkbox-list {
    max-height: 300px;
    overflow-y: auto;
    margin-bottom: 20px;
    border: 1px solid #EBEEF5;
    border-radius: 4px;
    padding: 10px;
  }

  .option-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #EBEEF5;
  }

  .option-item:last-child {
    border-bottom: none;
  }

  .no-data {
    text-align: center;
    color: #909399;
    padding: 20px 0;
  }

  .add-options {
    margin-top: 20px;
  }
  </style>
