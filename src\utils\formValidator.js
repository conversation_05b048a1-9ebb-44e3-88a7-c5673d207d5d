/**
 * 表单验证与错误定位工具
 * 用于验证表单并自动定位到错误字段
 */

/**
 * 验证表单并自动定位到错误字段
 * @param {Object} formRef - 表单引用对象
 * @param {Function} submitCallback - 验证成功后的回调函数
 * @param {Object} options - 配置选项
 * @returns {Promise<boolean>} - 返回验证结果
 */
export function validateFormAndLocate(formRef, submitCallback, options = {}) {
  if (!formRef) {
    console.error('表单引用对象不能为空')
    return Promise.resolve(false)
  }

  // 默认选项
  const defaultOptions = {
    // 是否显示错误提示
    showErrorMsg: true,
    // 是否滚动到错误字段
    scrollToError: true,
    // 高亮错误字段的时间(毫秒)
    highlightDuration: 2000,
    // 错误提示前缀
    msgPrefix: '请检查'
  }

  // 合并选项
  const mergedOptions = { ...defaultOptions, ...options }

  return new Promise(resolve => {
    formRef.validate(valid => {
      if (valid) {
        // 验证成功，执行回调
        if (typeof submitCallback === 'function') {
          submitCallback()
        }
        resolve(true)
      } else {
        console.error('表单验证失败')

        // 下一个事件循环中查找错误元素
        setTimeout(() => {
          const errorEl = document.querySelector('.el-form-item.is-error')
          if (errorEl && mergedOptions.showErrorMsg) {
            // 获取字段标签
            const labelEl = errorEl.querySelector('.el-form-item__label')
            const label = labelEl ? labelEl.textContent.trim() : '未知字段'

            // 找到所在区域
            const section = errorEl.closest('.form-section')
            let sectionName = ''
            if (section) {
              const divider = section.querySelector('.el-divider__text')
              if (divider) {
                sectionName = divider.textContent.trim()
              }
            }

            // 显示错误信息
            if (sectionName) {
              const message = `${mergedOptions.msgPrefix}"${sectionName}"中的"${label}"字段`
              // 兼容全局Vue实例和Vue组件实例
              if (formRef.$message) {
                formRef.$message.warning(message)
              } else if (window.$message) {
                window.$message.warning(message)
              }
            }

            // 滚动到错误元素
            if (mergedOptions.scrollToError) {
              errorEl.scrollIntoView({ behavior: 'smooth', block: 'center' })
            }

            // 高亮错误元素
            const originalBg = errorEl.style.backgroundColor
            errorEl.style.backgroundColor = 'rgba(245, 108, 108, 0.1)'

            setTimeout(() => {
              errorEl.style.backgroundColor = originalBg
            }, mergedOptions.highlightDuration)
          }
        }, 0)

        resolve(false)
      }
    })
  })
}

/**
 * 增强表单，添加验证和定位方法
 * @param {Vue} Vue - Vue实例
 */
export function installFormValidator(Vue) {
  // 全局注册方法
  Vue.prototype.$validateFormAndLocate = validateFormAndLocate

  // 可以在这里添加更多的表单相关工具方法
}

export default {
  install(Vue) {
    installFormValidator(Vue)
  }
}
