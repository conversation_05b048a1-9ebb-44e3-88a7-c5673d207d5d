import axios from 'axios'
import { getToken } from '@/utils/auth'
import { Message } from 'element-ui'

/**
 * 统一文件上传函数
 * @param {File} file - 要上传的文件对象
 * @param {Function} onSuccess - 上传成功回调函数
 * @param {Function} onError - 上传失败回调函数
 * @param {String} api - 上传接口地址，默认使用MinIO上传接口
 * @returns {Promise} 上传结果的Promise
 */
export function uploadFile(file, onSuccess, onError, api = '/api/minio/upload') {
  // 验证文件大小，默认限制15MB
  const isLt15M = file.size / 1024 / 1024 < 15
  if (!isLt15M) {
    Message.error('文件大小不能超过 15MB!')
    onError && onError(new Error('文件大小不能超过 15MB!'))
    return Promise.reject(new Error('文件大小不能超过 15MB!'))
  }

  const formData = new FormData()
  formData.append('file', file)

  const config = {
    headers: {
      'Authorization': getToken(),
      'Content-Type': 'multipart/form-data'
    }
  }

  return axios.post(api, formData, config)
    .then(response => {
      if (response.status === 200 && response.data) {
        // 适配MinIO响应格式 - 它应该返回URL
        const result = {
          name: file.name,
          url: response.data.url || '',
          id: response.data.fileName || file.name, // 使用文件名作为ID
          size: response.data.size || file.size,
          success: response.data.success || true
        }

        onSuccess && onSuccess(result)
        return result
      } else {
        const error = new Error('上传失败')
        onError && onError(error)
        return Promise.reject(error)
      }
    })
    .catch(error => {
      Message.error('文件上传失败: ' + (error.message || '未知错误'))
      onError && onError(error)
      return Promise.reject(error)
    })
}

/**
 * 删除已上传的文件
 * @param {String} fileName - 文件名称
 * @param {Function} onSuccess - 删除成功回调函数
 * @param {Function} onError - 删除失败回调函数
 * @param {String} api - 删除接口地址，默认使用MinIO删除接口
 * @returns {Promise} 删除结果的Promise
 */
export function deleteFile(fileName, onSuccess, onError, api = '/api/minio/delete') {
  if (!fileName) {
    const error = new Error('文件名不能为空')
    onError && onError(error)
    return Promise.reject(error)
  }

  return axios.get(`${api}?fileName=${encodeURIComponent(fileName)}`, {
    headers: { 'Authorization': getToken() }
  })
    .then(response => {
      if (response.status === 200 && response.data && response.data.success) {
        onSuccess && onSuccess(response.data)
        return response.data
      } else {
        const error = new Error('删除失败')
        onError && onError(error)
        return Promise.reject(error)
      }
    })
    .catch(error => {
      Message.error('文件删除失败: ' + (error.message || '未知错误'))
      onError && onError(error)
      return Promise.reject(error)
    })
}

/**
 * 处理文件的base64编码 - 备用方案，建议优先使用MinIO
 * @param {File} file - 文件对象
 * @returns {Promise} 包含base64编码的Promise
 */
export function getBase64(file) {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.readAsDataURL(file)
    reader.onload = () => resolve(reader.result)
    reader.onerror = error => reject(error)
  })
}

/**
 * 处理文件上传并返回base64编码 - 备用方案，建议优先使用MinIO
 * @param {File} file - 文件对象
 * @returns {Promise} 包含文件信息和base64编码的Promise
 */
export function processFileToBase64(file) {
  return getBase64(file).then(base64 => {
    return {
      name: file.name,
      url: base64,
      size: file.size,
      type: file.type
    }
  })
}
