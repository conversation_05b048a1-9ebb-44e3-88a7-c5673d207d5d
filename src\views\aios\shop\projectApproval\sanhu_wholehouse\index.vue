<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" :title="crud.status.title" width="1000px" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-document" /> 项目基础信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目编号" prop="projectNumber">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddress" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目类型">
                  <el-input v-model="form.projectTypeLx" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目状态" prop="projectstate">
                  <el-select v-model="form.projectstate" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.projectstate"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <checkbox-field
              v-model="form.typeOfService"
              label="需求内容"
              field-name="typeOfService"
            />
            <!-- <el-row :gutter="30">
                <el-col :span="24">
                  <el-form-item label="需求内容">
                    <el-input v-model="form.typeOfService" type="textarea" :rows="3" />
                  </el-form-item>
                </el-col>
              </el-row> -->

            <el-divider content-position="left"><i class="el-icon-office-building" /> 楼盘信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘ID" prop="loupid">
                  <el-input v-model="form.loupid" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼盘名称">
                  <el-input v-model="form.buildingName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘编号" prop="buildingNumber">
                  <el-input v-model="form.buildingNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参考均价">
                  <el-input v-model="form.referenceAveragePrice">
                    <template slot="append">元/平</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设计楼栋">
                  <el-input v-model="form.designBuilding" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设计楼层">
                  <el-input v-model="form.designFloor" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-user" /> 客户基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称">
                  <el-input v-model="form.nameOfTheCompany" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司性质">
                  <el-select v-model="form.corporateNatureX" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.corporate_nature_x"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户姓名">
                  <el-input v-model="form.corporateNaturename" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="form.gender" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.gender"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="form.contactNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职务">
                  <el-input v-model="form.post" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.mailbox" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="微信">
                  <el-input v-model="form.wechat" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户编号">
                  <el-input v-model="form.customerId" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="家庭成员">
                  <el-input v-model="form.familyMember" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="女主人">
                  <el-input v-model="form.hostess" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="男主人">
                  <el-input v-model="form.host" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="其他成员">
                  <el-input v-model="form.otherMembers" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-house" /> 家庭概况</el-divider>
            <el-row :gutter="30">
              <el-col :span="24">
                <el-form-item label="户型结构">
                  <el-select v-model="form.huxingStructure" filterable placeholder="请选择">
                    <el-option
                      v-for="item in dict.huxing_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <checkbox-field
              v-model="form.memberOfFamily"
              label="家庭成员"
              field-name="memberOfFamily"
            />
            <checkbox-field
              v-model="form.spaceRequirement"
              label="空间需求"
              field-name="spaceRequirement"
            />
            <checkbox-field
              v-model="form.fengge"
              label="风格"
              field-name="fengge"
            />
            <checkbox-field
              v-model="form.abstruse"
              label="玄关"
              field-name="abstruse"
            />
            <checkbox-field
              v-model="form.livingroom"
              label="客厅"
              field-name="livingroom"
            />
            <checkbox-field
              v-model="form.diningroom"
              label="餐厅"
              field-name="diningroom"
            />
            <checkbox-field
              v-model="form.kitchen"
              label="厨房"
              field-name="kitchen"
            />
            <checkbox-field
              v-model="form.puiblicBath"
              label="公共卫生间"
              field-name="puiblicBath"
            />
            <checkbox-field
              v-model="form.bedroom"
              label="主卧"
              field-name="bedroom"
            />
            <checkbox-field
              v-model="form.bathroomZw"
              label="主卧卫生间"
              field-name="bathroomZw"
            />
            <checkbox-field
              v-model="form.secondLie"
              label="次卧"
              field-name="secondLie"
            />
            <checkbox-field
              v-model="form.oldManRoom"
              label="老人房"
              field-name="oldManRoom"
            />
            <checkbox-field
              v-model="form.boyRoom"
              label="男孩房"
              field-name="boyRoom"
            />
            <checkbox-field
              v-model="form.girlRoom"
              label="女孩房"
              field-name="girlRoom"
            />
            <checkbox-field
              v-model="form.balcony"
              label="生活阳台"
              field-name="balcony"
            />

            <el-divider content-position="left"><i class="el-icon-house" /> 材料要求</el-divider>

            <checkbox-field
              v-model="form.surfaceofAWall2"
              label="客餐厅墙面"
              field-name="surfaceofAWall2"
            />
            <checkbox-field
              v-model="form.kctGround"
              label="客餐厅地面"
              field-name="kctGround"
            />
            <checkbox-field
              v-model="form.kctReq"
              label="客餐厅要求"
              field-name="kctReq"
            />
            <checkbox-field
              v-model="form.surfaceofAWall1"
              label="主卧室墙面"
              field-name="surfaceofAWall1"
            />
            <checkbox-field
              v-model="form.liveroomGround"
              label="主卧室地面"
              field-name="liveroomGround"
            />
            <checkbox-field
              v-model="form.materialReq"
              label="产品材质需求"
              field-name="materialReq"
            />
            <checkbox-field
              v-model="form.lightingRequirements"
              label="灯饰要求"
              field-name="lightingRequirements"
            />

            <el-divider content-position="left"><i class="el-icon-s-order" /> 生活方式</el-divider>
            <checkbox-field
              v-model="form.hotWater"
              label="热水方式"
              field-name="hotWater"
            />
            <checkbox-field
              v-model="form.refrigerationMethod"
              label="制冷方式"
              field-name="refrigerationMethod"
            />
            <checkbox-field
              v-model="form.heatingMethod"
              label="制热方式"
              field-name="heatingMethod"
            />
            <checkbox-field
              v-model="form.kitchenAppliances"
              label="厨房电器"
              field-name="kitchenAppliances"
            />
            <checkbox-field
              v-model="form.hostessHobby"
              label="女主人爱好"
              field-name="hostessHobby"
            />
            <checkbox-field
              v-model="form.maleMasterHobby"
              label="男主人爱好"
              field-name="maleMasterHobby"
            />
            <checkbox-field
              v-model="form.otherHobbies"
              label="其他成员爱好"
              field-name="otherHobbies"
            />

            <el-divider content-position="left"><i class="el-icon-money" /> 预算与时间</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="硬装预算">
                  <el-input v-model="form.hardPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="软装预算">
                  <el-input v-model="form.softOutfitBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="固装预算">
                  <el-input v-model="form.fixedPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期" prop="projectDeliveryPeriod">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="硬装完成时间">
                  <el-date-picker v-model="form.completionTimeYz" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="固装需完成时间">
                  <el-date-picker v-model="form.xqtime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划入住日期">
                  <el-date-picker v-model="form.jihua" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-info" /> 其他信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请日期">
                  <el-date-picker v-model="form.applydt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作时间">
                  <el-date-picker v-model="form.optdt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作人">
                  <el-input v-model="form.optname" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-picture" /> 参考意向图片</el-divider>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="客户提供参考图片">
                  <div class="image-upload-container">
                    <general-file-upload
                      v-model="form.intentionPictures"
                      :field-name="'intentionPictures'"
                      v-bind="fileUploadConfig"
                      :use-minio-delete="true"
                      :hide-remove="isViewMode"
                      @change="handleFileChange('intentionPictures', $event)"
                      @file-change="handleFileListChange"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="tabDate" label="制表日期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiOaQuanwu:edit','tumaiOaQuanwu:del','tumaiOaQuanwu:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaQuanwu from '@/api/aios/shop/tumaiOaQuanwu'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { mapGetters } from 'vuex'
import CheckboxField from '@/components/CheckBox/CheckboxField'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, intentionalImages: null, comid: null, oddNumbers: null, projectNumber: null, projecttypemultiplechoicesareallowed: null, nameOfTheCompany: null, corporateNaturename: null, contactNumber: null, post: null, mailbox: null, gender: null, familyMember: null, porch: null, sittingRoom: null, restaurant: null, kitchen: null, bathhouse: null, balcony: null, ground: null, hostess: null, host: null, intentionPictures: null, projectStatus: null, projectstate: null, remarks: null, uid: null, typeOfService: null, corporateName: null, name: null, email: null, customerName: null, corporateNatureX: null, projectName: null, projectAddress: null, projectNature: null, projectOverview: null, buildingName: null, referenceAveragePrice: null, wechat: null, huxingStructure: null, projectTypeLx: null, buildingNumber: null, surfaceofAWall2: null, surfaceofAWall1: null, loupid: null, projectDeliveryPeriod: null, customerId: null, thenumberOfFamily: null, totalNumberHx: null, livingRoomHeight: null, roomHeight: null, buildingStructure: null, designBuilding: null, designFloor: null, setOfArea: null, areaOfStructure: null, completionTimeYz: null, jihua: null, xqtime: null, hardPackBudget: null, softOutfitBudget: null, fixedPackBudget: null, spaceRequirement: null, bedroom: null, bathroomZw: null, secondLie: null, oldManRoom: null, boyRoom: null, girlRoom: null, kctReq: null, lightingRequirements: null, hotWater: null, refrigerationMethod: null, heatingMethod: null, kitchenAppliances: null, otherMembers: null, area: null, tabDate: null, materialReq: null, fengge: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, createtime: null, isturn: null, kctGround: null, liveroomGround: null }
export default {
  name: 'TumaiOaQuanwu',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, CheckboxField, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), GeneralFileHandler.mixin, subformFileHandlerMixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['gender', 'projectstate', 'corporate_nature_x', 'project_nature', 'huxing_structure', 'building_structure', 'status'],
  cruds() {
    return CRUD({ title: '项目立项（适用散户全屋定制）', url: 'api/shop/tumaiOaQuanwu', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaQuanwu }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaQuanwu:add'],
        edit: ['admin', 'tumaiOaQuanwu:edit'],
        del: ['admin', 'tumaiOaQuanwu:del'],
        view: ['admin', 'tumaiOaQuanwu:edit', 'tumaiOaQuanwu:view']
      },
      // 图片上传相关
      dialogVisible: false,
      dialogImageUrl: '',
      // 文件字段列表，用于初始化和提交前处理
      fileFields: [
        'intentionPictures'
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 10,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        fengge: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '项目编号不能为空', trigger: 'blur' }
        ],
        projectstate: [
          { required: true, message: '项目状态不能为空', trigger: 'blur' }
        ],
        buildingNumber: [
          { required: true, message: '楼盘编号不能为空', trigger: 'blur' }
        ],
        loupid: [
          { required: true, message: '楼盘id不能为空', trigger: 'blur' },
          {
            pattern: /^\d+$/, // 使用正则表达式，确保只能输入数字
            message: '楼盘id只能为数字',
            trigger: 'blur'
          }
        ],
        projectDeliveryPeriod: [
          { required: true, message: '项目交期不能为空', trigger: 'blur' }
        ],
        areaOfStructure: [
          { required: true, message: '建筑面积㎡不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 获取并初始化文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['intentionPictures'])
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化文件字段为空数组
      this.form.intentionPictures = '[]'
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始表单数据:', JSON.stringify({
          intentionPictures: this.form.intentionPictures
        }))

        // 使用通用方法处理文件字段
        this.prepareGeneralFileFields(this.fileFields)

        console.log('处理附件后:', JSON.stringify({
          intentionPictures: this.form.intentionPictures
        }))

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    },

    // 添加created钩子初始化文件字段
    created() {
      // 初始化文件字段
      this.initGeneralFileFields(this.fileFields)

      // 检查minioDeleteApi是否可用
      if (!this.minioDeleteApi) {
        console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
      }
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01);
}

.image-upload-container {
  display: flex;
  margin-bottom: 5px;
}

.image-upload-container .el-form-item {
  width: 100%;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background: #fff;
}

.el-tabs__item {
  font-size: 15px;
}

.el-tabs--border-card > .el-tabs__content {
  padding: 15px;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
