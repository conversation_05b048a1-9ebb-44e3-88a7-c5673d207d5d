<template>
  <div class="content-left">
    <div class="total">
      <div class="tit-bar">
        <span>{{ left.leftTableOne.title }}</span>
        <img src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png" alt="">
      </div>

      <div class="total-pot">
        <div
          v-for="(item, index) in left.leftTableOne.countryStats"
          :key="item.id"
          :class="[
            // 'total-item',
            `total-item${index + 1}`,
            // classMapping[item.type],
            // { 'active-item': activeIndex === index }
          ]"
        >
          <p class="total-unit">{{ item.name }}</p>
          <p class="total-quantity">
            {{ formattedValue(item.value) }}
          </p>
        </div>
      </div>
    </div>

    <div class="total">
      <div class="tit-bar">
        <span>{{ left.leftTableTwo.title }}</span>
        <img id="tit_spot" src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png" alt="">
      </div>
      <div ref="increase" style="width: 340px;height:200px" class="total-pot">&nbsp;</div>
    </div>

    <div class="total">
      <div class="tit-bar">
        <span>{{ left.leftTableThree.title }}</span>
        <img id="tit_spot" src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png" alt="">
      </div>
      <div ref="ranking" style="width: 340px;height:460px;" class="total-pot">&nbsp;</div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core'
export default {
  props: {
    left: {
      type: Object,
      default: () => ({})
    }
  },
  watch: {
    // 监听 left 变化
    left: {
      handler(newVal) {
        if (
          newVal.leftTableOne.countryStats &&
          newVal.leftTableTwo.incCountryData &&
          newVal.leftTableThree.YAxisRankRight
        ) {
          this.initChart() // 所有数据就绪后渲染图表
        }
      },
      immediate: true, // 立即触发一次（处理父组件数据已提前到达的情况）
      deep: true // 深度监听（如果数据结构嵌套较深）
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.increase) {
      this.increase.dispose()
      this.increase = null
    }
    if (this.ranking) {
      this.ranking.dispose()
      this.ranking = null
    }
  },
  methods: {
    initChart() {
      this.increase = echarts.init(this.$refs.increase)
      this.ranking = echarts.init(this.$refs.ranking)
      const option = {
        tooltip: {
          trigger: 'axis',
          borderWidth: 1,
          borderColor: '#2B6A8F',
          backgroundColor: '#182430',
          axisPointer: {
            type: 'shadow'
          },
          formatter(params) {
            var result = ''
            for (var i = 0; i < params.length; i++) {
              result +=
                '<span style="color:rgba(221, 232, 235, 1)">' +
                params[i].name +
                ' : </span>'
              result +=
                '<span style="color:rgba(255, 255, 255, 1)">' +
                params[i].data +
                '</span>'
            }
            return result
          }
        },
        grid: {
          top: '10%',
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },

        xAxis: {
          type: 'category',
          data: this.left.leftTableTwo.incCountryData,
          axisLabel: {
            color: '#DDE8EB'
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 400, // 调整统计图纵轴上限
          splitLine: {
            lineStyle: {
              type: 'dotted',
              color: 'rgba(94,109,110,0.4)'
            }
          },
          axisLabel: {
            color: '#DDE8EB'
          }
        },
        series: [
          {
            name: '',
            type: 'bar',
            barWidth: '30%',
            data: this.left.leftTableTwo.incNumData,
            itemStyle: {
              normal: {
                color() {
                  return new echarts.graphic.LinearGradient(
                    0,
                    0,
                    0,
                    1,
                    [
                      { offset: 0, color: '#9ECCF5' },
                      { offset: 1, color: '#122434' }
                    ],
                    false
                  )
                }
              }
            }
          }
        ]
      }
      const option1 = {
        grid: {
          top: '6%',
          left: '2%',
          right: '2%',
          bottom: '2%',
          containLabel: true
        },
        dataZoom: [
          {
            show: false,
            type: 'slider',
            yAxisIndex: [0, 1],
            showDetail: false,
            width: 8,
            height: '100%',
            endValue: 8,
            zoomLoxk: false
          },
          {
            type: 'inside',
            yAxisIndex: [0, 1],
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true
          }
        ],
        xAxis: {
          type: 'value',
          min: 0,
          max: 30000,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        yAxis: [
          {
            inverse: true,
            data: this.left.leftTableThree.YAxisRankLeft,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              // 控制坐标轴标签
              show: true,
              inside: true,
              interval: 0, // 横轴信息全部显
              splitNumber: 50,
              textStyle: {
                color: '#BED0DB',
                verticalAlign: 'bottom',
                fontSize: 13,
                align: 'left',
                padding: [0, 0, 10, 55]
              }
            }
          },
          {
            inverse: true,
            data: this.left.leftTableThree.YAxisRankRight,
            axisLabel: {
              inside: true,
              textStyle: {
                color: '#6BE4FF',
                verticalAlign: 'bottom',
                fontSize: 13,
                align: 'left',
                padding: [0, 0, 10, -310]
              }
            },
            offset: 0,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            // 辅助系列
            type: 'bar',
            barGap: '-100%',
            silent: true,
            itemStyle: {
              color: 'rgba(255, 255, 254, 0.2)'
            },
            barWidth: 13,
            data: [
              30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000,
              30000, 30000, 30000, 30000
            ]
          },

          {
            name: '',
            type: 'bar',
            barWidth: '27%',
            data: this.left.leftTableThree.YAxisRankData,
            label: {
              position: [10, 10],
              normal: {
                position: [280, -18],
                show: true,
                textStyle: {
                  color: '#DAECF6',
                  fontSize: 16
                }
              }
            },

            itemStyle: {
              normal: {
                color() {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [
                      { offset: 0, color: '#9ECCF5' },
                      { offset: 1, color: '#122434' }
                    ],
                    false
                  )
                }
              }
            }
          }
        ]
      }
      this.increase.setOption(option)
      this.ranking.setOption(option1)
    },
    handleResize() {
      if (this.increase) {
        this.increase.resize()
      }
    },
    formattedValue(val) {
      return val.toLocaleString()
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "@/assets/styles/bi.scss";

.content-left{
  float: left;
  margin-top: 10px;
}
</style>
