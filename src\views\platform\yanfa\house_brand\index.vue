<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">国别（国家）</label>
        <el-input v-model="query.country" clearable placeholder="国别（国家）" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">联系人姓名</label>
        <el-input v-model="query.contactName" clearable placeholder="联系人姓名" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">联系人手机</label>
        <el-input v-model="query.contactMobile" clearable placeholder="联系人手机" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">公司名称</label>
        <el-input v-model="query.companyName" clearable placeholder="公司名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">详细地址</label>
        <el-input v-model="query.detailAddr" clearable placeholder="详细地址" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别（国家）">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻时间">
            <el-date-picker v-model="form.enterDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="角色">
            <el-input v-model="form.role" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="联系人姓名">
            <el-input v-model="form.contactName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="联系人手机">
            <el-input v-model="form.contactMobile" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="推荐人1">
            <el-input v-model="form.referrerOne" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="推荐人2">
            <el-input v-model="form.referrerTwo" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司名称">
            <el-input v-model="form.companyName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="详细地址">
            <el-input v-model="form.detailAddr" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司地址（省市区">
            <el-input v-model="form.companyAddr" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="成立日期">
            <el-date-picker v-model="form.establishDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="营业期限（开始时间）">
            <el-date-picker v-model="form.operateStartDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="营业期限（结束时间）">
            <el-date-picker v-model="form.operateEndDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="注册资本（万元）">
            <el-input v-model="form.registedCapital" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="经营范围">
            <el-input v-model="form.operateScope" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="店铺名称">
            <el-input v-model="form.shopName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="纳税人类型">
            <el-input v-model="form.taxpayType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="经营类目">
            <el-input v-model="form.operateType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="银行开户名">
            <el-input v-model="form.bankAccountName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="对公结算银行账号">
            <el-input v-model="form.officialSettleAccount" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开户银行名称">
            <el-input v-model="form.bankName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开户银行支行所在地（省市区）">
            <el-input v-model="form.bankAddr" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别（国家）" />
        <el-table-column prop="enterDate" label="入驻时间" />
        <el-table-column prop="role" label="角色" />
        <el-table-column prop="contactName" label="联系人姓名" />
        <el-table-column prop="contactMobile" label="联系人手机" />
        <el-table-column prop="referrerOne" label="推荐人1" />
        <el-table-column prop="referrerTwo" label="推荐人2" />
        <el-table-column prop="companyName" label="公司名称" />
        <el-table-column prop="detailAddr" label="详细地址" />
        <el-table-column prop="companyAddr" label="公司地址（省市区" />
        <el-table-column prop="establishDate" label="成立日期" />
        <el-table-column prop="operateStartDate" label="营业期限（开始时间）" />
        <el-table-column prop="operateEndDate" label="营业期限（结束时间）" />
        <el-table-column prop="registedCapital" label="注册资本（万元）" />
        <el-table-column prop="operateScope" label="经营范围" />
        <el-table-column prop="shopName" label="店铺名称" />
        <el-table-column prop="taxpay type" label="纳税人类型" />
        <el-table-column prop="operateType" label="经营类目" />
        <el-table-column prop="bankAccountName" label="银行开户名" />
        <el-table-column prop="officialSettleAccount" label="对公结算银行账号" />
        <el-table-column prop="bankName" label="开户银行名称" />
        <el-table-column prop="bankAddr" label="开户银行支行所在地（省市区）" />
        <el-table-column v-if="checkPer(['admin','yanfaHouseBrandSummary:edit','yanfaHouseBrandSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudYanfaHouseBrandSummary from '@/api/yanfa/yanfaHouseBrandSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, enterDate: null, role: null, contactName: null, contactMobile: null, referrerOne: null, referrerTwo: null, companyName: null, detailAddr: null, companyAddr: null, establishDate: null, operateStartDate: null, operateEndDate: null, registedCapital: null, operateScope: null, shopName: null, taxpayType: null, operateType: null, bankAccountName: null, officialSettleAccount: null, bankName: null, bankAddr: null }
export default {
  name: 'YanfaHouseBrandSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '全屋定制品牌商汇总', url: 'api/yanfaHouseBrandSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudYanfaHouseBrandSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'yanfaHouseBrandSummary:add'],
        edit: ['admin', 'yanfaHouseBrandSummary:edit'],
        del: ['admin', 'yanfaHouseBrandSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别（国家）' },
        { key: 'contactName', display_name: '联系人姓名' },
        { key: 'contactMobile', display_name: '联系人手机' },
        { key: 'companyName', display_name: '公司名称' },
        { key: 'detailAddr', display_name: '详细地址' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
