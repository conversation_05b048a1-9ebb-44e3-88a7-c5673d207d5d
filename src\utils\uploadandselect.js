import axios from 'axios'

/**
 * 文件上传和管理工具类
 * 用于与后端API交互，实现文件上传、删除和查询等功能
 */

/**
 * 上传文件到服务器
 * @param {File} file - 要上传的文件对象
 * @returns {Promise} 包含上传结果的Promise
 */
export const uploadFile = async(file) => {
  if (!file) {
    return Promise.reject('文件不能为空')
  }

  const formData = new FormData()
  formData.append('file', file)

  try {
    const response = await axios.post('/api/file/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    return response.data
  } catch (error) {
    console.error('文件上传失败:', error)
    return Promise.reject(
      (error.response && error.response.data) || '文件上传失败'
    )
  }
}

/**
 * 删除服务器上的文件
 * @param {string} fileName - 文件名称或文件路径
 * @returns {Promise} 包含删除结果的Promise
 */
export const deleteFile = async(fileName) => {
  if (!fileName) {
    return Promise.reject('文件名不能为空')
  }

  try {
    const response = await axios.delete('/api/file/delete', {
      params: { fileName }
    })
    return response.data
  } catch (error) {
    console.error('文件删除失败:', error)
    return Promise.reject(
      (error.response && error.response.data) || '文件删除失败'
    )
  }
}

/**
 * 获取文件详细信息
 * @param {string} filePath - 文件路径
 * @returns {Promise} 包含文件信息的Promise
 */
export const getFileInfo = async(filePath) => {
  if (!filePath) {
    return Promise.reject('文件路径不能为空')
  }

  try {
    const response = await axios.get('/api/file/info', {
      params: { filePath }
    })
    return response.data
  } catch (error) {
    console.error('获取文件信息失败:', error)
    return Promise.reject(
      (error.response && error.response.data) || '获取文件信息失败'
    )
  }
}

/**
 * 根据日期查询文件列表
 * @param {string} date - 日期，格式：yyyyMMdd，如果为空则查询当天
 * @returns {Promise} 包含文件列表的Promise
 */
export const listFilesByDate = async(date) => {
  try {
    const response = await axios.get('/api/file/list', {
      params: { date }
    })
    return response.data
  } catch (error) {
    console.error('查询文件列表失败:', error)
    return Promise.reject(
      (error.response && error.response.data) || '查询文件列表失败'
    )
  }
}

/**
 * 根据文件名称查询文件（支持模糊查询）
 * @param {string} fileName - 文件名称
 * @returns {Promise} 包含查询结果的Promise
 */
export const searchFilesByName = async(fileName) => {
  if (!fileName) {
    return Promise.reject('文件名不能为空')
  }

  try {
    const response = await axios.get('/api/file/search', {
      params: { fileName }
    })
    return response.data
  } catch (error) {
    console.error('根据文件名查询失败:', error)
    return Promise.reject(
      (error.response && error.response.data) || '根据文件名查询失败'
    )
  }
}

/**
 * 文件上传组件辅助函数
 */

/**
 * 获取文件大小的可读形式
 * @param {number} size - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
export const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

/**
 * 检查文件类型是否为图片
 * @param {string} fileType - 文件MIME类型
 * @returns {boolean} 是否为图片
 */
export const isImageFile = (fileType) => {
  return fileType && fileType.startsWith('image/')
}

/**
 * 检查文件类型是否为视频
 * @param {string} fileType - 文件MIME类型
 * @returns {boolean} 是否为视频
 */
export const isVideoFile = (fileType) => {
  return fileType && fileType.startsWith('video/')
}

/**
 * 获取今天的日期，格式为yyyyMMdd
 * @returns {string} 格式化的日期字符串
 */
export const getTodayDateString = () => {
  const today = new Date()
  const year = today.getFullYear()
  const month = String(today.getMonth() + 1).padStart(2, '0')
  const day = String(today.getDate()).padStart(2, '0')
  return `${year}${month}${day}`
}

/**
 * 根据文件名获取文件扩展名
 * @param {string} fileName - 文件名
 * @returns {string} 文件扩展名
 */
export const getFileExtension = (fileName) => {
  return fileName.slice(((fileName.lastIndexOf('.') - 1) >>> 0) + 2)
}

/**
 * 根据文件扩展名获取文件图标
 * @param {string} fileName - 文件名
 * @returns {string} 文件图标类名或路径
 */
export const getFileIcon = (fileName) => {
  const extension = getFileExtension(fileName).toLowerCase()

  // 可以根据实际情况扩展更多类型
  const iconMap = {
    'pdf': 'icon-pdf',
    'doc': 'icon-word',
    'docx': 'icon-word',
    'xls': 'icon-excel',
    'xlsx': 'icon-excel',
    'ppt': 'icon-ppt',
    'pptx': 'icon-ppt',
    'jpg': 'icon-image',
    'jpeg': 'icon-image',
    'png': 'icon-image',
    'gif': 'icon-image',
    'mp4': 'icon-video',
    'mp3': 'icon-audio',
    'zip': 'icon-zip',
    'rar': 'icon-zip',
    'txt': 'icon-text'
  }

  return iconMap[extension] || 'icon-file'
}

/**
 * 使用示例：
 *
 * // 引入工具函数
 * import { uploadFile, deleteFile, listFilesByDate, searchFilesByName, formatFileSize } from '@/utils/uploadandselect';
 *
 * // 上传文件示例
 * const handleFileUpload = async (event) => {
 *   const file = event.target.files[0];
 *   if (file) {
 *     try {
 *       const result = await uploadFile(file);
 *       console.log('上传成功:', result);
 *       // 文件URL: result.url
 *       // 文件名: result.fileName
 *     } catch (error) {
 *       console.error('上传失败:', error);
 *     }
 *   }
 * };
 *
 * // 删除文件示例
 * const handleFileDelete = async (fileUrl) => {
 *   try {
 *     await deleteFile(fileUrl);
 *     console.log('文件删除成功');
 *   } catch (error) {
 *     console.error('删除失败:', error);
 *   }
 * };
 *
 * // 查询今日文件列表示例
 * const fetchTodayFiles = async () => {
 *   try {
 *     const today = getTodayDateString();
 *     const result = await listFilesByDate(today);
 *     console.log('今日文件列表:', result.files);
 *     console.log('文件总数:', result.total);
 *   } catch (error) {
 *     console.error('获取文件列表失败:', error);
 *   }
 * };
 *
 * // 搜索文件示例
 * const searchFiles = async (keyword) => {
 *   try {
 *     const result = await searchFilesByName(keyword);
 *     console.log('搜索结果:', result.files);
 *     console.log('匹配文件数:', result.total);
 *   } catch (error) {
 *     console.error('搜索文件失败:', error);
 *   }
 * };
 */
