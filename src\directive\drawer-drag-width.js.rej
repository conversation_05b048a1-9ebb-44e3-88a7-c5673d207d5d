diff a/src/directive/drawer-drag-width.js b/src/directive/drawer-drag-width.js	(rejected hunks)
@@ -4,2 +4,2 @@ const drawerDragWidth = {
-    const drawerEle = el.querySelector('.ai-chat-container') || el;
-    
+    const drawerEle = el.querySelector('.ai-chat-container') || el
+
@@ -16,2 +16,2 @@ const drawerDragWidth = {
-      document.body.style.userSelect = 'none';
-      
+      document.body.style.userSelect = 'none'
+
@@ -24,4 +24,4 @@ const drawerDragWidth = {
-        realWidth = realWidth > width80 ? width80 : realWidth < width30 ? width30 : realWidth;
-        drawerEle.style.width = realWidth + 'px';
-      };
-      
+        realWidth = realWidth > width80 ? width80 : realWidth < width30 ? width30 : realWidth
+        drawerEle.style.width = realWidth + 'px'
+      }
+
