<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :title="crud.status.title" width="1000px" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <checkbox-field
              v-model="form.typeOfService"
              label="需求内容"
              field-name="typeOfService"
            />
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目类型" prop="projectTypeLx">
                  <el-select v-model="form.projectTypeLx" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_type_lx"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目状态" prop="projectstate">
                  <el-select v-model="form.projectstate" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.projectstate"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目交期" prop="projectDeliveryPeriod">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目编号" prop="projectNumber">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目地址" prop="projectAddress">
                  <el-input v-model="form.projectAddress" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目性质" prop="projectNature">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="24">
                <el-form-item label="项目概况" prop="projectOverview">
                  <el-input v-model="form.projectOverview" :rows="3" type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-office-building" /> 楼盘信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘id" prop="loupid">
                  <el-input v-model="form.loupid" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼盘名称" prop="buildingName">
                  <el-input v-model="form.buildingName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘编号" prop="buildingNumber">
                  <el-input v-model="form.buildingNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参考均价 (元/平)" prop="referenceAveragePrice">
                  <el-input v-model="form.referenceAveragePrice">
                    <template slot="append">元/平</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设计楼栋">
                  <el-input v-model="form.designBuilding" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设计楼层">
                  <el-input v-model="form.designFloor" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="户型结构">
                  <el-select v-model="form.huxingStructure" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.huxing_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑结构">
                  <el-select v-model="form.buildingStructure" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.building_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="户型数（个）">
                  <el-input v-model="form.thenumberOfFamily" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="户型总数（户）">
                  <el-input v-model="form.totalNumberHx" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑面积㎡">
                  <el-input v-model="form.areaOfStructure">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider content-position="left"><i class="el-icon-house" /> 户型信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="层高(整体层高吊完顶)m">
                  <el-input v-model="form.floorHeight1">
                    <template slot="append">m</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="层高(客餐厅中间位置)m">
                  <el-input v-model="form.floorHeight2">
                    <template slot="append">m</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="层高(次卧层高)m">
                  <el-input v-model="form.floorHeight3">
                    <template slot="append">m</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-money" /> 预算信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="硬装预算(元)">
                  <el-input v-model="form.hardPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="软装预算(元)" prop="softOutfitBudget">
                  <el-input v-model="form.softOutfitBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="固装预算(元)">
                  <el-input v-model="form.fixedPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="设计费预算(元)">
                  <el-input v-model="form.designFeeBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目总预算(元)">
                  <el-input v-model="form.generalBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-date" /> 日期信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开盘日期">
                  <el-date-picker v-model="form.openingDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="交房日期">
                  <el-date-picker v-model="form.deliveryDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">

              <el-col :span="12">
                <el-form-item label="预计装修开始日期">
                  <el-date-picker v-model="form.startDateZx" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预计装修完成日期">
                  <el-date-picker v-model="form.completionDateZx" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划入住日期">
                  <el-date-picker v-model="form.jihua" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="概念方案需求日期">
                  <el-date-picker v-model="form.conceptplanrequirementdate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="深化方案需求日期">
                  <el-date-picker v-model="form.shrequirementdate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请日期">
                  <el-date-picker v-model="form.applydt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-date" /> 家庭概况</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属行业(男主)">
                  <el-input v-model="form.industryInvolved2" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职业(男主)">
                  <el-input v-model="form.occupation2" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属行业(女主)">
                  <el-input v-model="form.industryInvolved1" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职业(女主)">
                  <el-input v-model="form.occupation1" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider content-position="left"><i class="el-icon-s-home" /> 家庭成员</el-divider>
            <checkbox-field
              v-model="form.numberElders"
              label="长辈人数"
              field-name="numberElders"
            />
            <checkbox-field
              v-model="form.elderAge"
              label="长辈年龄"
              field-name="elderAge"
            />
            <checkbox-field
              v-model="form.husbandandWifeAge"
              label="夫妻年龄"
              field-name="husbandandWifeAge"
            />
            <el-row :gutter="20">
              <el-col :span="12">
                <checkbox-field
                  v-model="form.numberBoys"
                  label="男孩人数"
                  field-name="numberBoys"
                />
              </el-col>
              <el-col :span="12">
                <checkbox-field
                  v-model="form.boyAge"
                  label="男孩年龄"
                  field-name="boyAge"
                />
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <checkbox-field
                  v-model="form.numberGirls"
                  label="女孩人数"
                  field-name="numberGirls"
                />
              </el-col>
              <el-col :span="12">
                <checkbox-field
                  v-model="form.girlAge"
                  label="女孩年龄"
                  field-name="girlAge"
                />
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <checkbox-field
                  v-model="form.livingNanny"
                  label="住家保姆"
                  field-name="livingNanny"
                />
              </el-col>
              <el-col :span="12">
                <checkbox-field
                  v-model="form.livingDriver"
                  label="住家司机"
                  field-name="livingDriver"
                />
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <checkbox-field
                  v-model="form.petCat"
                  prop="petCat"
                  label="宠物猫"
                  field-name="petCat"
                />
              </el-col>
              <el-col :span="12">
                <checkbox-field
                  v-model="form.petDog"
                  prop="petCat"
                  label="宠物狗"
                  field-name="petDog"
                />
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-s-home" /> 空间需求</el-divider>
            <checkbox-field
              v-model="form.bedroomJz"
              prop="bedroomJz"
              label="卧室"
              field-name="bedroomJz"
            />
            <el-row :gutter="20">
              <el-col :span="10">
                <checkbox-field
                  v-model="form.childrensRoom"
                  prop="childrensRoom"
                  label="儿童房"
                  field-name="childrensRoom"
                />
              </el-col>
              <el-col :span="14">
                <checkbox-field
                  v-model="form.study"
                  prop="study"
                  label="书房"
                  field-name="study"
                />
              </el-col>
            </el-row>
            <checkbox-field
              v-model="form.cloakroom"
              label="衣帽间"
              prop="cloakroom"
              field-name="cloakroom"
            />
            <checkbox-field
              v-model="form.kitchenJz"
              prop="kitchenJz"
              label="厨房餐厅"
              field-name="kitchenJz"
            />
            <checkbox-field
              v-model="form.toilet"
              prop="toilet"
              label="卫生间"
              field-name="toilet"
            />
            <checkbox-field
              v-model="form.otherRooms"
              label="其他房间"
              prop="otherRooms"
              field-name="otherRooms"
            />
            <checkbox-field
              v-model="form.functionalSpace"
              label="功能空间"
              prop="functionalSpace"
              field-name="functionalSpace"
            />

            <el-divider content-position="left"><i class="el-icon-s-home" /> 软装材料需求</el-divider>
            <checkbox-field
              v-model="form.overallTone"
              label="整体色调"
              prop="overallTone"
              field-name="overallTone"
            />
            <checkbox-field
              v-model="form.lighting"
              label="灯光要求"
              prop="lighting"
              field-name="lighting"
            />
            <checkbox-field
              v-model="form.lightingRequirementsJz"
              label="灯饰要求"
              field-name="lightingRequirementsJz"
            />
            <checkbox-field
              v-model="form.furnitureReq"
              label="家具要求"
              field-name="furnitureReq"
            />
            <checkbox-field
              v-model="form.curtainShadingReq"
              label="窗帘遮光要求"
              field-name="curtainShadingReq"
            />

            <el-divider content-position="left"><i class="el-icon-s-home" /> 设计要求</el-divider>
            <el-row :gutter="20">
              <el-col :span="9">
                <el-form-item label="是否指定设计师">
                  <el-radio v-for="item in dict.if_designer" :key="item.id" v-model="form.designerDesignated" :label="item.value">{{ item.label }}</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="15">
                <el-form-item label="指定设计师姓名">
                  <el-input v-model="form.designerName" />
                </el-form-item>
              </el-col>
            </el-row>
            <checkbox-field
              v-model="form.fengge"
              label="风格"
              field-name="fengge"
            />
            <checkbox-field
              v-model="form.renderingsSpace"
              label="做效果图空间"
              field-name="renderingsSpace"
            />
            <checkbox-field
              v-model="form.dominantHue"
              label="主色调"
              field-name="dominantHue"
            />
            <checkbox-field
              v-model="form.auxiliaryTone"
              label="辅助色调"
              field-name="auxiliaryTone"
            />
            <checkbox-field
              v-model="form.hateColor"
              label="讨厌的颜色"
              field-name="hateColor"
            />
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="设计要求及要点">
                  <el-input v-model="form.designReq" :rows="1" type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="其他要求">
                  <el-input v-model="form.otherNeeds" :rows="3" type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-s-home" /> 精神需求</el-divider>
            <checkbox-field
              v-model="form.hostessHobby"
              label="女主人爱好"
              field-name="hostessHobby"
            />
            <checkbox-field
              v-model="form.maleMasterHobby"
              label="男主人爱好"
              field-name="maleMasterHobby"
            />

            <el-divider content-position="left"><i class="el-icon-user" /> 客户基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户名称" prop="customerName">
                  <el-input v-model="form.customerName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别" prop="gender">
                  <el-select v-model="form.gender" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.gender"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="form.contactNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司性质" prop="corporateNatureX">
                  <el-select v-model="form.corporateNatureX" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.corporate_nature_x"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="微信号">
                  <el-input v-model="form.wechat" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.email" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-s-home" /> 附件上传</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑CAD图纸">
                  <file-upload
                    :field-value.sync="form.cadDrawingsJz"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑电气CAD图纸">
                  <file-upload
                    :field-value.sync="form.cadDrawingsDq"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑消防CAD图纸">
                  <file-upload
                    :field-value.sync="form.cadDrawingsXf"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-picture-outline" /> 效果图片</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="意向图片">
                  <general-file-upload
                    v-model="form.intentionalImages"
                    :field-name="'intentionalImages'"
                    v-bind="fileUploadConfig"
                    :use-minio-delete="true"
                    :hide-remove="isViewMode"
                    @change="handleFileChange('intentionalImages', $event)"
                    @file-change="handleFileListChange"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="装修已完成图片">
                  <general-file-upload
                    v-model="form.decorationPicture"
                    :field-name="'decorationPicture'"
                    v-bind="fileUploadConfig"
                    :use-minio-delete="true"
                    :hide-remove="isViewMode"
                    @change="handleFileChange('decorationPicture', $event)"
                    @file-change="handleFileListChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="tabDate" label="制表日期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiOaJinzhuang:edit','tumaiOaJinzhuang:del','tumaiOaJinzhuang:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaJinzhuang from '@/api/aios/shop/tumaiOaJinzhuang'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import CheckboxField from '@/components/CheckBox/CheckboxField'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, projectNumber: null, projectName: null, projectType: null, typeOfService: null, areaOfStructure: null, floorHeight1: null, floorHeight2: null, floorHeight3: null, buildingStructure: null, industryInvolved1: null, mailbox: null, memberOfFamily: null, bedroomJz: null, study: null, cloakroom: null, kitchenJz: null, toilet: null, style: null, industryInvolved2: null, projectstate: null, uid: null, wechat: null, email: null, projectAddress: null, projectNature: null, projectOverview: null, buildingName: null, referenceAveragePrice: null, customerId: null, customerName: null, contactNumber: null, projectTypeLx: null, buildingNumber: null, thenumberOfFamily: null, huxingStructure: null, loupid: null, projectDeliveryPeriod: null, wechatName: null, fengge: null, designBuilding: null, totalNumberHx: null, designFloor: null, occupation1: null, occupation2: null, whichresidenceIsIt: null, permanentCz: null, residenceYears: null, numberElders: null, elderAge: null, husbandandWifeAge: null, numberBoys: null, boyAge: null, numberGirls: null, girlAge: null, livingNanny: null, livingDriver: null, petCat: null, petDog: null, childrensRoom: null, functionalSpace: null, otherRooms: null, overallTone: null, lighting: null, lightingRequirementsJz: null, furnitureReq: null, curtainShadingReq: null, hostessHobby: null, maleMasterHobby: null, cadDrawingsJz: null, cadDrawingsDq: null, cadDrawingsXf: null, openingDate: null, deliveryDate: null, startDateZx: null, completionDateZx: null, jihua: null, hardPackBudget: null, softOutfitBudget: null, fixedPackBudget: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null, tabDate: null, gender: null, conceptplanrequirementdate: null, shrequirementdate: null, designFeeBudget: null, generalBudget: null, corporateNatureX: null, designerDesignated: null, designerName: null, renderingsSpace: null, dominantHue: null, auxiliaryTone: null, hateColor: null, designReq: null, otherNeeds: null, intentionalImages: null, createtime: null, decorationPicture: null }
export default {
  name: 'TumaiOaJinzhuang',
  components: { pagination, crudOperation, rrOperation, FileUpload, GeneralFileUpload, CheckboxField, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaJinzhuang:add'],
        edit: ['admin', 'tumaiOaJinzhuang:edit'],
        del: ['admin', 'tumaiOaJinzhuang:del'],
        view: ['admin', 'tumaiOaJinzhuang:edit', 'tumaiOaJinzhuang:view']
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '项目编号不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        bedroomJz: [
          { required: true, message: '卧室不能为空', trigger: 'blur' }
        ],
        study: [
          { required: true, message: '书房不能为空', trigger: 'blur' }
        ],
        cloakroom: [
          { required: true, message: '衣帽间不能为空', trigger: 'blur' }
        ],
        kitchenJz: [
          { required: true, message: '厨房餐厅不能为空', trigger: 'blur' }
        ],
        toilet: [
          { required: true, message: '卫生间不能为空', trigger: 'blur' }
        ],
        style: [
          { required: true, message: '风格不能为空', trigger: 'blur' }
        ],
        projectstate: [
          { required: true, message: '项目状态不能为空', trigger: 'blur' }
        ],
        projectAddress: [
          { required: true, message: '项目地址不能为空', trigger: 'blur' }
        ],
        projectNature: [
          { required: true, message: '项目性质不能为空', trigger: 'blur' }
        ],
        projectOverview: [
          { required: true, message: '项目概况不能为空', trigger: 'blur' }
        ],
        buildingName: [
          { required: true, message: '楼盘名称不能为空', trigger: 'blur' }
        ],
        referenceAveragePrice: [
          { required: true, message: '参考均价 (元/平)不能为空', trigger: 'blur' }
        ],
        customerName: [
          { required: true, message: '客户名称不能为空', trigger: 'blur' }
        ],
        projectTypeLx: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ],
        buildingNumber: [
          { required: true, message: '楼盘编号不能为空', trigger: 'blur' }
        ],
        huxingStructure: [
          { required: true, message: '户型结构不能为空', trigger: 'blur' }
        ],
        loupid: [
          { required: true, message: '楼盘id不能为空', trigger: 'blur' }
        ],
        projectDeliveryPeriod: [
          { required: true, message: '项目交期不能为空', trigger: 'blur' }
        ],
        petCat: [
          { required: true, message: '宠物猫不能为空', trigger: 'blur' }
        ],
        petDog: [
          { required: true, message: '宠物狗不能为空', trigger: 'blur' }
        ],
        childrensRoom: [
          { required: true, message: '儿童房不能为空', trigger: 'blur' }
        ],
        functionalSpace: [
          { required: true, message: '功能空间不能为空', trigger: 'blur' }
        ],
        otherRooms: [
          { required: true, message: '其他房间不能为空', trigger: 'blur' }
        ],
        overallTone: [
          { required: true, message: '整体色调不能为空', trigger: 'blur' }
        ],
        lighting: [
          { required: true, message: '灯光要求不能为空', trigger: 'blur' }
        ],
        softOutfitBudget: [
          { required: true, message: '软装预算(元)不能为空', trigger: 'blur' }
        ],
        gender: [
          { required: true, message: '性别不能为空', trigger: 'blur' }
        ],
        corporateNatureX: [
          { required: true, message: '公司性质不能为空', trigger: 'blur' }
        ],
        dominantHue: [
          { required: true, message: '主色调不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 文件字段列表，用于初始化和提交前处理
      fileFields: [
        'intentionalImages',
        'decorationPicture',
        'cadDrawingsJz',
        'cadDrawingsDq',
        'cadDrawingsXf'
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 10,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      }
    }
  },
  dicts: ['building_structure', 'projectstate', 'project_nature', 'project_type_lx', 'huxing_structure', 'status', 'gender', 'corporate_nature_x', 'if_designer'],
  cruds() {
    return CRUD({ title: '项目立项（适用散户软装、精装房）', url: 'api/shop/tumaiOaJinzhuang', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaJinzhuang }})
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 获取并初始化文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, [
        'intentionalImages',
        'decorationPicture',
        'cadDrawingsJz',
        'cadDrawingsDq',
        'cadDrawingsXf'])
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化文件字段为空数组
      this.fileFields.forEach(field => {
        this.form[field] = '[]'
      })
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始表单数据:', JSON.stringify({
          intentionalImages: this.form.intentionalImages,
          decorationPicture: this.form.decorationPicture
        }))

        // 使用通用方法处理文件字段
        this.prepareGeneralFileFields(this.fileFields)

        console.log('处理附件后:', JSON.stringify({
          intentionalImages: this.form.intentionalImages,
          decorationPicture: this.form.decorationPicture
        }))

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01)
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #fff;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
