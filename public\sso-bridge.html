<!-- A系统的 /sso-bridge.html -->
<!DOCTYPE html>
<html>
<head>
  <title>SSO Bridge</title>
</head>
<body>
<script>
window.addEventListener('message', function(event) {
  if (event.origin !== 'http://localhost') return;
  // 打印B系统主动推送的数据
  if (event.data && event.data.type === 'localStorageValue') {
    console.log('B系统主动推送的数据：', event.data.key, event.data.value);
    localStorage.setItem(event.data.key, event.data.value);
  }
  // 响应B系统的getLocalStorage请求
  if (event.data && event.data.type === 'getLocalStorage') {
    const key = event.data.key;
    const value = localStorage.getItem(key);
    event.source.postMessage({ type: 'localStorageValue', key, value }, event.origin);
  }
  // 新增：处理B系统要求移除A系统localStorage
  if (event.data && event.data.type === 'removeLocalStorage') {
    localStorage.removeItem(event.data.key);
    console.log('A系统已移除localStorage:', event.data.key);
  }
});
</script>
</body>
</html>
