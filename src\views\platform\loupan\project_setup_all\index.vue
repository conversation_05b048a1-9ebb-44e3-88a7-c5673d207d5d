<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">国别</label>
        <el-input v-model="query.country" clearable placeholder="国别" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">归属门店/公司</label>
        <el-input v-model="query.ownerStore" clearable placeholder="归属门店/公司" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="归属门店/公司">
            <el-input v-model="form.ownerStore" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目名称">
            <el-input v-model="form.projectName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目性质">
            <el-input v-model="form.projectNature" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目数量">
            <el-input v-model="form.projectNum" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开盘时间">
            <el-date-picker v-model="form.openingDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表日期">
            <el-input v-model="form.tabDate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻角色">
            <el-input v-model="form.enterRole" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目类型">
            <el-input v-model="form.projectTypeLx" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目编号">
            <el-input v-model="form.projectNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目地址">
            <el-input v-model="form.projectAddress" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="服务类型">
            <el-input v-model="form.typeOfService" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘名称">
            <el-input v-model="form.buildingName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="建筑结构">
            <el-input v-model="form.buildingStructure" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="层高">
            <el-input v-model="form.floorhEight" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计楼栋">
            <el-input v-model="form.designBuilding" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="户型结构">
            <el-input v-model="form.huxingStructure" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="建筑面积㎡">
            <el-input v-model="form.areaOfStructure" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目概况">
            <el-input v-model="form.projectOverview" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="交房时间">
            <el-input v-model="form.deliveryDate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="计划入住时间">
            <el-input v-model="form.jihua" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="硬装预算">
            <el-input v-model="form.hardPackBudget" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="软装预算">
            <el-input v-model="form.softOutfitBudget" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="固装预算">
            <el-input v-model="form.fixedPackBudget" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司名称">
            <el-input v-model="form.corporateName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司性质">
            <el-input v-model="form.corporateNatureX" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（姓名）">
            <el-input v-model="form.name" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（联系电话）">
            <el-input v-model="form.contactNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（职务）">
            <el-input v-model="form.post" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（微信）">
            <el-input v-model="form.wechat" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（邮箱）">
            <el-input v-model="form.email" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（性别）">
            <el-input v-model="form.gender" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="风格需求">
            <el-input v-model="form.fengge" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="主色调">
            <el-input v-model="form.dominantHue" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="辅色调">
            <el-input v-model="form.auxiliaryTone" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="讨厌的颜色">
            <el-input v-model="form.hateColor" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目状态">
            <el-input v-model="form.projectstate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻角色（主体）">
            <el-input v-model="form.mainBody" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻角色（类型）">
            <el-input v-model="form.type" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻角色（公司性质）">
            <el-input v-model="form.companyProperty" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别" />
        <el-table-column prop="ownerStore" label="归属门店/公司" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质" />
        <el-table-column prop="projectNum" label="项目数量" />
        <el-table-column prop="openingDate" label="开盘时间" />
        <el-table-column prop="createBy" label="制表人" />
        <el-table-column prop="tabDate" label="制表日期" />
        <el-table-column prop="enterRole" label="入驻角色" />
        <el-table-column prop="projectTypeLx" label="项目类型" />
        <el-table-column prop="projectNumber" label="项目编号" />
        <el-table-column prop="projectAddress" label="项目地址" />
        <el-table-column prop="typeOfService" label="服务类型" />
        <el-table-column prop="buildingName" label="楼盘名称" />
        <el-table-column prop="buildingStructure" label="建筑结构" />
        <el-table-column prop="floorhEight" label="层高" />
        <el-table-column prop="designBuilding" label="设计楼栋" />
        <el-table-column prop="huxingStructure" label="户型结构" />
        <el-table-column prop="areaOfStructure" label="建筑面积㎡" />
        <el-table-column prop="projectOverview" label="项目概况" />
        <el-table-column prop="deliveryDate" label="交房时间" />
        <el-table-column prop="jihua" label="计划入住时间" />
        <el-table-column prop="hardPackBudget" label="硬装预算" />
        <el-table-column prop="softOutfitBudget" label="软装预算" />
        <el-table-column prop="fixedPackBudget" label="固装预算" />
        <el-table-column prop="corporateName" label="公司名称" />
        <el-table-column prop="corporateNatureX" label="公司性质" />
        <el-table-column prop="name" label="客户信息（姓名）" />
        <el-table-column prop="contactNumber" label="客户信息（联系电话）" />
        <el-table-column prop="post" label="客户信息（职务）" />
        <el-table-column prop="wechat" label="客户信息（微信）" />
        <el-table-column prop="email" label="客户信息（邮箱）" />
        <el-table-column prop="gender" label="客户信息（性别）" />
        <el-table-column prop="fengge" label="风格需求" />
        <el-table-column prop="dominantHue" label="主色调" />
        <el-table-column prop="auxiliaryTone" label="辅色调" />
        <el-table-column prop="hateColor" label="讨厌的颜色" />
        <el-table-column prop="projectstate" label="项目状态" />
        <el-table-column prop="mainBody" label="入驻角色（主体）" />
        <el-table-column prop="type" label="入驻角色（类型）" />
        <el-table-column prop="companyProperty" label="入驻角色（公司性质）" />
        <el-table-column v-if="checkPer(['admin','loupanProjectSetupAllSummary:edit','loupanProjectSetupAllSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudloupanProjectSetupAllSummary from '@/api/loupan/loupanProjectSetupAllSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, ownerStore: null, projectName: null, projectNature: null, projectNum: null, openingDate: null, createBy: null, tabDate: null, enterRole: null, projectTypeLx: null, projectNumber: null, projectAddress: null, typeOfService: null, buildingName: null, buildingStructure: null, floorhEight: null, designBuilding: null, huxingStructure: null, areaOfStructure: null, projectOverview: null, deliveryDate: null, jihua: null, hardPackBudget: null, softOutfitBudget: null, fixedPackBudget: null, corporateName: null, corporateNatureX: null, name: null, contactNumber: null, post: null, wechat: null, email: null, gender: null, fengge: null, dominantHue: null, auxiliaryTone: null, hateColor: null, projectstate: null, mainBody: null, type: null, companyProperty: null }
export default {
  name: 'LoupanProjectSetupAllSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '项目立项汇总（全部）', url: 'api/loupanProjectSetupAllSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudloupanProjectSetupAllSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'loupanProjectSetupAllSummary:add'],
        edit: ['admin', 'loupanProjectSetupAllSummary:edit'],
        del: ['admin', 'loupanProjectSetupAllSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别' },
        { key: 'ownerStore', display_name: '归属门店/公司' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
