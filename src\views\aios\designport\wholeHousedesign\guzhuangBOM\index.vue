<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="900px" :before-close="crud.cancelCU" :visible="crud.status.cu > 0" @update:visible="val => crud.status.cu = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="未税零售总金额(元)">
                  <el-input v-model="form.grossRetailSales" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="未税B2B总金额（元）">
                  <el-input v-model="form.b2bAmount" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              <el-input v-model="form.notes" :rows="3" type="textarea" />
            </el-form-item>

            <!-- 固装BOM子表单 -->
            <el-divider content-position="left"><i class="el-icon-document" /> 固装BOM明细信息</el-divider>
            <div class="table-container">
              <el-table :data="bomDetailList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="品牌" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.brand" size="mini" placeholder="请输入品牌" />
                  </template>
                </el-table-column>
                <el-table-column label="产品编号" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.productNumber" size="mini" placeholder="请输入产品编号" />
                  </template>
                </el-table-column>
                <el-table-column label="产品名称" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.productName" size="mini" placeholder="请输入产品名称" />
                  </template>
                </el-table-column>
                <el-table-column label="材质" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialQuality" size="mini" placeholder="请输入材质" />
                  </template>
                </el-table-column>
                <el-table-column label="颜色" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.pigment" size="mini" placeholder="请输入颜色" />
                  </template>
                </el-table-column>
                <el-table-column label="未税B2B单价(元)" width="140">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.price" size="mini" placeholder="请输入单价" />
                  </template>
                </el-table-column>
                <el-table-column label="B2B金额(元)" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.amount" size="mini" placeholder="请输入金额" />
                  </template>
                </el-table-column>
                <el-table-column label="区域" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.zone" size="mini" placeholder="请输入区域" />
                  </template>
                </el-table-column>
                <el-table-column label="单位" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.unit" size="mini" placeholder="单位" />
                  </template>
                </el-table-column>
                <el-table-column label="供应商名称" width="140">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.supplierName" size="mini" placeholder="请输入供应商名称" />
                  </template>
                </el-table-column>
                <el-table-column label="数量" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.count" size="mini" placeholder="数量" />
                  </template>
                </el-table-column>
                <el-table-column label="零售单价(元)" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.retailUnitPrice" size="mini" placeholder="请输入零售单价" />
                  </template>
                </el-table-column>
                <el-table-column label="零售金额(元)" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.retailValue" size="mini" placeholder="请输入零售金额" />
                  </template>
                </el-table-column>
                <el-table-column label="类别" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialCategory" size="mini" placeholder="请输入类别" />
                  </template>
                </el-table-column>
                <el-table-column label="备注" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.remarks" size="mini" type="textarea" :rows="2" placeholder="请输入备注" />
                  </template>
                </el-table-column>
                <el-table-column label="规格" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.specs" size="mini" type="textarea" :rows="2" placeholder="请输入规格" />
                  </template>
                </el-table-column>
                <el-table-column label="图片" width="150">
                  <template slot-scope="scope">
                    <div class="small-upload-container">
                      <file-upload
                        size="small"
                        :field-value.sync="scope.row.picture"
                        :limit="2"
                        :upload-to-server="true"
                        :api-url="minioUploadApi"
                        @change="handleFileListChange($event, scope.row)"
                      />
                    </div>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveBomDetail(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddBomDetail">新增</el-button>
              </div>
            </div>
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="tabDate" label="制表日期" />
        <el-table-column prop="grossRetailSales" label="未税零售总金额(元)" />
        <el-table-column prop="b2bAmount" label="未税B2B总金额（元）" />
        <el-table-column v-if="checkPer(['admin','tumaiSjGzbom:edit','tumaiSjGzbom:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudTumaiSjGzbom from '@/api/aios/designport/wholeHousedesign/tumaiSjGzbom'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, projectNature: null, position01: null, uid: null, projectAddress: null, typeOfService: null, projectName: null, tabDate: null, projectNumber: null, projectOverview: null, notes: null, projectid: null, projectDeliveryPeriod: null, amountInTotal: null, fengge: null, huxingStructure: null, areaOfStructure: null, fixedPackBudget: null, finalLayoutPlan: null, pdfFormaFile2: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null, createtime: null, grossRetailSales: null, b2bAmount: null, bomDetailData: '[]' }
export default {
  name: 'TumaiSjGzbom',
  components: { pagination, crudOperation, rrOperation, udOperation, FileUpload },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin],
  dicts: ['project_nature', 'huxing_structure', 'status'],
  cruds() {
    return CRUD({ title: '固装BOM', url: 'api/tumaiSjGzbom', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjGzbom }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjGzbom:add'],
        edit: ['admin', 'tumaiSjGzbom:edit'],
        del: ['admin', 'tumaiSjGzbom:del']
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'projectNumber', display_name: '项目编号' }
      ],
      // BOM明细子表单数据
      bomDetailList: [],
      // 图片预览
      dialogImageUrl: '',
      dialogVisible: false
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi'])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'finalLayoutPlan',
        'pdfFormaFile2'
      ])

      // 初始化BOM明细列表
      this.initBomDetailList(form)

      // 初始化子表单中的文件字段
      this.initSubformFileFields(this.bomDetailList, 'picture')
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.finalLayoutPlan = '[]'
      this.form.pdfFormaFile2 = '[]'

      this.bomDetailList = []

      // 添加一个空的BOM明细记录
      this.handleAddBomDetail()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'finalLayoutPlan',
          'pdfFormaFile2'
        ])

        // 处理子表单中的图片字段
        this.prepareSubformFileFields(this.bomDetailList, 'picture')

        // 将处理后的子表单数据同步回表单
        crud.form.bomDetailData = JSON.stringify(this.bomDetailList)

        console.log('处理后的表单数据:', crud.form.bomDetailData)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化BOM明细列表
    initBomDetailList(form) {
      try {
        this.bomDetailList = form.bomDetailData ? JSON.parse(form.bomDetailData) : []
        if (!Array.isArray(this.bomDetailList)) {
          this.bomDetailList = []
        }

        // 确保每行都有picture字段
        this.bomDetailList.forEach(item => {
          if (!item.picture) {
            item.picture = '[]'
          }
        })
      } catch (e) {
        console.error('解析BOM明细数据失败:', e)
        this.bomDetailList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.bomDetailList.length === 0) {
        this.handleAddBomDetail()
      }
    },

    // 添加BOM明细
    handleAddBomDetail() {
      this.bomDetailList.push({
        brand: null,
        productNumber: null,
        productName: null,
        materialQuality: null,
        pigment: null,
        price: null,
        amount: null,
        zone: null,
        unit: null,
        supplierName: null,
        remarks: null,
        count: null,
        picture: '[]',
        retailUnitPrice: null,
        retailValue: null,
        materialCategory: null,
        specs: null
      })
    },

    // 移除BOM明细
    handleRemoveBomDetail(index) {
      this.bomDetailList.splice(index, 1)
      if (this.bomDetailList.length === 0) {
        this.handleAddBomDetail()
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      // 确保事件对象格式正确
      if (event && event.action === 'success' && event.file && event.file.response) {
        // 手动补充事件信息
        if (!event.fieldName && row !== this.form) {
          // 针对子表单的BOM行，指定正确的字段名
          event.fieldName = 'picture'
        }
      }

      let result = false

      try {
        if (row === this.form) {
          // 主表单文件处理
          result = this.handleAttachmentChange(event, row)
          console.log('主表单处理后数据:', JSON.stringify({
            finalLayoutPlan: this.form.finalLayoutPlan,
            pdfFormaFile2: this.form.pdfFormaFile2
          }))
        } else {
          // 子表单文件处理
          result = this.handleSubformFileChange(event, row)
          console.log('子表单处理后行数据:', JSON.stringify(row))
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },
    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.small-upload-container {
  width: 100%;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}
</style>
