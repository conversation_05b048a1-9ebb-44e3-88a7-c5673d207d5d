<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="提交客户日期">
                  <el-date-picker v-model="form.submissionDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddr" style="width: 100%" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="商务跟单">
                  <el-input v-model="form.businessdocumentary" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物料设计计划完成时间">
                  <el-date-picker v-model="form.plannedCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="样板准备开始时间">
                  <el-date-picker v-model="form.samplepreparationStartTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="样板准备完成时间">
                  <el-date-picker v-model="form.samplepreparationCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" style="width: 100%" />
            </el-form-item>

            <el-form-item label="异常说明">
              <el-input v-model="form.exceptionDescription" :rows="2" type="textarea" style="width: 100%" />
            </el-form-item>

            <!-- 物料样本信息子表单 -->
            <el-divider content-position="left"><i class="el-icon-box" /> 物料样本列表</el-divider>
            <div class="table-container">
              <el-table :data="materialSamplesList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="材料名称/型号" min-width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialNamemodelNumber" placeholder="请输入材料名称/型号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="品牌" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.brand" placeholder="请输入品牌" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="样板规格" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialSamples" placeholder="请输入样板规格" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="样板数量" min-width="130">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.numberAterialSamples" placeholder="请输入样板数量" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="备注" min-width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.notes" placeholder="请输入备注" type="textarea" :rows="2" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveMaterialSample(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddMaterialSample">新增样本</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>
            <el-form-item label="样板实物图">
              <file-upload
                :field-value.sync="form.samplePhoto"
                :limit="10"
                :upload-to-server="true"
                :api-url="minioUploadApi"
                :hide-remove="isViewMode"
                @change="handleFileListChange($event, form)"
              />
            </el-form-item>
            <el-form-item label="样板示意图">
              <file-upload
                :field-value.sync="form.sampleDiagram"
                :limit="10"
                :upload-to-server="true"
                :api-url="minioUploadApi"
                :hide-remove="isViewMode"
                @change="handleFileListChange($event, form)"
              />
            </el-form-item>
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjYzwl:edit','tumaiSjYzwl:del','tumaiSjYzwl:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudTumaiSjYzwl from '@/api/aios/designport/deluxedesign/tumaiSjYzwl'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, submissionDate: null,
  tabDate: null, projectNumber: null, projectNature: null, projectAddr: null, projectName: null,
  serviceType: null, projectOverview: null, samplePhoto: null, sampleDiagram: null,
  plannedCompletionTime: null, businessdocumentary: null, exceptionDescription: null,
  uid: null, optdt: null, optid: null, optname: null, applydt: null, explain: null,
  status: null, isturn: null, projectAddress: null, typeOfService: null,
  samplepreparationStartTime: null, samplepreparationCompletionTime: null,
  projectid: null, createtime: null, projectDeliveryPeriod: null,
  // 子表表单字段
  materialSamplesList: '[]'
}
export default {
  name: 'TumaiSjYzwl',
  components: { pagination, crudOperation, rrOperation, FileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status'],
  cruds() {
    return CRUD({ title: '硬装物料设计', url: 'api/tumaiSjYzwl', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjYzwl }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjYzwl:add'],
        edit: ['admin', 'tumaiSjYzwl:edit'],
        del: ['admin', 'tumaiSjYzwl:del'],
        view: ['admin', 'tumaiSjYzwl:edit', 'tumaiSjYzwl:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      samplePhotoFileList: [],
      sampleDiagramFileList: [],
      // 物料样本列表
      materialSamplesList: [],
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields(['samplePhoto', 'sampleDiagram'])
      // 初始化物料样本列表
      this.initMaterialSamplesList()
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields(['samplePhoto', 'sampleDiagram'])
      this.initMaterialSamplesList()
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['samplePhoto', 'sampleDiagram'])
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化主表单附件为空数组
      this.form.samplePhoto = '[]'
      this.form.sampleDiagram = '[]'

      // 清空物料样本列表
      this.materialSamplesList = []

      // 添加一个空的物料样本
      this.handleAddMaterialSample()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        // 处理主表单附件字段
        this.prepareAttachmentFields(['samplePhoto', 'sampleDiagram'])

        // 更新表单中的物料样本数据
        crud.form.materialSamplesList = JSON.stringify(this.materialSamplesList)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化物料样本列表
    initMaterialSamplesList() {
      try {
        this.materialSamplesList = this.form.materialSamplesList ? JSON.parse(this.form.materialSamplesList) : []
        if (!Array.isArray(this.materialSamplesList)) {
          this.materialSamplesList = []
        }
      } catch (e) {
        console.error('解析物料样本数据失败:', e)
        this.materialSamplesList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.materialSamplesList.length === 0) {
        this.handleAddMaterialSample()
      }
    },

    // 添加物料样本
    handleAddMaterialSample() {
      this.materialSamplesList.push({
        notes: null,
        materialNamemodelNumber: null,
        brand: null,
        materialSamples: null,
        numberAterialSamples: null
      })
    },

    // 移除物料样本
    handleRemoveMaterialSample(index) {
      this.materialSamplesList.splice(index, 1)
      if (this.materialSamplesList.length === 0) {
        this.handleAddMaterialSample()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      // 确保事件对象格式正确
      if (event && event.action === 'success' && event.file && event.file.response) {
        // 根据上下文自动判断字段名
        if (!event.fieldName) {
          if (row === this.form) {
            // 主表单字段处理 - 通过event.target自动判断
            const targetUpload = event.target
            if (targetUpload && targetUpload.classList) {
              if (targetUpload.closest('.el-form-item__label').textContent.includes('样板实物图')) {
                event.fieldName = 'samplePhoto'
              } else if (targetUpload.closest('.el-form-item__label').textContent.includes('样板示意图')) {
                event.fieldName = 'sampleDiagram'
              }
            }
          }
        }
      }

      let result = false

      try {
        if (row === this.form) {
          // 主表单文件处理
          result = this.handleAttachmentChange(event, row)
        } else {
          // 子表单文件处理
          result = this.handleSubformFileChange(event, row)
        }

        if (!result) {
          console.warn('文件处理返回失败结果')
          this.$message.warning('文件处理失败，请重试')
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },
    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01)
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}
</style>
