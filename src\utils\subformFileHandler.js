/**
 * 子表单文件处理工具
 * 用于统一处理表单中子表单的文件字段
 */

/**
 * 将JSON格式的文件列表转换为URL字符串
 * @param {String} jsonStr - JSON格式的文件列表字符串
 * @returns {String} - URL字符串，多个URL用逗号分隔
 */
function jsonToUrlString(jsonStr) {
  if (!jsonStr || typeof jsonStr !== 'string') {
    return ''
  }

  try {
    // 尝试解析JSON
    const files = JSON.parse(jsonStr)

    // 如果已经是URL字符串，直接返回
    if (typeof files === 'string' && files.includes('http')) {
      return files
    }

    // 处理JSON数组
    if (Array.isArray(files) && files.length > 0) {
      // 提取url字段并过滤空值
      const urls = files.map(file => file.url).filter(url => url)

      if (urls.length > 0) {
        return urls.join(',')
      }
    }
  } catch (e) {
    console.warn('转换JSON到URL字符串失败:', e)
  }

  return ''
}

/**
 * 从URL中提取文件名
 * @param {string} url - 文件URL
 * @returns {string} - 文件名
 */
function getFileNameFromUrl(url) {
  if (!url) return '未知文件'
  try {
    const parts = url.split('/')
    const fileName = parts[parts.length - 1]
    // 如果文件名包含扩展名，直接返回
    if (fileName && fileName.includes('.')) {
      return fileName
    }
    // 如果没有扩展名，尝试从URL中推断
    return fileName || '未知文件'
  } catch (e) {
    console.error('提取文件名失败:', e)
    return '未知文件'
  }
}

/**
 * 将URL字符串转换为JSON格式的文件列表
 * @param {String} urlStr - URL字符串，多个URL用逗号分隔
 * @returns {String} - JSON格式的文件列表字符串
 */
function urlStringToJson(urlStr) {
  if (!urlStr) {
    return '[]'
  }

  // 如果已经是JSON格式，检查有效性
  if (urlStr.startsWith('[') && urlStr.endsWith(']')) {
    try {
      // 验证是否是有效的JSON
      JSON.parse(urlStr)
      return urlStr
    } catch (e) {
      console.warn('无效的JSON格式:', urlStr)
      return '[]'
    }
  }

  try {
    // 处理URL字符串
    if (typeof urlStr === 'string') {
      // 分割多个URL
      const urls = urlStr.split(',').filter(url => url.trim())

      if (urls.length > 0) {
        // 转换为文件对象数组
        const fileList = urls.map((url) => ({
          name: getFileNameFromUrl(url.trim()),
          url: url.trim()
        }))

        return JSON.stringify(fileList)
      }
    }
  } catch (e) {
    console.warn('转换URL字符串到JSON失败:', e)
  }

  return '[]'
}

/**
 * 子表单文件处理工具类
 */
export const SubformFileHandler = {
  /**
   * 初始化子表单列表中的文件字段
   * 将数据库中的URL字符串转换为前端需要的JSON格式
   *
   * @param {Array} subformList - 子表单数据列表
   * @param {Array|String} fileFields - 需要处理的文件字段名(数组或单个字符串)
   * @returns {Array} - 处理后的子表单列表
   */
  initFields(subformList, fileFields) {
    if (!subformList || !Array.isArray(subformList)) {
      console.warn('子表单列表无效')
      return subformList || []
    }

    // 确保fileFields是数组
    const fields = Array.isArray(fileFields) ? fileFields : [fileFields]

    console.log(`准备初始化${subformList.length}个子表单项中的${fields.length}个文件字段`)

    // 处理每个子表单项
    subformList.forEach((item, index) => {
      if (!item) return

      // 处理每个文件字段
      fields.forEach(field => {
        if (!field) return

        // 将URL字符串转为JSON格式
        item[field] = urlStringToJson(item[field])
        console.log(`初始化子表单项[${index}]的字段[${field}] = ${item[field]}`)
      })
    })

    return subformList
  },

  /**
   * 准备子表单列表中的文件字段用于提交
   * 将前端的JSON格式转换为数据库需要的URL字符串
   *
   * @param {Array} subformList - 子表单数据列表
   * @param {Array|String} fileFields - 需要处理的文件字段名(数组或单个字符串)
   * @returns {Array} - 处理后的子表单列表
   */
  prepareFields(subformList, fileFields) {
    if (!subformList || !Array.isArray(subformList)) {
      console.warn('子表单列表无效')
      return subformList || []
    }

    // 确保fileFields是数组
    const fields = Array.isArray(fileFields) ? fileFields : [fileFields]

    console.log(`准备处理${subformList.length}个子表单项中的${fields.length}个文件字段用于提交`)

    // 处理每个子表单项
    subformList.forEach((item, index) => {
      if (!item) return

      // 处理每个文件字段
      fields.forEach(field => {
        if (!field) return

        const original = item[field]

        // 将JSON格式转为URL字符串
        item[field] = jsonToUrlString(item[field])

        console.log(`处理子表单项[${index}]的字段[${field}]: ${original} => ${item[field]}`)
      })
    })

    return subformList
  },

  /**
   * 检查子表单列表中是否有文件已上传
   * 用于验证字段是否有效，防止提交空值
   *
   * @param {Array} subformList - 子表单数据列表
   * @param {Array|String} fileFields - 需要检查的文件字段名(数组或单个字符串)
   * @returns {Boolean} - 是否有文件已上传
   */
  hasFiles(subformList, fileFields) {
    if (!subformList || !Array.isArray(subformList) || subformList.length === 0) {
      return false
    }

    // 确保fileFields是数组
    const fields = Array.isArray(fileFields) ? fileFields : [fileFields]

    // 检查每个子表单项
    for (const item of subformList) {
      if (!item) continue

      // 检查每个文件字段
      for (const field of fields) {
        if (!field) continue

        // 检查是否有URL
        const urlString = jsonToUrlString(item[field])
        if (urlString) {
          return true
        }
      }
    }

    return false
  },

  /**
   * 子表单文件变更事件处理
   * 用于处理子表单项中的文件上传、删除等操作
   *
   * @param {Object} event - 文件变更事件
   * @param {Object} row - 子表单行数据
   * @param {Function} deleteApi - 文件删除API函数
   * @param {Object} vueInstance - Vue组件实例
   * @returns {Boolean} - 处理成功返回true
   */
  handleFileChange(event, row, deleteApi, vueInstance) {
    if (!event || !row) {
      console.warn('事件或行数据无效')
      return false
    }

    console.log('处理子表单文件变更事件:', event)
    console.log('变更前子表单行数据:', row)

    const { file, action, field, fieldName } = event

    // 获取字段名
    const targetField = field || fieldName || (event.fieldValue !== undefined ? 'fieldValue' : null)

    if (!targetField) {
      console.warn('无法确定目标字段名')
      return false
    }

    if (action === 'success' && file && file.response) {
      // 文件上传成功
      try {
        // 修改这里：支持多种响应格式
        const url = file.response.data || file.response.url || file.response

        if (!url) {
          console.warn('文件上传成功但未获取到URL')
          if (vueInstance && vueInstance.$message) {
            vueInstance.$message.warning('文件上传成功但未获取到URL')
          }
          return false
        }

        // 解析现有文件列表
        let fileList = []
        try {
          fileList = JSON.parse(row[targetField] || '[]')
          if (!Array.isArray(fileList)) fileList = []
        } catch (e) {
          console.warn('解析现有文件列表失败，重置为空数组:', e)
          fileList = []
        }

        // 添加新文件
        fileList.push({
          name: file.name || `附件${fileList.length + 1}`,
          url: url
        })

        // 更新行数据
        row[targetField] = JSON.stringify(fileList)

        console.log(`子表单字段 ${targetField} 更新成功，添加文件URL: ${url}`)
        console.log(`更新后的字段值: ${row[targetField]}`)

        return true
      } catch (e) {
        console.error('处理文件上传事件失败:', e)
        return false
      }
    } else if (action === 'remove' && file) {
      // 文件删除
      try {
        // 解析现有文件列表
        let fileList = []
        try {
          fileList = JSON.parse(row[targetField] || '[]')
          if (!Array.isArray(fileList)) fileList = []
        } catch (e) {
          fileList = []
        }

        // 处理服务器上的文件删除
        if (file.url && deleteApi) {
          deleteApi({ fileName: file.url })
            .then(() => {
              if (vueInstance) {
                vueInstance.$message.success('文件删除成功')
              }
            })
            .catch(error => {
              console.error('删除服务器文件失败:', error)
              if (vueInstance) {
                vueInstance.$message.error('文件删除失败')
              }
            })
        }

        // 更新文件列表
        fileList = fileList.filter(item => item.url !== file.url)

        // 更新行数据
        row[targetField] = JSON.stringify(fileList)
        console.log('文件删除后更新字段:', targetField, row[targetField])

        return true
      } catch (e) {
        console.error('处理文件删除事件失败:', e)
        return false
      }
    }

    return false
  }
}

/**
 * 子表单文件处理Mixin
 * 提供在Vue组件中使用的便捷方法
 */
export const subformFileHandlerMixin = {
  methods: {
    /**
     * 初始化子表单中的文件字段
     * @param {Array} subformList - 子表单数据列表
     * @param {Array|String} fileFields - 需要处理的文件字段名
     * @returns {Array} - 处理后的子表单列表
     */
    initSubformFileFields(subformList, fileFields) {
      return SubformFileHandler.initFields(subformList, fileFields)
    },

    /**
     * 准备子表单中的文件字段用于提交
     * @param {Array} subformList - 子表单数据列表
     * @param {Array|String} fileFields - 需要处理的文件字段名
     * @returns {Array} - 处理后的子表单列表
     */
    prepareSubformFileFields(subformList, fileFields) {
      return SubformFileHandler.prepareFields(subformList, fileFields)
    },

    /**
     * 检查子表单中是否有文件已上传
     * @param {Array} subformList - 子表单数据列表
     * @param {Array|String} fileFields - 需要检查的文件字段名
     * @returns {Boolean} - 是否有文件已上传
     */
    hasSubformFiles(subformList, fileFields) {
      return SubformFileHandler.hasFiles(subformList, fileFields)
    },

    /**
     * 处理子表单文件变更事件
     * @param {Object} event - 文件变更事件
     * @param {Object} row - 子表单行数据
     * @returns {Boolean} - 处理成功返回true
     */
    handleSubformFileChange(event, row) {
      return SubformFileHandler.handleFileChange(event, row, this.minioDeleteApi, this)
    }
  }
}
