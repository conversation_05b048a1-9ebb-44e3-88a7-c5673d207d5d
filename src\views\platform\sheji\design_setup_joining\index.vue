<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">设计立项单号</label>
        <el-input v-model="query.designCode" clearable placeholder="设计立项单号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">国别</label>
        <el-input v-model="query.country" clearable placeholder="国别" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">制表人</label>
        <el-input v-model="query.lister" clearable placeholder="制表人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目性质</label>
        <el-input v-model="query.projectNature" clearable placeholder="项目性质" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目地址</label>
        <el-input v-model="query.projectAddr" clearable placeholder="项目地址" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计立项单号">
            <el-input v-model="form.designCode" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表人">
            <el-input v-model="form.lister" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表日期">
            <el-date-picker v-model="form.listerDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目类型">
            <el-input v-model="form.projectType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目编号">
            <el-input v-model="form.projectCode" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目名称">
            <el-input v-model="form.projectName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目性质">
            <el-input v-model="form.projectNature" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目地址">
            <el-input v-model="form.projectAddr" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目交期">
            <el-date-picker v-model="form.projectPayDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目概况">
            <el-input v-model="form.projectSituation" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（平面图）">
            <el-input v-model="form.planeGraph" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（概念方案）">
            <el-input v-model="form.scheme" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（效果图）">
            <el-input v-model="form.effectDrawing" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（施工图）">
            <el-input v-model="form.workingDrawing" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（硬装深化方案）">
            <el-input v-model="form.hardProgram" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（精装主材报价）">
            <el-input v-model="form.materialQuote" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（软装深化方案）">
            <el-input v-model="form.softProgram" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（软装清单及报价）">
            <el-input v-model="form.softListQuotation" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（软装物料采购）">
            <el-input v-model="form.softMaterialProcurement" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计周期计划时间（软装摆场）">
            <el-input v-model="form.softField" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="designCode" label="设计立项单号" />
        <el-table-column prop="country" label="国别" />
        <el-table-column prop="lister" label="制表人" />
        <el-table-column prop="listerDate" label="制表日期" />
        <el-table-column prop="projectType" label="项目类型" />
        <el-table-column prop="projectCode" label="项目编号" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质" />
        <el-table-column prop="projectAddr" label="项目地址" />
        <el-table-column prop="projectPayDate" label="项目交期" />
        <el-table-column prop="projectSituation" label="项目概况" />
        <el-table-column prop="planeGraph" label="设计周期计划时间（平面图）" />
        <el-table-column prop="scheme" label="设计周期计划时间（概念方案）" />
        <el-table-column prop="effectDrawing" label="设计周期计划时间（效果图）" />
        <el-table-column prop="workingDrawing" label="设计周期计划时间（施工图）" />
        <el-table-column prop="hardProgram" label="设计周期计划时间（硬装深化方案）" />
        <el-table-column prop="materialQuote" label="设计周期计划时间（精装主材报价）" />
        <el-table-column prop="softProgram" label="设计周期计划时间（软装深化方案）" />
        <el-table-column prop="softListQuotation" label="设计周期计划时间（软装清单及报价）" />
        <el-table-column prop="softMaterialProcurement" label="设计周期计划时间（软装物料采购）" />
        <el-table-column prop="softField" label="设计周期计划时间（软装摆场）" />
        <el-table-column v-if="checkPer(['admin','shejiDesignSetupJoiningSummary:edit','shejiDesignSetupJoiningSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudShejiDesignSetupJoiningSummary from '@/api/sheji/shejiDesignSetupJoiningSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, designCode: null, country: null, lister: null, listerDate: null, projectType: null, projectCode: null, projectName: null, projectNature: null, projectAddr: null, projectPayDate: null, projectSituation: null, planeGraph: null, scheme: null, effectDrawing: null, workingDrawing: null, hardProgram: null, materialQuote: null, softProgram: null, softListQuotation: null, softMaterialProcurement: null, softField: null }
export default {
  name: 'ShejiDesignSetupJoiningSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '设计立项汇总（店面加盟）', url: 'api/shejiDesignSetupJoiningSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudShejiDesignSetupJoiningSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'shejiDesignSetupJoiningSummary:add'],
        edit: ['admin', 'shejiDesignSetupJoiningSummary:edit'],
        del: ['admin', 'shejiDesignSetupJoiningSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'designCode', display_name: '设计立项单号' },
        { key: 'country', display_name: '国别' },
        { key: 'lister', display_name: '制表人' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'projectNature', display_name: '项目性质' },
        { key: 'projectAddr', display_name: '项目地址' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
