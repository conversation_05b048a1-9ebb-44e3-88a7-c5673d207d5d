/**
 * AI聊天历史管理工具
 * 提供聊天记录的本地存储和恢复功能
 */

const CHAT_HISTORY_KEY = 'ai_chat_history'
const MAX_HISTORY_SIZE = 100 // 最多保存100条消息

export class ChatHistoryManager {
  /**
   * 保存聊天记录到本地存储
   * @param {Array} messages - 消息数组
   */
  static saveHistory(messages) {
    try {
      // 只保存最近的消息，避免存储过大
      const recentMessages = messages.slice(-MAX_HISTORY_SIZE)
      // 清理消息中的一些不必要的属性，减少存储大小
      const cleanMessages = recentMessages.map(msg => ({
        id: msg.id,
        type: msg.type,
        text: msg.text,
        isHtml: msg.isHtml,
        chartUrls: msg.chartUrls,
        chartUrl: msg.chartUrl,
        image: msg.image,
        timestamp: msg.timestamp || Date.now()
      }))
      localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(cleanMessages))
    } catch (error) {
      console.warn('保存聊天记录失败:', error)
    }
  }

  /**
   * 从本地存储恢复聊天记录
   * @returns {Array} 消息数组
   */
  static loadHistory() {
    try {
      const stored = localStorage.getItem(CHAT_HISTORY_KEY)
      if (stored) {
        const messages = JSON.parse(stored)
        // 验证数据格式
        if (Array.isArray(messages)) {
          return messages
        }
      }
    } catch (error) {
      console.warn('加载聊天记录失败:', error)
    }
    // 返回默认的欢迎消息
    return [
      {
        id: 1,
        type: 'ai',
        text: '欢迎使用 AI 分析助手，请输入您的分析需求！',
        timestamp: Date.now()
      }
    ]
  }

  /**
   * 清空聊天记录
   */
  static clearHistory() {
    try {
      localStorage.removeItem(CHAT_HISTORY_KEY)
    } catch (error) {
      console.warn('清空聊天记录失败:', error)
    }
  }

  /**
   * 添加新消息并自动保存
   * @param {Array} currentMessages - 当前消息数组
   * @param {Object} newMessage - 新消息
   * @returns {Array} 更新后的消息数组
   */
  static addMessage(currentMessages, newMessage) {
    // 确保消息有时间戳
    if (!newMessage.timestamp) {
      newMessage.timestamp = Date.now()
    }
    const updatedMessages = [...currentMessages, newMessage]
    this.saveHistory(updatedMessages)
    return updatedMessages
  }

  /**
   * 更新最后一条消息并自动保存
   * @param {Array} currentMessages - 当前消息数组
   * @param {Object} updates - 要更新的属性
   * @returns {Array} 更新后的消息数组
   */
  static updateLastMessage(currentMessages, updates) {
    if (currentMessages.length === 0) return currentMessages
    const updatedMessages = [...currentMessages]
    const lastIndex = updatedMessages.length - 1
    updatedMessages[lastIndex] = {
      ...updatedMessages[lastIndex],
      ...updates,
      timestamp: updatedMessages[lastIndex].timestamp || Date.now()
    }
    this.saveHistory(updatedMessages)
    return updatedMessages
  }

  /**
   * 获取存储大小（KB）
   * @returns {number} 存储大小
   */
  static getStorageSize() {
    try {
      const stored = localStorage.getItem(CHAT_HISTORY_KEY)
      return stored ? (stored.length / 1024).toFixed(2) : 0
    } catch (error) {
      return 0
    }
  }
}

export default ChatHistoryManager
