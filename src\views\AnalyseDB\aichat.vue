<template>
  <div class="ai-chat-wrapper">
    <!-- 隐藏/显示切换按钮 -->
    <div
      class="toggle-button"
      :class="{ 'hidden': isHidden, 'dragging': isDragging }"
      :style="{ top: toggleButtonTop + 'px' }"
      @mousedown="startDragToggle"
      @click.stop="handleToggleClick"
    >
      <div class="button-content">
        <i v-if="isHidden" class="el-icon-chat-dot-round toggle-icon" />
        <i v-else class="el-icon-arrow-right toggle-icon" />
        <!-- 拖拽提示点 -->
        <div class="drag-dots">
          <span class="dot" />
          <span class="dot" />
          <span class="dot" />
        </div>
      </div>
    </div>

    <!-- AI对话框容器 -->
    <div v-show="!isHidden" class="ai-chat-container" :style="{width: chatWidth + 'px'}">
      <div class="chat-header">
        <div class="header-title">
          <span class="ai-icon">
            <img src="/aislogo.svg" alt="AI" class="ai-logo-svg">
          </span>
          <span>AI智能分析</span>
        </div>
        <div
          class="header-buttons"
          style="position: absolute; right: 20px; top: 50%; transform: translateY(-50%);"
        >
          <div
            class="transmit-btn"
            title="转发聊天记录"
            @click="showTransmitDialog = true"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
          <div
            class="clear-history-btn"
            title="清空聊天记录"
            @click="clearChatHistory"
          >
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
              <path
                d="M3 6h18M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6h14zM10 11v6M14 11v6"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              />
            </svg>
          </div>
        </div>
      </div>
      <div ref="chatBody" class="chat-body">
        <div v-for="message in messages" :key="message.id" class="chat-message" :class="message.type">
          <div class="bubble">
            <img v-if="message.image" :src="message.image" class="ai-generated-image">
            <div v-if="message.isLoading" class="streaming-indicator">
              <span class="dot" />
              <span class="dot" />
              <span class="dot" />
            </div>
            <div v-else-if="message.isHtml" class="html-content">
              <div v-html="message.text" />
              <!-- 显示多个图表 -->
              <div v-if="message.chartUrls && message.chartUrls.length > 0" class="charts-container">
                <div v-for="(chartUrl, index) in message.chartUrls" :key="index" class="chart-container">
                  <img :src="chartUrl" class="chart-image" :alt="`数据报表 ${index + 1}`" @error="handleImageError">
                </div>
              </div>
              <!-- 兼容旧的单个图表URL -->
              <div v-else-if="message.chartUrl" class="chart-container">
                <img :src="message.chartUrl" class="chart-image" alt="数据报表" @error="handleImageError">
              </div>
            </div>
            <p v-else>{{ message.text }}</p>
          </div>
        </div>
      </div>

      <!-- 快速启动模板 -->
      <QuestionTemplate
        :visible="showQuickTemplates"
        :message-count="messages.length"
        :is-ai-responding="isAiResponding"
        @template-selected="handleTemplateSelected"
        @hide-templates="handleHideTemplates"
      />

      <div class="if-create-image">
        <div class="left-container">
          <div v-if="localSelectedTables.length > 0" class="selected-tables-container">
            <div v-for="table in localSelectedTables" :key="table.tableName" class="selected-table-tag" :class="{ 'is-default': table.isDefault, 'is-pinned': table.isPinned }">
              <i
                class="pin-icon"
                :class="table.isPinned ? 'el-icon-lock' : 'el-icon-unlock'"
                :title="table.isPinned ? '点击取消固定' : '点击固定此表'"
                @click="toggleTablePin(table.tableName)"
              />
              <span class="table-name">
                {{ table.tableName }}
                <span v-if="table.isDefault" class="default-indicator">(默认)</span>
              </span>
              <i
                style="cursor:pointer;margin-left:4px;z-index:999;"
                class="el-icon-close"
                :title="`删除表: ${table.tableName}`"
                @click.stop="clearSingleTable(table.tableName)"
                @mousedown.stop
              />
            </div>
          </div>
        </div>
        <div class="right-container">
          <span class="if-create-image-label">是否需要生成图表</span>
          <el-radio-group v-model="ifCreateImage" @change="handleRadioChange">
            <el-radio :label="true" size="small">是</el-radio>
            <el-radio :label="false" size="small">否</el-radio>
          </el-radio-group>
        </div>
      </div>
      <!-- 表选择弹窗 -->
      <el-dialog
        :visible.sync="showTableSelector"
        title="选择数据表"
        width="400px"
        :before-close="handleCloseTableSelector"
        :modal="true"
        :close-on-click-modal="true"
        :close-on-press-escape="true"
        :append-to-body="true"
        :z-index="3000"
      >
        <div class="table-selector-content">
          <p class="data-tip">💡 提示：选择的数据将按当前分页条数提供给AI分析</p>

          <div class="table-list">
            <div v-for="tableName in availableTableList" :key="tableName" class="table-item">
              <el-checkbox
                :value="isTableSelected(tableName)"
                :label="tableName"
                @change="(checked) => handleTableSelection(tableName, checked)"
              >
                {{ tableName }}
              </el-checkbox>
            </div>
          </div>
          <div v-if="availableTableList.length === 0" class="no-tables">
            <p>暂无可选择的数据表</p>
            <p class="hint">请先点击左侧选择数据库和表</p>
          </div>
        </div>
        <div slot="footer" class="dialog-footer">
          <el-button @click="showTableSelector = false">取消</el-button>
          <el-button type="primary" @click="confirmTableSelection">确定</el-button>
        </div>
      </el-dialog>

      <!-- 转发聊天记录对话框 -->
      <TransmitChat
        :visible.sync="showTransmitDialog"
        :messages="messages"
      />

      <div class="chat-footer">
        <el-button class="at-btn" circle size="small" @click="handleAtButtonClick">@</el-button>
        <el-button
          class="template-btn"
          circle
          size="small"
          :class="{ 'active': showQuickTemplates }"
          title="快速模板"
          @click="toggleTemplates"
        >
          <i class="el-icon-magic-stick" />
        </el-button>
        <el-input
          v-model="userInput"
          type="textarea"
          :autosize="{ minRows: 1, maxRows: 14 }"
          placeholder="和AI聊聊你的分析想法吧…"
          class="chat-input"
          clearable
          @keydown.native="handleKeyDown"
        />
        <!-- 停止按钮 -->
        <el-button
          v-if="isAiResponding"
          type="danger"
          class="stop-button"
          title="停止AI回复"
          @click="stopAiResponse"
        >
          <i class="el-icon-close" />
        </el-button>
        <!-- 发送按钮 -->
        <el-button
          v-else
          type="primary"
          class="send-button"
          :disabled="!userInput.trim()"
          @click="handleSend"
        >
          <svg width="20" height="20" fill="none" viewBox="0 0 24 24">
            <path d="M2 21l21-9-21-9v7l15 2-15 2v7z" fill="currentColor" />
          </svg>
        </el-button>
      </div>
      <div class="resize-handle" @mousedown="startResize" />
    </div>
  </div>
</template>

<script>
import { ChaWithAiStream } from '@/api/AnalyseDB/aichat'
import DOMPurify from 'dompurify'
import { marked } from 'marked'
import ChatHistoryManager from '@/utils/chatHistory'
import QuestionTemplate from './QuestionTemplate.vue'
import TransmitChat from './TransmitChat.vue'

export default {
  name: 'AiChat',

  components: {
    QuestionTemplate,
    TransmitChat
  },

  props: {
    tableList: {
      type: Array,
      default: () => []
    },
    selectedTables: {
      type: Array,
      default: () => []
    },
    selectedTablesData: {
      type: Object,
      default: () => ({})
    }
  },

  data() {
    return {
      showTableSelector: false,
      localSelectedTables: [], // 本地选中的表列表，避免与props冲突
      isImportTable: false,
      deletedTables: [], // 记录已删除的表
      ifCreateImage: false,
      isHidden: false, // 控制对话框隐藏状态
      isAiResponding: false, // 控制AI是否正在响应

      // 切换按钮拖拽相关
      toggleButtonTop: window.innerHeight / 2 - 40, // 初始位置在屏幕中央
      isDragging: false,
      dragStartY: 0,
      dragStartTop: 0,
      dragStartTime: 0,
      hasDragged: false, // 标记是否发生了实际拖拽

      localTableData: [],
      localTableHeaders: [],

      userInput: '',
      messages: ChatHistoryManager.loadHistory(),

      chatWidth: 450,
      resizing: false,
      userScrolling: false, // 用户是否正在手动滚动

      // 快速启动模板相关
      showQuickTemplates: false, // 控制模板显示

      // 转发聊天记录相关
      showTransmitDialog: false
    }
  },

  computed: {
    // 计算属性：从selectedTablesData获取可选择的表列表（所有曾经点击过的表）
    availableTableList() {
      if (!this.selectedTablesData) return []
      // 返回所有在selectedTablesData中的表名，不再过滤deletedTables
      // 因为删除操作只是从选中列表移除，不是永久删除
      return Object.keys(this.selectedTablesData)
    }
  },

  watch: {
    // 监听props变化，同步本地数据
    '$props.selectedTables': {
      handler(newSelectedTables) {
        // 确保完整复制对象结构，包括 isPinned 和 isDefault 属性
        this.localSelectedTables = (newSelectedTables || []).map(table => ({
          tableName: table.tableName,
          headers: table.headers,
          data: table.data,
          isPinned: table.isPinned || false,
          isDefault: table.isDefault || false
        }))
        this.updateLocalTableData()
      },
      immediate: true,
      deep: true
    },
    '$props.selectedTablesData': {
      handler() {
        this.updateLocalTableData()
      },
      immediate: true,
      deep: true
    }
  },

  mounted() {
    this.initEventListeners()
    this.restoreButtonPosition()
    this.initScrollListener()
  },

  beforeDestroy() {
    this.cleanupEventListeners()
    this.cleanupAiConnection()
  },

  methods: {
    // 初始化事件监听器
    initEventListeners() {
      window.addEventListener('mousemove', this.doResize)
      window.addEventListener('mouseup', this.stopResize)
      window.addEventListener('resize', this.handleWindowResize)
    },

    // 清理事件监听器
    cleanupEventListeners() {
      window.removeEventListener('mousemove', this.doResize)
      window.removeEventListener('mouseup', this.stopResize)
      window.removeEventListener('resize', this.handleWindowResize)
      document.removeEventListener('mousemove', this.doDragToggle)
      document.removeEventListener('mouseup', this.stopDragToggle)
    },

    // 恢复按钮位置
    restoreButtonPosition() {
      const savedTop = localStorage.getItem('toggleButtonTop')
      if (savedTop) {
        this.toggleButtonTop = parseInt(savedTop)
        this.handleWindowResize()
      }
    },

    // 初始化滚动监听
    initScrollListener() {
      this.$nextTick(() => {
        const chatBody = this.$refs.chatBody
        if (chatBody) {
          chatBody.addEventListener('scroll', this.handleScroll)
        }
      })
    },

    // 清理AI连接
    cleanupAiConnection() {
      if (window.aiEventSource) {
        window.aiEventSource.close()
        window.aiEventSource = null
      }
    },

    // 更新消息并保存历史记录
    updateMessagesAndSave() {
      this.messages = [...this.messages]
      ChatHistoryManager.saveHistory(this.messages)
    },

    // 清空聊天记录
    clearChatHistory() {
      this.$confirm('确定要清空所有聊天记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ChatHistoryManager.clearHistory()
        this.messages = ChatHistoryManager.loadHistory()
        this.resetTemplatesVisibility() // 重置模板显示
        this.$message.success('聊天记录已清空')
      }).catch(() => {
        // 用户取消
      })
    },

    // @按钮点击处理
    handleAtButtonClick() {
      // 使用 nextTick 确保 DOM 更新后再显示弹窗
      this.$nextTick(() => {
        this.showTableSelector = true

        // 再次使用 nextTick 确保弹窗完全渲染后设置焦点
        this.$nextTick(() => {
          // 尝试聚焦到弹窗内的第一个可聚焦元素
          const dialog = document.querySelector('.el-dialog')
          if (dialog) {
            const firstInput = dialog.querySelector('input, button, [tabindex]')
            if (firstInput) {
              firstInput.focus()
            }
          }
        })
      })
    },

    // 调试单选按钮变化
    handleRadioChange(value) {
      console.log('单选按钮变化:', value, '类型:', typeof value)
      console.log('当前 ifCreateImage:', this.ifCreateImage, '类型:', typeof this.ifCreateImage)
    },

    // 拖拽调整大小功能
    startResize(e) {
      this.resizing = true
      this.setResizeCursor(true)
      e.preventDefault()
    },

    doResize(e) {
      if (this.resizing) {
        const newWidth = window.innerWidth - e.clientX
        this.chatWidth = this.constrainChatWidth(newWidth)
      }
    },

    stopResize() {
      this.resizing = false
      this.setResizeCursor(false)
    },

    // 设置调整大小光标
    setResizeCursor(isResizing) {
      document.body.style.cursor = isResizing ? 'ew-resize' : ''
      document.body.style.userSelect = isResizing ? 'none' : ''
    },

    // 约束聊天窗口宽度
    constrainChatWidth(width) {
      const min = 400
      const max = window.innerWidth * 0.8
      return Math.min(Math.max(width, min), max)
    },
    // 窗口大小改变时调整按钮位置
    handleWindowResize() {
      this.toggleButtonTop = this.constrainButtonPosition(this.toggleButtonTop)
    },

    // 更新本地表数据
    updateLocalTableData() {
      if (this.localSelectedTables.length > 0) {
        // 合并所有选中表的数据
        this.localTableData = []
        this.localTableHeaders = []

        this.localSelectedTables.forEach(table => {
          if (table.headers && table.data) {
            // 添加表名前缀到表头
            const prefixedHeaders = table.headers.map(header => `${table.tableName}.${header}`)
            this.localTableHeaders.push(...prefixedHeaders)

            // 添加数据
            table.data.forEach((row, rowIndex) => {
              if (!this.localTableData[rowIndex]) {
                this.localTableData[rowIndex] = []
              }
              this.localTableData[rowIndex].push(...row)
            })
          }
        })
      } else {
        this.localTableData = []
        this.localTableHeaders = []
      }
    },

    // 清除所有表
    clearTable() {
      this.localSelectedTables = []
      this.localTableData = []
      this.localTableHeaders = []
      this.isImportTable = false
      this.$emit('clear-table')
    },

    // 清除单个表
    clearSingleTable(tableName) {
      // 过滤掉指定的表格
      this.localSelectedTables = this.localSelectedTables.filter(table => table.tableName !== tableName)
      console.log('删除后 localSelectedTables:', this.localSelectedTables)

      // 如果没有选中的表了，设置isImportTable为false
      if (this.localSelectedTables.length === 0) {
        this.isImportTable = false
      }

      this.updateLocalTableData()

      // 通知父组件更新选中的表列表，但不删除表数据
      this.$emit('update-selected-tables', this.localSelectedTables)
    },

    // 切换表格固定状态
    toggleTablePin(tableName) {
      this.$emit('toggle-table-pin', tableName)
    },

    // 检查表是否已选择
    isTableSelected(tableName) {
      return this.localSelectedTables.some(table => table.tableName === tableName)
    },

    // 处理表选择
    handleTableSelection(tableName, checked) {
      if (checked) {
        // 添加表到选择列表（用户主动勾选，标记为固定）
        if (!this.isTableSelected(tableName) && this.selectedTablesData && this.selectedTablesData[tableName]) {
          const tableData = this.selectedTablesData[tableName]
          this.localSelectedTables.push({
            tableName: tableName,
            headers: tableData.headers,
            data: tableData.data,
            isPinned: true, // 用户主动勾选的表，标记为固定
            isDefault: false // 通过弹窗选择的表默认不是默认表
          })
        }
      } else {
        // 从选择列表中移除表
        this.localSelectedTables = this.localSelectedTables.filter(table => table.tableName !== tableName)
      }
    },

    // 确认表选择
    confirmTableSelection() {
      this.showTableSelector = false
      this.updateLocalTableData()
      this.isImportTable = this.localSelectedTables.length > 0

      // 将用户在弹窗中选择的表同步到父组件
      // 通过emit事件通知父组件更新selectedTables
      this.$emit('update-selected-tables', this.localSelectedTables)
    },

    // 处理关闭表选择器
    handleCloseTableSelector() {
      this.showTableSelector = false
    },

    // 切换按钮拖拽功能
    startDragToggle(e) {
      e.preventDefault()
      e.stopPropagation()

      this.isDragging = true
      this.hasDragged = false
      this.dragStartY = e.clientY
      this.dragStartTop = this.toggleButtonTop
      this.dragStartTime = Date.now()

      this.setDragCursor(true)
      document.addEventListener('mousemove', this.doDragToggle)
      document.addEventListener('mouseup', this.stopDragToggle)
    },

    doDragToggle(e) {
      if (!this.isDragging) return

      const deltaY = e.clientY - this.dragStartY
      const deltaTime = Date.now() - this.dragStartTime

      if (Math.abs(deltaY) > 5 || deltaTime > 200) {
        this.hasDragged = true
      }

      this.toggleButtonTop = this.constrainButtonPosition(this.dragStartTop + deltaY)
    },

    stopDragToggle() {
      this.isDragging = false
      this.setDragCursor(false)
      document.removeEventListener('mousemove', this.doDragToggle)
      document.removeEventListener('mouseup', this.stopDragToggle)
      localStorage.setItem('toggleButtonTop', this.toggleButtonTop.toString())

      if (this.hasDragged) {
        setTimeout(() => { this.hasDragged = false }, 100)
      }
    },

    // 设置拖拽光标
    setDragCursor(isDragging) {
      document.body.style.cursor = isDragging ? 'grabbing' : ''
      document.body.style.userSelect = isDragging ? 'none' : ''
    },

    // 约束按钮位置在屏幕范围内
    constrainButtonPosition(newTop) {
      const buttonHeight = 80
      const minTop = 20
      const maxTop = window.innerHeight - buttonHeight - 20
      return Math.min(Math.max(newTop, minTop), maxTop)
    },

    handleToggleClick(e) {
      // 阻止事件冒泡
      e.preventDefault()
      e.stopPropagation()

      // 只有在没有发生实际拖拽的情况下才触发切换
      if (!this.hasDragged && !this.isDragging) {
        this.toggleChat()
      }
    },

    // 切换对话框显示/隐藏状态
    toggleChat() {
      this.isHidden = !this.isHidden

      // 如果是展开状态，滚动到底部
      if (!this.isHidden) {
        this.$nextTick(() => {
          this.scrollToBottom()
        })
      }
    },

    // 停止AI响应
    stopAiResponse() {
      this.isAiResponding = false
      this.cleanupAiStreams()
      this.updateLastAiMessage()
      this.updateMessagesAndSave()
      this.scrollToBottom()
    },

    // 清理AI流
    cleanupAiStreams() {
      if (window.aiStreamControl) {
        window.aiStreamControl.abort()
        window.aiStreamControl = null
      }
      this.cleanupAiConnection()
    },

    // 更新最后一条AI消息
    updateLastAiMessage() {
      const lastMessage = this.messages[this.messages.length - 1]
      if (lastMessage && lastMessage.type === 'ai') {
        lastMessage.isLoading = false
        lastMessage.isGeneratingChart = false
        lastMessage.text = lastMessage.text ? lastMessage.text + '\n\n*已停止响应*' : '已停止响应'
      }
    },

    // 处理键盘事件
    handleKeyDown(event) {
      // 只处理回车键
      if (event.key === 'Enter') {
        // 如果按下的是 Ctrl+Enter 或 Shift+Enter，允许换行
        if (event.ctrlKey || event.shiftKey) {
          return
        }

        // 阻止默认的换行行为
        event.preventDefault()

        // 触发发送
        this.handleSend()
      }
    },

    async handleSend() {
      if (!this.userInput.trim()) return

      // 添加用户消息
      this.messages = ChatHistoryManager.addMessage(this.messages, {
        id: Date.now(),
        type: 'user',
        text: this.userInput
      })

      // 添加加载中的 AI 消息
      const loadingMessage = {
        id: Date.now() + Math.random(),
        type: 'ai',
        text: '',
        isHtml: true, // 确保这里设置为true
        isLoading: true,
        image: null
      }
      this.messages = ChatHistoryManager.addMessage(this.messages, loadingMessage)

      // 设置AI正在响应状态
      this.isAiResponding = true

      try {
        const aiRequest = {
          content: this.userInput,
          ifCreateImage: this.ifCreateImage
        }

        // 如果有选中的表就发送数据
        if (this.localSelectedTables.length > 0) {
          // 构建多表数据结构
          const tablesData = {}
          const tablesHeaders = {}

          this.localSelectedTables.forEach(table => {
            tablesData[table.tableName] = table.data
            tablesHeaders[table.tableName] = table.headers
          })

          // 传递多表数据
          aiRequest.tablesData = tablesData
          aiRequest.tablesHeaders = tablesHeaders
          aiRequest.selectedTables = this.localSelectedTables.map(table => table.tableName)
        }

        // 创建消息容器用于接收流式数据
        const currentMessageIndex = this.messages.length - 1
        // 创建内容缓冲区来累积Markdown内容
        let contentBuffer = ''
        // 使用新的流式调用方法
        const streamControl = ChaWithAiStream(aiRequest, {
          // 处理内容消息 - 累积内容并实时显示
          onContent: (contentChunk) => {
            // 处理各种换行符情况
            if (contentChunk === '' || contentChunk === null || contentChunk === undefined) {
              // 空内容块，添加换行符
              contentBuffer += '\n'
            } else if (contentChunk === '\\n') {
              // 后端发送的字符串 "\n"，转换为真正的换行符
              contentBuffer += '\n'
            } else {
              // 正常内容块，将字符串中的 \n 转换为真正的换行符
              const processedChunk = contentChunk.replace(/\\n/g, '\n')
              contentBuffer += processedChunk
            }

            // 2. 用全部内容做 markdown 解析
            marked.setOptions({
              gfm: true,
              breaks: true,
              smartLists: true,
              xhtml: true
            })
            const htmlContent = marked.parse(contentBuffer)
            const safeHtmlContent = DOMPurify.sanitize(htmlContent)

            // 3. 更新消息内容
            const message = this.messages[currentMessageIndex]
            message.text = safeHtmlContent
            message.isHtml = true
            message.isLoading = false
            this.messages = [...this.messages]
            this.scrollToBottom()
          },
          onBotContent: (botContent) => {
            console.log('收到图片描述内容:', botContent)

            // 提取所有可能的图片URL
            const extractImageUrls = (text) => {
              const urls = []

              // 匹配所有 http/https 链接，排除常见的非图片链接
              const allUrlMatches = text.match(/https?:\/\/[^\s\)\]]+/g)

              if (allUrlMatches) {
                allUrlMatches.forEach(url => {
                  // 清理URL末尾可能的标点符号
                  const cleanUrl = url.replace(/[.,;!?]+$/, '')

                  // 判断是否可能是图片链接
                  const isImageUrl = (
                    // 包含常见图片扩展名
                    /\.(png|jpg|jpeg|gif|svg|webp|bmp)(\?.*)?$/i.test(cleanUrl) ||
                    // 包含图表相关关键词
                    /chart|graph|img|image|render/i.test(cleanUrl) ||
                    // 包含已知的图表服务域名
                    /(quickchart|ichartcool|chartjs|plotly|canvasjs|highcharts)\./.test(cleanUrl)
                  )

                  if (isImageUrl) {
                    urls.push(cleanUrl)
                  }
                })
              }

              return [...new Set(urls)] // 去重
            }

            const imageUrls = extractImageUrls(botContent)

            // 查找并替换加载状态消息
            const loadingMessageIndex = this.messages.findIndex(msg =>
              msg.type === 'ai' && msg.isLoading === true
            )

            if (loadingMessageIndex !== -1) {
              // 替换加载状态消息
              this.messages[loadingMessageIndex] = {
                id: this.messages[loadingMessageIndex].id,
                type: 'ai',
                text: botContent,
                isHtml: true, // 使用HTML渲染
                isLoading: false,
                chartUrls: imageUrls // 支持多个图表URL
              }
            } else {
              // 如果没有找到加载状态消息，创建新消息
              this.messages.push({
                id: Date.now() + Math.random(),
                type: 'ai',
                text: botContent,
                isHtml: true, // 使用HTML渲染
                chartUrls: imageUrls // 支持多个图表URL
              })
            }

            // 如果有图表URL，说明响应完成
            if (imageUrls.length > 0) {
              this.isAiResponding = false
            }

            // 确保视图更新
            this.messages = [...this.messages]
            this.scrollToBottom()
          },
          // 处理报表生成状态
          onReportGenerating: (message) => {
            console.log('报表生成中:', message)

            // 创建加载状态消息
            this.messages.push({
              id: Date.now() + Math.random(),
              type: 'ai',
              text: message,
              isHtml: false,
              isLoading: true
            })

            // 确保视图更新
            this.messages = [...this.messages]
            this.scrollToBottom()
          },
          // 处理图表
          onChart: (chartUrl) => {
            console.log('收到图表URL:', chartUrl)

            // 创建图表消息
            this.messages.push({
              id: Date.now() + Math.random(),
              type: 'ai',
              text: '',
              isHtml: false,
              chartUrl: chartUrl
            })

            // 确保视图更新
            this.messages = [...this.messages]
            this.scrollToBottom()
          },
          // 处理图片消息
          onImage: (imageUrl) => {
            // 路径转换：将本地路径转换为相对路径
            const relativePath = imageUrl
              .replace('file:///', '') // 去除 file:/// 前缀
              .replace(/\\/g, '/') // 统一使用正斜杠
              .split('/')
              .slice(-2) // 取最后两级路径（createImage/xxx.png）
              .join('/')

            this.messages.push({
              id: Date.now() + Math.random(),
              type: 'ai',
              image: `/${relativePath}` // 使用相对路径
            })
            // 确保视图更新
            this.messages = [...this.messages]
            this.scrollToBottom()
          },
          // 处理错误
          onError: (error) => {
            console.error('AI错误:', error)
            this.isAiResponding = false // 清除响应状态
            this.messages[currentMessageIndex].text = `AI 分析失败：${error}`
            this.messages[currentMessageIndex].isLoading = false
            this.messages = [...this.messages] // 确保视图更新
            // 滚动到底部
            this.scrollToBottom()
          },
          // 处理完成
          onComplete: (fullContent) => {
            this.isAiResponding = false // 清除响应状态

            const message = this.messages[currentMessageIndex]
            message.isLoading = false

            // 智能选择最完整的内容进行渲染
            if (fullContent && fullContent.trim() !== '') {
              // 优先使用后端提供的完整内容
              const safeHtmlContent = DOMPurify.sanitize(fullContent)
              message.text = safeHtmlContent
              message.isHtml = true
            } else if (contentBuffer && contentBuffer.trim() !== '') {
              // 如果没有fullContent，使用累积的contentBuffer
              marked.setOptions({
                gfm: true,
                breaks: true,
                smartLists: true,
                xhtml: true
              })
              const htmlContent = marked.parse(contentBuffer)
              const safeHtmlContent = DOMPurify.sanitize(htmlContent)
              message.text = safeHtmlContent
              message.isHtml = true
            }

            // 触发响应式更新并保存历史记录
            this.updateMessagesAndSave()
            // 滚动到底部
            this.scrollToBottom()
          }
        })

        // 保存控制引用以便可能的中断
        window.aiStreamControl = streamControl
      } catch (error) {
        console.error('发送请求失败:', error)
        this.isAiResponding = false // 清除响应状态
        const currentMessageIndex = this.messages.length - 1
        this.messages[currentMessageIndex] = {
          ...this.messages[currentMessageIndex],
          text: `AI 分析失败：${error.message || '未知错误'}`,
          isHtml: false,
          isLoading: false
        }
        this.updateMessagesAndSave() // 确保视图更新并保存
        // 滚动到底部
        this.scrollToBottom()
      }

      this.userInput = ''
    },

    // 处理用户滚动
    handleScroll() {
      const chatBody = this.$refs.chatBody
      if (chatBody) {
        const isAtBottom = chatBody.scrollTop + chatBody.clientHeight >= chatBody.scrollHeight - 5
        this.userScrolling = !isAtBottom
      }
    },

    scrollToBottom() {
      if (!this.userScrolling && this.$refs.chatBody) {
        this.$nextTick(() => {
          this.$refs.chatBody.scrollTop = this.$refs.chatBody.scrollHeight
        })
      }
    },

    handleImageError(event) {
      console.error('图表加载失败:', event.target.src)
      event.target.style.display = 'none'
    },

    // 创建AI消息
    createAiMessage(options = {}) {
      return {
        id: Date.now() + Math.random(),
        type: 'ai',
        text: options.text || '',
        isHtml: options.isHtml || false,
        isLoading: options.isLoading || false,
        chartUrl: options.chartUrl,
        chartUrls: options.chartUrls,
        image: options.image
      }
    },

    // 添加消息并更新视图
    addMessageAndUpdate(message) {
      this.messages.push(message)
      this.messages = [...this.messages]
      this.scrollToBottom()
    },

    // 模板相关方法
    handleTemplateSelected(data) {
      // 将模板内容填充到输入框
      this.userInput = data.content

      // 聚焦到输入框
      this.$nextTick(() => {
        const inputElement = this.$el.querySelector('.chat-input textarea')
        if (inputElement) {
          inputElement.focus()
          // 将光标移到文本末尾
          inputElement.setSelectionRange(inputElement.value.length, inputElement.value.length)
        }
      })
    },

    handleHideTemplates() {
      this.showQuickTemplates = false
    },

    // 切换模板显示状态
    toggleTemplates() {
      this.showQuickTemplates = !this.showQuickTemplates
    },

    // 重置模板显示（当清空聊天记录时调用）
    resetTemplatesVisibility() {
      this.showQuickTemplates = false
    }
  }
}
</script>

<style scoped>
/* AI对话框包装器 */
.ai-chat-wrapper {
  position: relative;
  height: 100vh;
}

::v-deep .el-textarea {
  .el-textarea__inner {
    resize: none;
  }
}

/* 切换按钮样式 */
.toggle-button {
  position: fixed;
  right: 0;
  width: 40px;
  height: 80px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px 0 0 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  z-index: 1001;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: -2px 0 10px rgba(102, 126, 234, 0.2);
  user-select: none;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.ai-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.ai-logo-svg {
  width: 28px;
  height: 28px;
  object-fit: contain;
}

.header-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 通用圆形按钮样式 */
.transmit-btn,
.clear-history-btn {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px);
}

.transmit-btn:hover,
.clear-history-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

/* 切换按钮状态 */
.toggle-button:hover:not(.dragging) {
  transform: translateX(-2px);
  box-shadow: -4px 0 15px rgba(102, 126, 234, 0.3);
}

.toggle-button.hidden {
  right: -10px;
  background: linear-gradient(135deg, #52c41a 0%, #1890ff 100%);
}

.toggle-button.hidden:hover:not(.dragging) {
  right: -5px;
}

.toggle-button.dragging {
  cursor: grabbing;
  transform: translateX(-2px) scale(1.05);
  box-shadow: -6px 0 20px rgba(102, 126, 234, 0.4);
  transition: none;
}

.toggle-button.dragging.hidden {
  right: -5px;
}

.button-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.toggle-icon {
  color: white;
  font-size: 18px;
  transition: transform 0.2s ease;
}

.toggle-button:hover:not(.dragging) .toggle-icon {
  transform: scale(1.1);
}

.drag-dots {
  display: flex;
  flex-direction: column;
  gap: 2px;
  opacity: 0.6;
}

.drag-dots .dot {
  width: 3px;
  height: 3px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.toggle-button:hover:not(.dragging) .drag-dots .dot {
  background: white;
  transform: scale(1.2);
}

.toggle-button.dragging .drag-dots .dot {
  background: white;
  animation: dragPulse 0.6s ease-in-out infinite alternate;
}

@keyframes dragPulse {
  from {
    opacity: 0.6;
    transform: scale(1);
  }
  to {
    opacity: 1;
    transform: scale(1.3);
  }
}

.ai-image-container {
  margin-top: 12px;
  max-width: 100%;
  display: flex;
  justify-content: center;
}

.ai-generated-image {
  max-width: 100%;
  height: auto;
  margin-top: 12px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(80, 120, 255, 0.12);
}

.charts-container {
  margin-top: 16px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.chart-container {
  text-align: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 16px;
  border: 1px solid #e9ecef;
}

.chart-image {
  max-width: 100%;
  height: auto;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  background: white;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.chart-image:hover {
  transform: scale(1.02);
}

.streaming-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.dot {
  width: 6px;
  height: 6px;
  background: #666;
  border-radius: 50%;
  animation: blink 1.4s infinite ease-in-out both;
}

@keyframes blink {
  0%, 80%, 100% { transform: scale(1); opacity: 0.4; }
  40% { transform: scale(1.2); opacity: 1; }
}
.if-create-image-label {
  color: #666;
  font-size: 14px;
  margin-right: 16px;
  font-weight: normal;
}
.if-create-image {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 8px;
  /* padding: 0 16px; */
}
.left-container {
  display: flex;
  align-items: center;
}
.right-container {
  display: flex;
  align-items: center;
}
:deep(.chat-input) textarea {
  resize: none !important;
}
.ai-chat-container {
  position: fixed;
  top: 0;
  right: 0;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #e0e7ff 0%, #f0fdfa 100%);
  box-shadow: -4px 0 24px 0 rgba(80, 120, 255, 0.08);
  border-left: none;
  z-index: 100;
  min-width: 260px;
  box-sizing: border-box;
  transition: width 0.1s;
}
.resize-handle {
  position: absolute;
  left: -4px;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
  z-index: 10;
  background: transparent;
}
.chat-header {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  height: 64px;
  font-size: 20px;
  font-weight: bold;
  background: linear-gradient(90deg, #ffffff 0%, #0accda 100%);
  color: #fff;
  border-radius: 0 0 24px 24px;
  padding: 0 16px;
  box-shadow: 0 2px 8px rgba(80, 120, 255, 0.08);
  letter-spacing: 2px;
}

/* 清除历史按钮特殊效果 */
.clear-history-btn::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.1));
  border-radius: 14px;
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.clear-history-btn:hover::before {
  opacity: 1;
}
.ai-icon {
  font-size: 26px;
  margin-right: 10px;
}
.selected-tables-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.selected-table-tag {
  display: inline-flex;
  align-items: center;
  background: #e0f3ff;
  color: #409eff;
  border-radius: 12px;
  padding: 2px 10px;
  font-size: 14px;
  transition: all 0.2s ease;
  position: relative;
}

.selected-table-tag:hover {
  background: #cce7ff;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

/* 默认表格样式 */
.selected-table-tag.is-default {
  background: #f0f9ff;
  border: 2px solid #409eff;
  font-weight: bold;
}

.selected-table-tag.is-default:hover {
  background: #e6f7ff;
}

/* 固定表格样式 */
.selected-table-tag.is-pinned {
  background: #fff7e6;
  border: 1px solid #faad14;
  color: #fa8c16;
}

.selected-table-tag.is-pinned:hover {
  background: #fff1b8;
}

/* 固定按钮样式 */
.pin-icon {
  cursor: pointer;
  margin-right: 4px;
  font-size: 12px;
  transition: all 0.2s ease;
}

.pin-icon:hover {
  transform: scale(1.2);
}

.selected-table-tag.is-pinned .pin-icon {
  color: #fa8c16;
}

/* 表名样式 */
.table-name {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 默认指示器样式 */
.default-indicator {
  font-size: 10px;
  background: #52c41a;
  color: white;
  padding: 1px 4px;
  border-radius: 8px;
  font-weight: normal;
}
/* 通用功能按钮样式 */
.at-btn,
.template-btn {
  background: #f4f8ff;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  padding: 0;
  cursor: pointer !important;
  pointer-events: auto !important;
  z-index: 10;
  position: relative;
}

.at-btn {
  margin-right: 0px;
  color: #409eff;
  font-size: 18px;
  box-shadow: 0 2px 8px rgba(80,120,255,0.08);
}

.at-btn:hover {
  background: #e0f3ff;
  color: #1976d2;
  box-shadow: 0 4px 16px rgba(80,120,255,0.15);
}

.template-btn {
  margin-right: 2px;
  color: #6366f1;
  font-size: 16px;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.08);
}

.template-btn:hover {
  background: #e0f3ff;
  color: #4f46e5;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.15);
  transform: translateY(-1px);
}

.template-btn.active {
  background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
  color: #fff;
  box-shadow: 0 4px 16px rgba(99, 102, 241, 0.25);
}

.template-btn.active:hover {
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  color: #fff;
}
.chat-body {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  scroll-behavior: smooth;
}
.chat-message {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  width: 100%;
  box-sizing: border-box;
}
.chat-message.user {
  align-items: flex-end;
}
.bubble {
  position: relative;
  max-width: 60% !important;
  min-width: 120px;
  padding: 8px 14px;
  border-radius: 16px;
  font-size: 14px;
  line-height: 1.5;
  box-shadow: 0 1px 4px rgba(80, 120, 255, 0.06);
  word-break: break-word;
  transition: all 0.2s ease;
  margin-bottom: 2px;
}
.chat-message.user .bubble {
  background: linear-gradient(90deg, #6366f1 0%, #06b6d4 100%);
  color: #fff;
  border-bottom-right-radius: 4px;
}
.chat-message.ai .bubble {
  background: #fff;
  color: #333;
  border-bottom-left-radius: 4px;
}
.chat-footer {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  padding: 18px 10px;
  border-top: 1px solid #e5e7eb;
  background: transparent;
}
.chat-input {
  flex: 1;
}

.chat-input :deep(.el-input__wrapper) {
  border-radius: 24px;
  box-shadow: 0 2px 8px rgba(80, 120, 255, 0.06);
  background: #fff;
  transition: box-shadow 0.2s;
  resize: none;
}
.chat-input :deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 4px 16px rgba(80, 120, 255, 0.12);
  resize: none;
}
/* 通用操作按钮样式 */
.send-button,
.stop-button {
  min-width: 33px;
  height: 33px;
  border-radius: 50%;
  color: #fff;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  transition: all 0.2s ease;
  padding: 0;
}

.send-button {
  background: linear-gradient(135deg, #6366f1 0%, #06b6d4 100%);
  box-shadow: 0 2px 8px rgba(80, 120, 255, 0.12);
}

.send-button:hover {
  background: linear-gradient(135deg, #06b6d4 0%, #6366f1 100%);
  box-shadow: 0 4px 16px rgba(80, 120, 255, 0.18);
}

.send-button:disabled {
  background: #d1d5db;
  cursor: not-allowed;
  box-shadow: none;
}

.stop-button {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
  box-shadow: 0 2px 8px rgba(239, 68, 68, 0.12);
  animation: pulse 2s infinite;
}

.stop-button:hover {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  box-shadow: 0 4px 16px rgba(239, 68, 68, 0.25);
  transform: scale(1.05);
}

@keyframes pulse {
  0% {
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.12);
  }
  50% {
    box-shadow: 0 4px 16px rgba(239, 68, 68, 0.25);
  }
  100% {
    box-shadow: 0 2px 8px rgba(239, 68, 68, 0.12);
  }
}

.import-bubble {
  position: absolute;
  left: 22%;
  bottom: 80px; /* 距离底部的高度，可根据chat-footer高度微调 */
  transform: translateX(-50%);
  background: #fff;
  color: #333;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(80,120,255,0.12);
  padding: 10px 20px;
  font-size: 14px;
  z-index: 200;
  display: flex;
  align-items: center;
  white-space: nowrap;
}
.bubble-actions {
  display: flex;
  align-items: center;
}

/* 表选择器样式 */
.table-selector-content {
  max-height: 300px;
  overflow-y: auto;
}

.data-tip {
  background: #f0f9ff;
  color: #0369a1;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  margin-bottom: 16px;
  border-left: 3px solid #0ea5e9;
}

.table-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.table-item {
  padding: 8px 12px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.table-item:hover {
  background: #f5f5f5;
  border-color: #409eff;
}

.no-tables {
  text-align: center;
  padding: 40px 20px;
  color: #999;
}

.no-tables .hint {
  font-size: 12px;
  color: #ccc;
  margin-top: 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toggle-button {
    width: 36px;
    height: 70px;
    border-radius: 18px 0 0 18px;
  }

  .toggle-icon {
    font-size: 16px;
  }

  .drag-dots .dot {
    width: 2px;
    height: 2px;
  }

  .ai-chat-container {
    min-width: 240px;
  }

  .chat-header {
    height: 56px;
    padding: 0 16px;
  }
}

/* 触摸设备支持 */
@media (hover: none) and (pointer: coarse) {
  .toggle-button {
    /* 在触摸设备上增大触摸区域 */
    width: 44px;
    height: 88px;
  }

  .toggle-button:hover {
    /* 禁用触摸设备上的悬停效果 */
    transform: none;
    box-shadow: -2px 0 10px rgba(102, 126, 234, 0.2);
  }

  .toggle-button.hidden:hover {
    right: -10px;
  }
}

</style>
