import { setDbToken, getDbToken } from '@/utils/request'

/**
 * AnalyseDB模块的DB Token管理工具
 */

// 初始化DB Token（从数据库连接成功后获取）
export function initDbToken(token) {
  if (token) {
    setDbToken(token)
    console.log('DB Token已设置:', token.substring(0, 10) + '...')
  }
}

// 获取当前DB Token
export function getCurrentDbToken() {
  return getDbToken()
}

// 清除DB Token
export function clearDbToken() {
  setDbToken('')
  console.log('DB Token已清除')
}

// 检查是否有有效的DB Token
export function hasValidDbToken() {
  const token = getDbToken()
  return token && token.length > 0
}

// 从数据库连接响应中提取并设置Token
export function handleDbConnectionResponse(response) {
  if (response && response.token) {
    initDbToken(response.token)
    return response.token
  }
  return null
}
