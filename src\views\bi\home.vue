<template>
  <div class="fullscreen-bg">
    <TopBar
      :top="top"
      :dynamic-time="currentTime"
      @nav-change="handleNavChange"
    />

    <div class="contect">
      <LeftSideBar
        :left="left"
      />
      <MiddleMap
        :middle="middle"
      />
      <RightBar
        :right="right"
      />
    </div>

  </div>
</template>

<script>
import {
  topMenu, leftTableOneMenu, leftTableTwoMenu, leftTableThreeMenu,
  pageMiddleButtonMenu, rightTableOneMenu, rightTableTwoMenu, rightTableThreeMenu
} from '@/api/dataPage/biApi.js'
import TopBar from '@/layout/biComponents/topBar.vue'
import LeftSideBar from '@/layout/biComponents/leftSideBar.vue'
import MiddleMap from '@/layout/biComponents/middleMap.vue'
import RightBar from '@/layout/biComponents/rightBar.vue'

export default {
  // 2. 注册组件
  components: {
    TopBar,
    LeftSideBar,
    MiddleMap,
    RightBar
  },
  data() {
    return {
      // 顶部资源
      top: {
        titleName: '',
        navList: []
      },
      left: {
        // 左侧边栏第一表
        leftTableOne: {},
        // 左侧边栏第二表
        leftTableTwo: {},
        // 左侧边栏第三表
        leftTableThree: {}
      },
      middle: {
        // 中间地图数据
        mapData: [
          { name: '北京', value: this.randomData(), id: '1' },
          { name: '天津', value: this.randomData(), id: '1' },
          { name: '上海', value: this.randomData(), id: '1' },
          { name: '重庆', value: this.randomData(), id: '1' },
          { name: '河北', value: this.randomData(), id: '1' },
          { name: '河南', value: this.randomData(), id: '1' },
          { name: '云南', value: this.randomData(), id: '1' },
          { name: '辽宁', value: this.randomData(), id: '1' },
          { name: '黑龙江', value: this.randomData(), id: '1' },
          { name: '湖南', value: this.randomData(), id: '1' },
          { name: '安徽', value: this.randomData(), id: '1' },
          { name: '山东', value: this.randomData(), id: '1' },
          { name: '新疆', value: this.randomData(), id: '1' },
          { name: '江苏', value: this.randomData(), id: '1' },
          { name: '浙江', value: this.randomData(), id: '1' },
          { name: '江西', value: this.randomData(), id: '1' },
          { name: '湖北', value: this.randomData(), id: '1' },
          { name: '广西', value: this.randomData(), id: '1' },
          { name: '甘肃', value: this.randomData(), id: '1' },
          { name: '山西', value: this.randomData(), id: '1' },
          { name: '内蒙古', value: this.randomData(), id: '1' },
          { name: '陕西', value: this.randomData(), id: '1' },
          { name: '吉林', value: this.randomData(), id: '1' },
          { name: '福建', value: this.randomData(), id: '1' },
          { name: '贵州', value: this.randomData(), id: '1' },
          { name: '广东', value: this.randomData(), id: '1' },
          { name: '青海', value: this.randomData(), id: '1' },
          { name: '西藏', value: this.randomData(), id: '1' },
          { name: '四川', value: this.randomData(), id: '1' },
          { name: '宁夏', value: this.randomData(), id: '1' },
          { name: '海南', value: this.randomData(), id: '1' },
          { name: '台湾', value: this.randomData(), id: '1' },
          { name: '香港', value: this.randomData(), id: '1' },
          { name: '澳门', value: this.randomData(), id: '1' },
          {
            name: '南海诸岛',
            value: this.randomData(), id: '1',
            itemStyle: {
              shadowOffsetY: 0
            }
          }
        ],
        // 中间底部列表集
        PageMiddleButton: []
      },

      right: {
        // 右侧边栏第一表
        rightTableOne: {},
        // 右侧边栏第二表
        rightTableTwo: {},
        // 右侧边栏第三表
        rightTableThree: {}
      },
      // 动态时间（示例）
      currentTime: new Date().toLocaleString(),
      user: this.$store.state.user.user,
      roles: this.$store.state.user.roles
    }
  },
  async mounted() {
    this.updateTime()
    const title = localStorage.getItem('title')
    await this.dataInit({ title: title })
  },
  beforeDestroy() {
    cancelAnimationFrame(this.rafId)
  },
  methods: {
    // 处理导航切换
    handleNavChange(index) {
      this.top.navList = this.top.navList.map((item, i) => ({
        ...item,
        active: i === index
      }))
    },
    formatTime() {
      const d = new Date()
      return `${d.getFullYear()}年
              ${(d.getMonth() + 1).toString().padStart(2, '0')}月
              ${d.getDate().toString().padStart(2, '0')}日
              ${d.getHours().toString().padStart(2, '0')}:
              ${d.getMinutes().toString().padStart(2, '0')}:
              ${d.getSeconds().toString().padStart(2, '0')}`
    },
    updateTime() {
      this.currentTime = this.formatTime()
      this.rafId = requestAnimationFrame(this.updateTime)
    },
    randomData() {
      return Math.round(Math.random() * 500)
    },
    getData() {
      const title = localStorage.getItem('title')
      switch (title) {
        case 'cailiao':
          break
      }
    },
    async dataInit(params) {
      try {
        this.top = await topMenu(params)
        // 确保数据结构正确
        if (!this.top.navList) {
          this.top.navList = []
        }
        const [one, two, three, four, five, six, seven] = await Promise.all([
          leftTableOneMenu(params),
          leftTableTwoMenu(params),
          leftTableThreeMenu(params),
          pageMiddleButtonMenu(params),
          rightTableOneMenu(params),
          rightTableTwoMenu(params),
          rightTableThreeMenu(params)
        ])

        // 统一更新数据
        this.left = {
          leftTableOne: one,
          leftTableTwo: two,
          leftTableThree: three
        }
        this.middle = {
          PageMiddleButton: four
        }
        this.right = {
          rightTableOne: five,
          rightTableTwo: six,
          rightTableThree: seven
        }
      } catch (e) {
        console.error('接口请求失败:', e)
        this.top.navList = [] // 保证数据结构
      }
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.fullscreen-bg {
  /* 全屏设置 */
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  /* 背景设置 */
  background-color: #020911;
  /* 内容布局（可选） */
  display: flex;
  flex-direction: column;
  overflow: hidden;

}

.contect {
  // height: calc(100vh - 60px); /* 假设顶部栏高度60px */
  // overflow-y: auto;

  flex: 1;
  width: 100%;
  overflow-y: auto;  // 必须保留滚动功能
  -webkit-overflow-scrolling: touch; // 优化移动端滚动
  /* 隐藏所有滚动条 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
  /* WebKit浏览器隐藏滚动条 */
  &::-webkit-scrollbar {
    display: none; /* Chrome/Safari/Opera */
    width: 0 !important;
    height: 0 !important;
    background: transparent;
  }
}
</style>
