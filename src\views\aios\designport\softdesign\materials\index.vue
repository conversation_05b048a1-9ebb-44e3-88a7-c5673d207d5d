<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="提交客户日期">
                  <el-date-picker v-model="form.submissionDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划完成时间">
                  <el-date-picker v-model="form.plannedCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddr" style="width: 100%" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="样板准备开始时间">
                  <el-date-picker v-model="form.samplepreparationStartTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="样板准备完成时间">
                  <el-date-picker v-model="form.samplePreparationCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="商务跟单">
              <el-input v-model="form.businessdocumentary" style="width: 100%" />
            </el-form-item>
            <!-- <el-form-item label="服务类型">
              <el-input v-model="form.serviceType" :rows="2" type="textarea" style="width: 100%" />
            </el-form-item> -->
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" style="width: 100%" />
            </el-form-item>
            <el-form-item label="异常说明">
              <el-input v-model="form.exceptionDescription" :rows="2" type="textarea" style="width: 100%" />
            </el-form-item>

            <!-- 物料样本子表单 -->
            <el-divider content-position="left"><i class="el-icon-box" /> 物料样本列表</el-divider>
            <div class="table-container">
              <el-table :data="materialSamplesList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="材料名称/型号" min-width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialNamemodelNumber" placeholder="请输入材料名称/型号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="品牌" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.brand" placeholder="请输入品牌" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="样板规格" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialSamples" placeholder="请输入样板规格" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="样板数量" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.numberAterialSamples" placeholder="请输入样板数量" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="备注" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.notes" placeholder="请输入备注" type="textarea" :rows="2" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveMaterialSample(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddMaterialSample">新增样本</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>
            <el-form-item label="样板实物图">
              <general-file-upload
                v-model="form.samplePhoto"
                :field-name="'samplePhoto'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('samplePhoto', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
            <el-form-item label="样板示意图">
              <general-file-upload
                v-model="form.sampleDiagram"
                :field-name="'sampleDiagram'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('sampleDiagram', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjRzcl:edit','tumaiSjRzcl:del','tumaiSjRzcl:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudTumaiSjRzcl from '@/api/aios/designport/softdesign/tumaiSjRzcl'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, submissionDate: null,
  plannedCompletionTime: null, sampleDiagram: null, samplePhoto: null, projectNumber: null,
  projectNature: null, projectAddr: null, projectName: null, serviceType: null,
  projectOverview: null, exceptionDescription: null, uid: null, optdt: null, optid: null,
  optname: null, applydt: null, explain: null, status: null, isturn: null,
  typeOfService: null, projectAddress: null, samplepreparationStartTime: null,
  projectid: null, projectDeliveryPeriod: null, tabDate: null, businessdocumentary: null,
  createtime: null, samplePreparationCompletionTime: null,
  // 子表单字段
  materialSamples: '[]'
}
export default {
  name: 'TumaiSjRzcl',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status'],
  cruds() {
    return CRUD({ title: '软装（材料样板）', url: 'api/tumaiSjRzcl', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjRzcl }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjRzcl:add'],
        edit: ['admin', 'tumaiSjRzcl:edit'],
        del: ['admin', 'tumaiSjRzcl:del'],
        view: ['admin', 'tumaiSjRzcl:edit', 'tumaiSjRzcl:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 材料样本列表
      materialSamplesList: [],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 5,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      },
      // 文件字段映射
      fileFields: ['samplePhoto', 'sampleDiagram'],
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'samplePhoto',
        'sampleDiagram'
      ])
      // 初始化材料样本列表
      this.initMaterialSamplesList()
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'samplePhoto',
        'sampleDiagram'
      ])
      this.initMaterialSamplesList()
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['samplePhoto', 'sampleDiagram'])
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.samplePhoto = '[]'
      this.form.sampleDiagram = '[]'

      this.materialSamplesList = []

      // 添加一个空的材料样本记录
      this.handleAddMaterialSample()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始表单数据:', JSON.stringify({
          samplePhoto: this.form.samplePhoto,
          sampleDiagram: this.form.sampleDiagram
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'samplePhoto',
          'sampleDiagram'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          samplePhoto: this.form.samplePhoto,
          sampleDiagram: this.form.sampleDiagram
        }))

        // 将处理后的材料样本数据同步回表单
        this.updateFormMaterialSamples()

        console.log('处理后的表单数据:', this.form.materialSamples)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化材料样本列表
    initMaterialSamplesList() {
      try {
        this.materialSamplesList = this.form.materialSamples ? JSON.parse(this.form.materialSamples) : []
        if (!Array.isArray(this.materialSamplesList)) {
          this.materialSamplesList = []
        }
      } catch (e) {
        console.error('解析材料样本数据失败:', e)
        this.materialSamplesList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.materialSamplesList.length === 0) {
        this.handleAddMaterialSample()
      }
    },

    // 更新表单中的材料样本数据
    updateFormMaterialSamples() {
      this.form.materialSamples = JSON.stringify(this.materialSamplesList)
    },

    // 添加材料样本
    handleAddMaterialSample() {
      this.materialSamplesList.push({
        materialNamemodelNumber: null,
        brand: null,
        materialSamples: null,
        numberAterialSamples: null,
        notes: null
      })
    },

    // 移除材料样本
    handleRemoveMaterialSample(index) {
      this.materialSamplesList.splice(index, 1)
      if (this.materialSamplesList.length === 0) {
        this.handleAddMaterialSample()
      }
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },
    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}
</style>
