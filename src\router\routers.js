import Vue from 'vue'
import Router from 'vue-router'
import Layout from '../layout/index'
import store from '@/store'

Vue.use(Router)

var biTitle = localStorage.getItem('titleCN')

export const constantRouterMap = [
  { path: '/bi',
    meta: { title: biTitle + '数据可视化', noCache: true },
    component: (resolve) => require(['@/views/bi/home'], resolve),
    beforeEnter: (to, from, next) => {
      const roles = store.state.user.roles // 获取用户角色
      if (roles.includes('menu:list') || roles.includes('admin')) {
        next() // 用户有权限，继续访问
      } else {
        next('/404') // 用户无权限，跳转到 404 页面
      }
    },
    hidden: true
  },
  { path: '/AnalyseDB',
    meta: { title: 'AI数据分析', noCache: true },
    component: (resolve) => require(['@/views/AnalyseDB/home'], resolve),
    beforeEnter: (to, from, next) => {
      const roles = store.state.user.roles // 获取用户角色
      if (roles.includes('menu:list') || roles.includes('admin')) {
        next() // 用户有权限，继续访问
      } else {
        next('/404') // 用户无权限，跳转到 404 页面
      }
    },
    hidden: true
  },
  { path: '/login',
    meta: { title: '登录', noCache: true },
    component: (resolve) => require(['@/views/login'], resolve),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/features/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/features/401'], resolve),
    hidden: true
  },
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path*',
        component: (resolve) => require(['@/views/features/redirect'], resolve)
      }
    ]
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        component: (resolve) => require(['@/views/home'], resolve),
        name: 'Dashboard',
        meta: { title: '首页', icon: 'index', affix: true, noCache: true }
      }
    ]
  },
  {
    path: '/user',
    component: Layout,
    hidden: true,
    redirect: 'noredirect',
    children: [
      {
        path: 'center',
        component: (resolve) => require(['@/views/system/user/center'], resolve),
        name: '个人中心',
        meta: { title: '个人中心' }
      }
    ]
  }
]

export default new Router({
  // mode: 'hash',
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRouterMap
})
