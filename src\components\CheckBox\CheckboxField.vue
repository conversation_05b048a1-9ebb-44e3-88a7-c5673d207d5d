<template>
  <div class="checkbox-field">
    <el-form-item>
      <template slot="label">
        <div class="field-label-container">
          <span>{{ label }}</span>
          <el-tooltip v-if="isAdmin" content="管理选项" placement="top">
            <i
              class="el-icon-setting option-manager-icon"
              @click="showDialog"
            />
          </el-tooltip>
        </div>
      </template>

      <!-- 复选框选项组 -->
      <div class="checkbox-container">
        <template v-if="options.length > 0">
          <el-checkbox
            v-for="option in options"
            :key="option"
            v-model="selectedValues[option]"
            @change="handleChange"
          >{{ option }}</el-checkbox>
        </template>

        <!-- 没有选项时的提示 -->
        <template v-else>
          <el-link
            v-if="isAdmin"
            type="primary"
            icon="el-icon-plus"
            class="no-options"
            @click="showDialog"
          >暂无可选项，点击添加</el-link>
          <span v-else class="no-options-readonly">暂无可选项</span>
        </template>
      </div>

      <!-- 选项管理对话框 -->
      <el-dialog
        :title="`${label}选项管理`"
        :visible.sync="dialogVisible"
        width="600px"
        append-to-body
      >
        <!-- 批量操作头部 -->
        <div v-if="options.length > 0" class="options-header">
          <el-checkbox
            v-model="checkAll"
            :indeterminate="isIndeterminate"
            @change="handleCheckAllChange"
          >全选</el-checkbox>
          <el-button
            type="danger"
            size="mini"
            :disabled="selectedForDelete.length === 0"
            @click="batchDeleteSelected"
          >批量删除({{ selectedForDelete.length }})</el-button>
        </div>

        <div class="option-list">
          <div v-for="(option, index) in options" :key="index" class="option-item">
            <div class="option-content">
              <!-- 删除选择框 -->
              <el-checkbox v-model="deleteSelection[option]" @change="handleSelectionChange" />
              <span class="option-text">{{ option }}</span>
            </div>
            <el-button
              type="danger"
              size="mini"
              icon="el-icon-delete"
              circle
              @click="deleteOption(option)"
            />
          </div>
          <div v-if="options.length === 0" class="no-data">
            暂无选项数据
          </div>
        </div>

        <div class="add-options">
          <el-input
            v-model="newOptions"
            type="textarea"
            :rows="3"
            placeholder="请输入要添加的选项，多个选项用(,)分隔。按Ctrl+Enter快速添加"
            @keydown.ctrl.enter.native.prevent="addOptions"
          />
          <el-button type="primary" style="margin-top: 15px" @click="addOptions">批量添加</el-button>
        </div>

        <div slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">关闭</el-button>
        </div>
      </el-dialog>
    </el-form-item>
  </div>
</template>

<script>
import { getOptionsByFieldName, batchAddOptions, deleteOption } from '@/api/aios/shop/tumaiOaXz'
import isCurrentUserAdmin from '@/utils/adminPermissionChecker'

export default {
  name: 'CheckboxField',
  props: {
    label: {
      type: String,
      required: true
    },
    fieldName: {
      type: String,
      required: true
    },
    value: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      options: [],
      selectedValues: {},
      dialogVisible: false,
      newOptions: '',
      // 批量删除相关数据
      deleteSelection: {},
      checkAll: false,
      isIndeterminate: false
    }
  },
  computed: {
    // 计算当前选择要删除的选项
    selectedForDelete() {
      return Object.keys(this.deleteSelection).filter(key => this.deleteSelection[key])
    },
    // 检查当前用户是否为管理员
    isAdmin() {
      return isCurrentUserAdmin()
    }
  },
  watch: {
    value: {
      immediate: true,
      handler(newVal) {
        this.initSelectedValues(newVal)
      }
    },
    // 监听对话框打开关闭，重置批量删除状态
    dialogVisible(val) {
      if (val) {
        // 对话框打开时初始化删除选择状态
        this.initDeleteSelection()
      } else {
        // 对话框关闭时重置删除选择状态
        this.resetDeleteSelection()
      }
    }
  },
  created() {
    this.loadOptions()
  },
  methods: {
    // 加载选项数据
    async loadOptions() {
      try {
        const response = await getOptionsByFieldName(this.fieldName)

        // 处理后端返回的数据
        let options = []
        if (response && response.length > 0) {
          // 检查是否为带逗号的字符串
          if (response.length === 1 && response[0].includes(',')) {
            // 将字符串拆分为数组
            options = response[0].split(',').map(item => item.trim())
          } else {
            // 如果已经是数组，直接使用
            options = response
          }
        }

        this.options = options
        this.initSelectedValues(this.value)
      } catch (error) {
        console.error(`加载${this.label}选项失败:`, error)
        this.$message.error(`加载${this.label}选项失败`)
      }
    },

    // 初始化选中值
    initSelectedValues(valueStr) {
      // 重置选中状态
      this.selectedValues = {}

      if (valueStr) {
        const selectedItems = valueStr.split(',')
        this.options.forEach(option => {
          this.selectedValues[option] = selectedItems.includes(option)
        })
      }
    },

    // 初始化删除选择状态
    initDeleteSelection() {
      // 创建新对象而非清空旧对象，确保响应式更新
      const newSelection = {}

      // 遍历所有选项，设置初始状态
      this.options.forEach(option => {
        // 使用 Vue 的 $set 方法确保响应式
        this.$set(newSelection, option, false)
      })

      // 整体替换选择状态对象
      this.deleteSelection = newSelection

      // 重置全选和半选状态
      this.checkAll = false
      this.isIndeterminate = false

      console.log('删除选择状态已初始化，选项数量:', this.options.length)
    },

    // 重置删除选择状态
    resetDeleteSelection() {
      this.deleteSelection = {}
      this.checkAll = false
      this.isIndeterminate = false
    },

    // 处理复选框变更
    handleChange() {
      const selected = Object.keys(this.selectedValues)
        .filter(key => this.selectedValues[key])
        .join(',')

      this.$emit('input', selected)
      this.$emit('change', selected)
    },

    // 显示管理对话框
    showDialog() {
      this.dialogVisible = true
    },

    // 添加选项
    async addOptions() {
      if (!this.newOptions.trim()) {
        this.$message.warning('请输入要添加的选项')
        return
      }

      try {
        const dto = {
          fieldName: this.fieldName,
          options: this.newOptions
        }

        await batchAddOptions(dto)
        this.$message.success('添加成功')
        this.newOptions = ''
        await this.loadOptions()
        this.initDeleteSelection() // 重新初始化删除选择状态
      } catch (error) {
        console.error('添加选项失败:', error)
        this.$message.error('添加选项失败')
      }
    },

    // 删除选项
    async deleteOption(option) {
      this.$confirm(`确认删除选项"${option}"?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          await deleteOption(this.fieldName, option)
          this.$message.success('删除成功')
          await this.loadOptions()
          this.initDeleteSelection() // 重新初始化删除选择状态
        } catch (error) {
          console.error('删除选项失败:', error)
          this.$message.error('删除选项失败')
        }
      }).catch(() => {})
    },

    // 处理选择变更
    handleSelectionChange() {
      const values = Object.values(this.deleteSelection)
      const allSelected = values.length > 0 && values.every(v => v)
      const someSelected = values.some(v => v)

      this.checkAll = allSelected
      this.isIndeterminate = someSelected && !allSelected
    },

    // 处理全选
    handleCheckAllChange(val) {
      // 先清空状态
      this.deleteSelection = {}

      // 重新设置每个选项的状态
      this.options.forEach(option => {
        this.$set(this.deleteSelection, option, val)
      })

      // 手动触发更新
      this.$nextTick(() => {
        console.log('全选状态:', val)
        console.log('选择的项目数量:', this.selectedForDelete.length)
        console.log('选择状态:', { ...this.deleteSelection })
        this.isIndeterminate = false
      })
    },
    // 批量删除
    // 批量删除选中项
    async batchDeleteSelected() {
      // 添加调试日志
      console.log('准备批量删除，选中的选项:', this.selectedForDelete)
      console.log('选择状态对象:', { ...this.deleteSelection })

      if (this.selectedForDelete.length === 0) {
        this.$message.warning('请选择要删除的选项')
        return
      }

      this.$confirm(`确认删除选中的 ${this.selectedForDelete.length} 个选项?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        try {
          // 创建一个副本，避免在删除过程中数组内容变化
          const itemsToDelete = [...this.selectedForDelete]
          console.log('准备删除的项目:', itemsToDelete)

          // 改为串行删除，而非使用 Promise.all 并行删除
          let successCount = 0
          for (const option of itemsToDelete) {
            try {
              console.log(`开始删除: ${option}`)
              await deleteOption(this.fieldName, option)
              successCount++
              console.log(`成功删除: ${option}`)
            } catch (err) {
              console.error(`删除 ${option} 失败:`, err)
            }
          }

          this.$message.success(`成功删除 ${successCount} 个选项`)

          // 重置选择状态
          this.resetDeleteSelection()

          // 重新加载选项
          await this.loadOptions()
        } catch (error) {
          console.error('批量删除过程出错:', error)
          this.$message.error('批量删除选项失败')
        }
      }).catch(() => {
        console.log('用户取消删除操作')
      })
    }
  }
}
</script>

<style scoped>
.checkbox-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 3px;
}

.checkbox-container .el-checkbox {
  margin-right: 10px;
  margin-bottom: 3px;
}

.field-label-container {
  display: flex;
  align-items: center;
}

.option-manager-icon {
  margin-left: 3px;
  font-size: 12px;
  color: #909399;
  cursor: pointer;
  transition: color 0.3s;
}

.option-manager-icon:hover {
  color: #409EFF;
}

.no-options {
  color: #909399;
  font-size: 13px;
  margin-bottom: 3px;
}

.options-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3px;
  padding-bottom: 3px;
  border-bottom: 1px solid #EBEEF5;
}

.option-list {
  max-height: 220px;
  overflow-y: auto;
  margin-bottom: 8px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  padding: 3px;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2px 4px;
  border-bottom: 1px solid #EBEEF5;
}

.option-content {
  display: flex;
  align-items: center;
}

.option-text {
  margin-left: 5px;
  font-size: 13px;
}

.option-item:last-child {
  border-bottom: none;
}

.no-data {
  text-align: center;
  color: #909399;
  padding: 8px 0;
}

.add-options {
  margin-top: 8px;
}

/* 额外添加表单项样式优化 */
.checkbox-field >>> .el-form-item__content {
  line-height: 26px;
}

.checkbox-field >>> .el-form-item {
  margin-bottom: 3px;
}

.add-options >>> .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.add-options >>> .el-textarea__inner {
  padding: 3px 5px;
}

.add-options .el-button {
  margin-top: 8px !important;
  height: 28px;
  padding: 5px 12px;
  font-size: 12px;
}
</style>
