<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.itemNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.entryName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <!-- 项目基本信息 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 楼盘信息 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 楼盘信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘ID" prop="loupid">
                  <el-input v-model="form.loupid" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="归属楼盘" prop="belongstoThebuilding">
                  <el-input v-model="form.belongstoThebuilding" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="归属楼栋">
                  <el-input v-model="form.belongtoThemuilding" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 户型详情 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-house" /> 户型详情</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="户型名称">
                  <el-input v-model="form.familyName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="户型编号">
                  <el-input v-model="form.doorNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="户型格局" prop="doorPattern">
                  <el-select v-model="form.doorPattern" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.huxing_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="户型类型" prop="doorType">
                  <el-select v-model="form.doorType" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.door_Type"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑面积(㎡)" prop="buildingareaOfhouseType">
                  <el-input v-model="form.buildingareaOfhouseType">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总户数">
                  <el-input v-model="form.totalnumberOfhouseType" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 价格详情 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-money" /> 价格详情</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="参考均价">
                  <el-input v-model="form.referenceAveragePrice">
                    <template slot="append">元/平</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="总价">
                  <el-input v-model="form.totalPrice">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabulationDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

          </div>

          <!-- 图纸文档 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-picture" /> 图纸文档</el-divider>
            <el-progress v-if="uploadProgress > 0 && uploadProgress < 100" :percentage="uploadProgress" />

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="施工图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.constructionDrawing"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="平面图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.ichnography"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.architecturalDrawing"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="结构图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.assumptionDiagram"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="排水图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.drainagePlan"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="户型图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.floorPlan"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="效果图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.renderings"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="实景图">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.realScenes"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="doorPattern" label="户型格局">
          <template slot-scope="scope">
            {{ dict.label.huxing_structure[scope.row.doorPattern] }}
          </template>
        </el-table-column>
        <el-table-column prop="buildingareaOfhouseType" label="户型建筑面积(㎡)" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.dept_status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiOaHuxing:edit','tumaiOaHuxing:del','tumaiOaHuxing:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaHuxing from '@/api/aios/shop/tumaiOaHuxing'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, assumptionDiagram: null, floorPlan: null, remarks: null, itemNumber: null, entryName: null, realScenes: null, tabulationDate: null, constructionDrawing: null, architecturalDrawing: null, renderings: null, drainagePlan: null, budget: null, doorPattern: null, ichnography: null, uid: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null, loupid: null, projectNumber: null, projectName: null, doorNumber: null, familyName: null, referenceAveragePrice: null, buildingNumber: null, doorType: null, belongtoThemuilding: null, buildingareaOfhouseType: null, totalnumberOfhouseType: null, belongstoThebuilding: null, projectid: null, createtime: null, totalPrice: null }
export default {
  name: 'TumaiOaHuxing',
  components: { pagination, crudOperation, rrOperation, FileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['huxing_structure', 'dept_status', 'door_Type'],
  cruds() {
    return CRUD({ title: '户型资料', url: 'api/shop/tumaiOaHuxing', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaHuxing }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaHuxing:add'],
        edit: ['admin', 'tumaiOaHuxing:edit'],
        del: ['admin', 'tumaiOaHuxing:del'],
        view: ['admin', 'tumaiOaHuxing:edit', 'tumaiOaHuxing:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 进度条
      uploadProgress: 0,
      // 上传文件映射字段名
      fileFieldMap: {
        'constructionDrawing': '施工图',
        'ichnography': '平面图',
        'architecturalDrawing': '建筑图',
        'assumptionDiagram': '结构图',
        'drainagePlan': '排水图',
        'floorPlan': '户型图',
        'renderings': '效果图',
        'realScenes': '实景图'
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        loupid: [
          { required: true, message: '楼盘id不能为空', trigger: 'blur' },
          // 仅允许正整数
          { pattern: /^[1-9]\d*$/, message: '必须输入正整数', trigger: 'blur' },
          // 自定义验证不超过32767
          { validator: (rule, value, callback) => {
            if (parseInt(value) > 32767) {
              callback(new Error('不能超过32767'))
            } else {
              callback()
            }
          },
          trigger: 'blur'
          }
        ],
        doorPattern: [
          { required: true, message: '户型格局不能为空', trigger: 'blur' }
        ],
        doorType: [
          { required: true, message: '户型类型不能为空', trigger: 'blur' }
        ],
        buildingareaOfhouseType: [
          { required: true, message: '户型建筑面积(㎡)不能为空', trigger: 'blur' }
        ],
        belongstoThebuilding: [
          { required: true, message: '归属楼盘不能为空', trigger: 'blur' }
        ]
      },

      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'itemNumber', display_name: '项目编号' },
        { key: 'entryName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'constructionDrawing',
        'ichnography',
        'architecturalDrawing',
        'assumptionDiagram',
        'drainagePlan',
        'floorPlan',
        'renderings',
        'realScenes'
      ])
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, [
        'constructionDrawing',
        'ichnography',
        'architecturalDrawing',
        'assumptionDiagram',
        'drainagePlan',
        'floorPlan',
        'renderings',
        'realScenes'])
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.constructionDrawing = '[]'
      this.form.ichnography = '[]'
      this.form.architecturalDrawing = '[]'
      this.form.assumptionDiagram = '[]'
      this.form.drainagePlan = '[]'
      this.form.floorPlan = '[]'
      this.form.renderings = '[]'
      this.form.realScenes = '[]'
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'constructionDrawing',
          'ichnography',
          'architecturalDrawing',
          'assumptionDiagram',
          'drainagePlan',
          'floorPlan',
          'renderings',
          'realScenes'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          constructionDrawing: this.form.constructionDrawing,
          ichnography: this.form.ichnography,
          architecturalDrawing: this.form.architecturalDrawing,
          assumptionDiagram: this.form.assumptionDiagram,
          drainagePlan: this.form.drainagePlan,
          floorPlan: this.form.floorPlan,
          renderings: this.form.renderings,
          realScenes: this.form.realScenes
        }))

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      let result = false

      try {
        // 主表单文件处理
        result = this.handleAttachmentChange(event, row)
        console.log('主表单处理后数据:', JSON.stringify({
          constructionDrawing: this.form.constructionDrawing,
          ichnography: this.form.ichnography,
          architecturalDrawing: this.form.architecturalDrawing,
          assumptionDiagram: this.form.assumptionDiagram,
          drainagePlan: this.form.drainagePlan,
          floorPlan: this.form.floorPlan,
          renderings: this.form.renderings,
          realScenes: this.form.realScenes
        }))

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01)
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
