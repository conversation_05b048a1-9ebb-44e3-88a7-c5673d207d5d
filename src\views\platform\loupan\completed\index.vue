<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">楼盘编号</label>
        <el-input v-model="query.baseSericnum" clearable placeholder="楼盘编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">楼盘名称</label>
        <el-input v-model="query.buildingName" clearable placeholder="楼盘名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">物业类型</label>
        <el-input v-model="query.propertyType" clearable placeholder="物业类型" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">开发商名称</label>
        <el-input v-model="query.corporateName" clearable placeholder="开发商名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">楼盘状态</label>
        <el-input v-model="query.buildingPresentSituation" clearable placeholder="楼盘状态" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘编号">
            <el-input v-model="form.baseSericnum" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘名称">
            <el-input v-model="form.buildingName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="物业类型">
            <el-input v-model="form.propertyType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="产权年限">
            <el-input v-model="form.titleFixedNumberOfYear" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="品牌">
            <el-input v-model="form.brand" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘地址">
            <el-input v-model="form.buildingAddress" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="省">
            <el-input v-model="form.province" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="市">
            <el-input v-model="form.city" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="区域">
            <el-input v-model="form.inTheArea" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="装修标准">
            <el-input v-model="form.decorationStandard" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="建筑类型">
            <el-input v-model="form.constructionType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="参考均价（元/㎡）">
            <el-input v-model="form.referenceAveragePrice" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开发商名称">
            <el-input v-model="form.corporateName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘状态">
            <el-input v-model="form.buildingPresentSituation" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="销售状态">
            <el-input v-model="form.salesStatus" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="正在销售楼栋">
            <el-input v-model="form.sellingBuild" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="销售户型">
            <el-input v-model="form.sellHouseType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="最新开盘日期">
            <el-input v-model="form.theLatestOpeningDate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="最近交房日期">
            <el-input v-model="form.recentDeliveryDate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="建筑面积㎡">
            <el-input v-model="form.coveredArea" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="工程进度">
            <el-input v-model="form.jobSchedule" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘状况">
            <el-input v-model="form.loupanStatus" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入住率">
            <el-input v-model="form.occupancyRate" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="规划楼栋">
            <el-input v-model="form.planningBuilding" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="规划户数(户)">
            <el-input v-model="form.numberOfPlanningHouseholds" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="物业公司">
            <el-input v-model="form.propertyManagementCompany" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表人">
            <el-input v-model="form.createBy" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表日期">
            <el-input v-model="form.createTime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别" />
        <el-table-column prop="baseSericnum" label="楼盘编号" />
        <el-table-column prop="buildingName" label="楼盘名称" />
        <el-table-column prop="propertyType" label="物业类型" />
        <el-table-column prop="brand" label="品牌" />
        <el-table-column prop="titleFixedNumberOfYear" label="产权年限" />
        <el-table-column prop="buildingAddress" label="楼盘地址" />
        <el-table-column prop="province" label="省" />
        <el-table-column prop="city" label="市" />
        <el-table-column prop="inTheArea" label="区域" />
        <el-table-column prop="decorationStandard" label="装修标准" />
        <el-table-column prop="constructionType" label="建筑类型" />
        <el-table-column prop="referenceAveragePrice" label="参考均价（元/㎡）" />
        <el-table-column prop="corporateName" label="开发商名称" />
        <el-table-column prop="buildingPresentSituation" label="楼盘状态" />
        <el-table-column prop="salesStatus" label="销售状态" />
        <el-table-column prop="sellingBuild" label="正在销售楼栋" />
        <el-table-column prop="sellHouseType" label="销售户型" />
        <el-table-column prop="theLatestOpeningDate" label="最新开盘日期" />
        <el-table-column prop="recentDeliveryDate" label="最近交房日期" />
        <el-table-column prop="coveredArea" label="建筑面积㎡" />
        <el-table-column prop="jobSchedule" label="工程进度" />
        <el-table-column prop="loupanStatus" label="楼盘状况" />
        <el-table-column prop="occupancyRate" label="入住率" />
        <el-table-column prop="planningBuilding" label="规划楼栋" />
        <el-table-column prop="numberOfPlanningHouseholds" label="规划户数(户)" />
        <el-table-column prop="propertyManagementCompany" label="物业公司" />
        <el-table-column prop="createBy" label="制表人" />
        <el-table-column prop="createTime" label="制表日期" />
        <el-table-column v-if="checkPer(['admin','loupanSellingSummary:edit','loupanSellingSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudLoupanCompletedSummary from '@/api/loupan/loupanCompletedSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, buildCode: null, buildName: null, tenement: null, brand: null, property: null, buildAddr: null, prov: null, city: null, region: null, decoration: null, buildType: null, averagePrice: null, developName: null, buildStatus: null, sellStatus: null }
export default {
  name: 'LoupanCompletedSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '楼盘汇总（已竣工）', url: 'api/loupanCompletedSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudLoupanCompletedSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'loupanCompletedSummary:add'],
        edit: ['admin', 'loupanCompletedSummary:edit'],
        del: ['admin', 'loupanCompletedSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别' },
        { key: 'buildCode', display_name: '楼盘编号' },
        { key: 'buildName', display_name: '楼盘名称' },
        { key: 'brand', display_name: '品牌' },
        { key: 'buildAddr', display_name: '楼盘地址' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
