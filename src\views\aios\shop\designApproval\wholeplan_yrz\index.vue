<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目类型" prop="projectTypeLx">
                  <el-select v-model="form.projectTypeLx" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_type_lx"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目完成日期">
                  <el-date-picker v-model="form.projectCompletionDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" />
            </el-form-item>

          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-edit" /> 设计理念</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="意向图">
                  <file-upload
                    :field-value.sync="form.intentionDiagram"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    list-type="picture-card"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="设计理念说明">
              <el-input v-model="form.designphilosophy1" :rows="3" type="textarea" />
            </el-form-item>

            <!-- 设计理念附件 -->
            <el-divider content-position="left"><i class="el-icon-picture-outline" /> 设计理念附件</el-divider>
            <div class="image-upload-container">
              <el-form-item label="设计理念附件">
                <general-file-upload
                  v-model="form.designPhilosophy"
                  :field-name="'designPhilosophy'"
                  v-bind="fileUploadConfig"
                  :use-minio-delete="true"
                  :hide-remove="isViewMode"
                  @change="handleFileChange('designPhilosophy', $event)"
                />
              </el-form-item>
            </div>

            <el-divider content-position="left"><i class="el-icon-date" /> 设计周期计划</el-divider>
            <div class="table-container">
              <el-table :data="designCycleList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="60" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="设计阶段" min-width="140" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.designPhase" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.design_phase"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="提交内容" min-width="140" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.submitContent" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.submit_content"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="周期时间" min-width="140" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.cycleTime" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.cycle_time"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="方案提交时间" min-width="160" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.submitTime" type="datetime" style="width: 100%" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="70" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" size="mini" @click="handleRemoveDesignCycle(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDesignCycle">新增</el-button>
              </div>
            </div>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-user" /> 项目设计小组人员</el-divider>
            <div class="table-container">
              <el-table :data="designTeamList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="60" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="姓名" min-width="120" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name" placeholder="请输入姓名" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="职责" min-width="140" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.responsibility" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.responsibility"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="职位" min-width="140" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.positionSjlx" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.position_sjlx"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="联系方式" min-width="140" show-overflow-tooltip>
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.contactInformation" placeholder="请输入联系方式" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="70" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" size="mini" @click="handleRemoveDesignTeam(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDesignTeam">新增</el-button>
              </div>
            </div>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-time" /> 时间进度</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.createdAt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请日期">
                  <el-date-picker v-model="form.applydt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="操作时间">
                  <el-date-picker v-model="form.optdt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作人">
                  <el-input v-model="form.optname" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="说明">
              <el-input v-model="form.explain" :rows="3" type="textarea" />
            </el-form-item>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectOverview" label="项目概况" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column v-if="checkPer(['admin','tumaiOaSj:edit','tumaiOaSj:del','tumaiOaSj:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaSj from '@/api/aios/shop/tumaiOaSj'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import FileUpload from '@/components/FileUpload'
import { SimpleFileHandler } from '@/utils/fileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  // 原有表单字段
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, projectNumber: null,
  projectName: null, projectAddr: null, projectNature: null, projectCompletionDate: null,
  projectOverview: null, thirdScheme: null, createdAt: null, uid: null, optdt: null,
  optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null,
  designPhilosophy: null, intentionDiagram: null, projectAddress: null, projectid: null,
  projectDeliveryPeriod: null, typeOfService: null, createtime: null, designphilosophy1: null,
  projectTypeLx: null,
  // 设计周期计划和设计小组人员以数组形式存储
  designCycles: '[]',
  designTeams: '[]'
}

export default {
  name: 'TumaiOaSj',
  components: { pagination, crudOperation, rrOperation, FileUpload, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'project_type_lx', 'design_phase', 'submit_content', 'cycle_time', 'responsibility', 'position_sjlx'],
  cruds() {
    return CRUD({ title: '项目立项（全案/硬软装）', url: 'api/tumaiOaSj', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaSj }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaSj:add'],
        edit: ['admin', 'tumaiOaSj:edit'],
        del: ['admin', 'tumaiOaSj:del'],
        view: ['admin', 'tumaiOaSj:edit', 'tumaiOaSj:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 设计周期和设计团队列表
      designCycleList: [],
      designTeamList: [],
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectTypeLx: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 5,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      },
      // 文件字段列表
      fileFields: ['designPhilosophy', 'intentionDiagram']
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit]() {
      this.initAttachmentFields(['intentionDiagram'])
      this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
      this.initDesignCycleList()
      this.initDesignTeamList()
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['designPhilosophy', 'intentionDiagram'])
      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields(['intentionDiagram'])
      this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
      this.initDesignCycleList()
      this.initDesignTeamList()
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd]() {
      this.form.intentionDiagram = '[]'
      this.form.designPhilosophy = '[]'
      this.designCycleList = []
      this.designTeamList = []

      // 为每个子表单添加一条默认记录
      this.handleAddDesignCycle()
      this.handleAddDesignTeam()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit]() {
      try {
        // 处理附件字段
        this.prepareAttachmentFields(['intentionDiagram'])

        // 处理通用文件字段
        this.prepareGeneralFileFields && this.prepareGeneralFileFields(this.fileFields)

        this.updateFormDesignCycles()
        this.updateFormDesignTeams()
        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      try {
        return this.handleAttachmentChange(event, row)
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 初始化设计周期计划列表
    initDesignCycleList() {
      try {
        this.designCycleList = this.form.designCycles ? JSON.parse(this.form.designCycles) : []
        if (!Array.isArray(this.designCycleList)) {
          this.designCycleList = []
        }

        // 确保列表至少有一条记录
        if (this.designCycleList.length === 0) {
          this.handleAddDesignCycle()
        }
      } catch (e) {
        console.error('解析设计周期数据失败', e)
        this.designCycleList = []
        // 出错时添加一条默认记录
        this.handleAddDesignCycle()
      }
    },

    // 初始化设计团队列表
    initDesignTeamList() {
      try {
        this.designTeamList = this.form.designTeams ? JSON.parse(this.form.designTeams) : []
        if (!Array.isArray(this.designTeamList)) {
          this.designTeamList = []
        }

        // 确保列表至少有一条记录
        if (this.designTeamList.length === 0) {
          this.handleAddDesignTeam()
        }
      } catch (e) {
        console.error('解析设计团队数据失败', e)
        this.designTeamList = []
        // 出错时添加一条默认记录
        this.handleAddDesignTeam()
      }
    },

    // 更新表单中的设计周期计划
    updateFormDesignCycles() {
      this.form.designCycles = JSON.stringify(this.designCycleList)
    },

    // 更新表单中的设计团队
    updateFormDesignTeams() {
      this.form.designTeams = JSON.stringify(this.designTeamList)
    },

    // 添加设计周期计划
    handleAddDesignCycle() {
      this.designCycleList.push({
        designPhase: null,
        submitContent: null,
        cycleTime: null,
        submitTime: null
      })
    },

    // 移除设计周期计划
    handleRemoveDesignCycle(index) {
      // a如果只有一条记录，则不允许删除
      if (this.designCycleList.length <= 1) {
        this.$message.warning('至少保留一条设计周期记录')
        return
      }
      this.designCycleList.splice(index, 1)
    },

    // 添加设计团队成员
    handleAddDesignTeam() {
      this.designTeamList.push({
        name: null,
        responsibility: null,
        positionSjlx: null,
        contactInformation: null
      })
    },

    // 移除设计团队成员
    handleRemoveDesignTeam(index) {
      // 如果只有一条记录，则不允许删除
      if (this.designTeamList.length <= 1) {
        this.$message.warning('至少保留一条设计团队成员记录')
        return
      }
      this.designTeamList.splice(index, 1)
    },

    // 图片预览 (仍然保留此方法用于预览)
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件变更处理
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>
<style scoped>
.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01)
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

.table-container {
  margin-bottom: 5px;
  width: 100%;
  overflow-x: auto;
}

/* 子表格自适应优化 */
.table-container .el-table {
  min-width: 700px; /* 设置最小宽度确保内容不会过度压缩 */
}

/* 表格内输入框和选择器的自适应 */
.table-container .el-input,
.table-container .el-select {
  min-width: 100px; /* 确保输入框有最小宽度 */
}

/* 日期选择器在表格中的自适应 */
.table-container .el-date-editor {
  min-width: 140px;
}

/* 响应式设计 - 小屏幕优化 */
@media (max-width: 1200px) {
  .table-container .el-table {
    min-width: 600px;
  }
}

@media (max-width: 768px) {
  .table-container .el-table {
    min-width: 500px;
  }

  .table-container .el-input,
  .table-container .el-select {
    min-width: 80px;
  }

  .table-container .el-date-editor {
    min-width: 120px;
  }
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
