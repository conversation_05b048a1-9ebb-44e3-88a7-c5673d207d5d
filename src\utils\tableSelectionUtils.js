/**
 * 表格选择工具类
 * 用于管理数据库分析模块中的表格选择逻辑
 */

/**
 * 表格选择管理器
 */
export class TableSelectionManager {
  constructor() {
    this.defaultTable = null // 默认表格 {dbName, tableName}
    this.selectedTables = [] // 选中的表格列表 [{tableName, headers, data}]
    this.selectedTablesData = {} // 表格数据映射 {tableName: {headers: [], data: []}}
  }

  /**
   * 设置默认表格（点击左侧表格时调用）
   * @param {string} dbName - 数据库名
   * @param {string} tableName - 表格名
   * @param {Object} tableData - 表格数据 {headers: [], data: []}
   */
  setDefaultTable(dbName, tableName, tableData = null) {
    this.defaultTable = { dbName, tableName }

    // 如果提供了表格数据，更新数据映射
    if (tableData) {
      this.selectedTablesData[tableName] = tableData

      // 如果该表格不在选中列表中，自动添加
      if (!this.isTableSelected(tableName)) {
        this.selectedTables.push({
          tableName,
          headers: tableData.headers,
          data: tableData.data
        })
      } else {
        // 如果已在选中列表中，更新数据
        const existingIndex = this.selectedTables.findIndex(table => table.tableName === tableName)
        if (existingIndex >= 0) {
          this.selectedTables[existingIndex] = {
            tableName,
            headers: tableData.headers,
            data: tableData.data
          }
        }
      }
    }

    return this.getState()
  }

  /**
   * 添加表格到选择列表
   * @param {string} tableName - 表格名
   * @param {Object} tableData - 表格数据 {headers: [], data: []}
   */
  addTable(tableName, tableData) {
    if (!this.isTableSelected(tableName)) {
      this.selectedTables.push({
        tableName,
        headers: tableData.headers,
        data: tableData.data
      })
      this.selectedTablesData[tableName] = tableData
    }
    return this.getState()
  }

  /**
   * 从选择列表中移除表格
   * @param {string} tableName - 表格名
   */
  removeTable(tableName) {
    this.selectedTables = this.selectedTables.filter(table => table.tableName !== tableName)
    delete this.selectedTablesData[tableName]

    // 如果移除的是默认表格，清空默认表格
    if (this.defaultTable && this.defaultTable.tableName === tableName) {
      this.defaultTable = null
    }

    return this.getState()
  }

  /**
   * 检查表格是否已选择
   * @param {string} tableName - 表格名
   * @returns {boolean}
   */
  isTableSelected(tableName) {
    return this.selectedTables.some(table => table.tableName === tableName)
  }

  /**
   * 检查是否为默认表格
   * @param {string} tableName - 表格名
   * @returns {boolean}
   */
  isDefaultTable(tableName) {
    return this.defaultTable && this.defaultTable.tableName === tableName
  }

  /**
   * 获取默认表格信息
   * @returns {Object|null}
   */
  getDefaultTable() {
    return this.defaultTable
  }

  /**
   * 清空所有选择
   */
  clearAll() {
    this.defaultTable = null
    this.selectedTables = []
    this.selectedTablesData = {}
    return this.getState()
  }

  /**
   * 更新表格数据
   * @param {string} tableName - 表格名
   * @param {Object} tableData - 表格数据 {headers: [], data: []}
   */
  updateTableData(tableName, tableData) {
    this.selectedTablesData[tableName] = tableData

    // 更新选中列表中的数据
    const existingIndex = this.selectedTables.findIndex(table => table.tableName === tableName)
    if (existingIndex >= 0) {
      this.selectedTables[existingIndex] = {
        tableName,
        headers: tableData.headers,
        data: tableData.data
      }
    }

    return this.getState()
  }

  /**
   * 获取当前状态
   * @returns {Object}
   */
  getState() {
    return {
      defaultTable: this.defaultTable,
      selectedTables: [...this.selectedTables],
      selectedTablesData: { ...this.selectedTablesData }
    }
  }

  /**
   * 从状态恢复
   * @param {Object} state - 状态对象
   */
  setState(state) {
    this.defaultTable = state.defaultTable || null
    this.selectedTables = state.selectedTables || []
    this.selectedTablesData = state.selectedTablesData || {}
  }
}

/**
 * 表格选择工具函数
 */
export const TableSelectionUtils = {
  /**
   * 创建新的表格选择管理器
   * @returns {TableSelectionManager}
   */
  createManager() {
    return new TableSelectionManager()
  },

  /**
   * 合并多个表格的数据用于AI分析
   * @param {Array} selectedTables - 选中的表格列表
   * @returns {Object} - 合并后的数据 {headers: [], data: []}
   */
  mergeTablesData(selectedTables) {
    if (!selectedTables || selectedTables.length === 0) {
      return { headers: [], data: [] }
    }

    const mergedHeaders = []
    const mergedData = []

    selectedTables.forEach(table => {
      if (table.headers && table.data) {
        // 添加表名前缀到表头
        const prefixedHeaders = table.headers.map(header => `${table.tableName}.${header}`)
        mergedHeaders.push(...prefixedHeaders)

        // 合并数据行
        table.data.forEach((row, index) => {
          if (!mergedData[index]) {
            mergedData[index] = []
          }
          mergedData[index].push(...row)
        })
      }
    })

    return {
      headers: mergedHeaders,
      data: mergedData
    }
  },

  /**
   * 格式化表格选择状态用于显示
   * @param {Object} state - 表格选择状态
   * @returns {Object}
   */
  formatSelectionState(state) {
    const { defaultTable, selectedTables } = state

    return {
      defaultTableName: defaultTable ? defaultTable.tableName : null,
      selectedTableNames: selectedTables.map(table => table.tableName),
      totalSelected: selectedTables.length,
      hasDefault: !!defaultTable
    }
  }
}

export default TableSelectionUtils
