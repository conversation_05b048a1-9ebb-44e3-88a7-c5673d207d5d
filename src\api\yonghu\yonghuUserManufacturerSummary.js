import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/yonghuUserManufacturerSummary',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/yonghuUserManufacturerSummary/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/yonghuUserManufacturerSummary',
    method: 'put',
    data
  })
}

export default { add, edit, del }
