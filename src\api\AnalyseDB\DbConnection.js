import request from '@/utils/request'

// 测试数据库连接
export function testConnection(config) {
  return request({
    url: '/api/db/testConnection', // 修改为与后端一致的大小写
    method: 'post',
    data: config
  })
}

// 连接数据库
export function connection() {
  return request({
    url: '/api/db/connection',
    method: 'post'
  })
}

// 获取数据库列表和表列表
export function getDatabases(config) {
  return request({
    url: '/api/db/getAllBaseAndTableNames',
    method: 'post',
    data: config
  })
}

// 获取指定数据库的表
export function getTables(dbName) {
  return request({
    url: '/api/db/tables',
    method: 'get',
    params: { dbName }
  })
}

// 获取指定表的结构和数据
export function getTableData(getTableDataDto) {
  return request({
    url: '/api/db/tabledata',
    method: 'post',
    data: getTableDataDto
  })
}

// 获取表结构
export function getTableStructure(dbName, tableName) {
  return request({
    url: '/api/db/table-structure',
    method: 'get',
    params: {
      dbName,
      tableName
    }
  })
}

// 执行SQL查询
export function executeQuery(sql, dbName) {
  return request({
    url: '/api/db/execute',
    method: 'post',
    data: {
      sql,
      dbName
    }
  })
}

// 保存数据库连接配置
export function saveConnection(config) {
  return request({
    url: '/api/db/save-connection',
    method: 'post',
    data: config
  })
}

// 获取已保存的数据库连接列表
export function getSavedConnections() {
  return request({
    url: '/api/db/connections',
    method: 'get'
  })
}

// 删除数据库连接配置
export function deleteConnection(id) {
  return request({
    url: `/api/db/connection/${id}`,
    method: 'delete'
  })
}
