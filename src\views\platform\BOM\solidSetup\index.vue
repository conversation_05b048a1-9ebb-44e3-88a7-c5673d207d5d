<template>
  <div class="app-container">
    <div class="head-container">
      <label class="el-form-item-label">项目名称</label>
      <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" />
      <label class="el-form-item-label">制表日期</label>
      <el-date-picker v-model="query.createdAt" type="datetime" value-format="yyyy-MM-dd HH:mm:ss" clearable placeholder="制表日期" style="width: 185px;" class="filter-item" />
      <label class="el-form-item-label">核准人</label>
      <el-input v-model="query.approve" clearable placeholder="核准人" style="width: 185px;" class="filter-item" />
      <label class="el-form-item-label">审核人</label>
      <el-input v-model="query.audit" clearable placeholder="审核人" style="width: 185px;" class="filter-item" />
      <label class="el-form-item-label">制表人</label>
      <el-input v-model="query.createdBy" clearable placeholder="制表人" style="width: 185px;" class="filter-item" />
      <div>
        <el-upload ref="upload" class="btn" action="" :http-request="importData" accept=".xlsx,.xls" :show-file-list="false">
          <el-button type="primary" icon="el-icon-download" :loading="loadingBtn">导入excel数据</el-button>
        </el-upload>
        <el-button class="btn" type="warning" icon="el-icon-download" @click="downloadExcel">选中数据下载为excel表格</el-button>
        <el-button class="btn" type="success" icon="el-icon-download" @click="downloadTemplate">下载BOMExcel表格模板</el-button>
        <el-button type="primary" icon="el-icon-search" style="position: absolute; right: 100px;" @click="search">搜索</el-button>
        <el-button type="warning" icon="el-icon-refresh-left" style="position: absolute; right: 20px;" @click="reset">重置</el-button>
      </div>
    </div>
    <hr style="margin-top: 50px;">
    <div
      v-for="(item, index) in projectList"
      :key="item.id"
      class="project-item"
      :class="{ 'active': item.isExpanded }"
    >
      <!-- 修改后的头部布局 -->
      <div class="header-container" @click="toggleExpand(index)">
        <div class="project-info">
          <el-radio
            v-model="selectedId"
            :label="item.id"
            @click.native.stop
          >选择</el-radio>
          <div class="info-item">
            <span class="label">项目名称：</span>
            <el-text class="value" tag="b">{{ item.projectName }}</el-text>
          </div>
          <div class="info-item">
            <span class="label">制表日期：</span>
            <el-text class="value">{{ item.createdAt }}</el-text>
          </div>
        </div>

        <div class="project-people">
          <div class="people-item">
            <span class="label">核准人：</span>
            <el-text class="value">{{ item.approve }}</el-text>
          </div>
          <div class="people-item">
            <span class="label">审核人：</span>
            <el-text class="value">{{ item.audit }}</el-text>
          </div>
          <div class="people-item">
            <span class="label">制表人：</span>
            <el-text class="value">{{ item.createdBy }}</el-text>
          </div>
        </div>
      </div>

      <!-- 第二层表格 -->
      <transition name="el-zoom-in-top">
        <detail-table
          v-if="item.isExpanded"
          :table-data="item.solidSetupBOMChildList"
          :type="'solid'"
          :column-config="columnConfig"
          class="detail-table"
        />
      </transition>
    </div>
  </div>
</template>

<script>
import { getSolidSetupBOMDTO, downloadExcel, importExcel } from '@/api/BOM/BOM.js'
import DetailTable from '@/views/components/BOM/DetailTable.vue'

export default {
  components: { DetailTable },
  data() {
    return {
      query: {
        projectName: '',
        approve: '',
        audit: '',
        createdBy: '',
        createdAt: ''
      },
      projectList: [
        {
          id: '',
          projectName: '',
          createdAt: '',
          approve: '',
          audit: '',
          createdBy: '',
          isExpanded: false,
          hardSetupBOMChildList: []
        }
      ],
      selectedId: '',
      addFormVisible: false,
      loadingBtn: false
    }
  },
  async mounted() {
    await this.dataInit()
  },
  methods: {
    toggleExpand(index) {
      this.projectList[index].isExpanded = !this.projectList[index].isExpanded
    },
    async dataInit(params) {
      const res = await getSolidSetupBOMDTO(params)
      this.projectList = res
    },
    search() {
      this.dataInit()
    },
    reset() {
      this.query = {}
      this.dataInit()
    },
    importData(file) {
      this.loadingBtn = true
      if (file.file.size === 0) {
        this.$message.error('文件内容不能为空')
        return false // 阻止上传
      }
      const validTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet']
      if (!validTypes.includes(file.file.type)) {
        this.$message.error('仅支持xls/xlsx格式文件')
        return false
      }
      this.uploadFile = file.file
      importExcel(this.uploadFile, 'solid')
        .then(res => {
          this.$message.success(res)
          this.dialogVisible = false
          this.$emit('refresh')
        })
        .catch(err => {
          this.$message.error('导入失败：' + (err.message || '未知错误'))
        })
        .finally(() => {
          this.loadingBtn = false
          this.dataInit()
        })
    },
    async downloadExcel() {
      if (!this.selectedId) {
        this.$message.warning('请先选择要导出的项目')
        return
      }

      const selectedProject = this.projectList.find(item => item.id === this.selectedId)
      if (!selectedProject) {
        this.$message.error('未找到选中的项目')
        return
      }

      try {
        this.$loading({
          lock: true,
          text: '正在导出Excel...',
          spinner: 'el-icon-loading'
        })

        const response = await downloadExcel([selectedProject], 'solid')

        // 处理文件下载
        const blob = new Blob([response], {
          type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })

        // 生成文件名
        const fileName = `固装BOM_${selectedProject.projectName || '未命名'}.xlsx`

        // 创建下载链接
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = fileName
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        this.$message.success('导出成功')
      } catch (error) {
        console.error('导出错误：', error)
        this.$message.error('导出失败：' + (error.message || '未知错误'))
      } finally {
        this.$loading().close()
      }
    },
    downloadTemplate() {
      this.$loading({ lock: true, text: '正在下载模板...' })
      try {
        const link = document.createElement('a')
        link.href = require('@/assets/excel/BOM模板.xlsx')
        link.download = '软装BOM模板.xlsx'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
      } finally {
        this.$loading().close()
      }
    }
  }
}
</script>

<style scoped>
.project-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 15px;
  cursor: pointer;
  transition: all 0.3s;
}

.project-item:hover {
  box-shadow: 0 2px 12px 0 rgba(0,0,0,.1);
}

.project-item.active {
  background-color: #dcdcdd;
}

.project-info, .project-people {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.detail-table {
  margin-top: 15px;
}
.header-container {
  border-radius: 6px;
  padding: 12px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s;
}

/* 信息项样式 */
.project-info, .project-people {
  display: flex;
  gap: 30px;
}

.info-item, .people-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.label {
  color: #606266;
  font-size: 14px;
}

.value {
  color: #303133;
  font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', sans-serif;
}

/* 修改表格样式 */
.detail-table {
  margin-top: 15px;
}

:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table__header) {
  background-color: #f5f7fa;
}

:deep(.el-table__header th) {
  background-color: #f5f7fa !important;
  color: #303133;
  font-weight: 600;
}

:deep(.el-table__body) {
  background-color: #ffffff;
}
#header{
  height: 100px;
}
.excel{
  position: absolute;
  bottom: 100px;
  right: 20px;
}
.btn{
  float: left;
  margin-right: 10px;
}
</style>
