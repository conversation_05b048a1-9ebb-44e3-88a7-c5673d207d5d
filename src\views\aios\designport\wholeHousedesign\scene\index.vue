<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号" prop="projectNumber">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabulationDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>

            </el-row>
            <el-row :gutter="30">

              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" />
            </el-form-item>

            <!-- 现场复尺子表单 -->
            <el-divider content-position="left"><i class="el-icon-document" /> 复尺基本信息</el-divider>
            <div class="table-container">
              <el-table :data="measurementList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="现场复尺日期" width="160">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.sitecompoundSizeDate"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="空间名称" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.space" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.space"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="区域" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.area1" size="mini" placeholder="请输入区域" />
                  </template>
                </el-table-column>
                <el-table-column label="长(米)" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.long" size="mini" placeholder="长度" />
                  </template>
                </el-table-column>
                <el-table-column label="宽(米)" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.wide" size="mini" placeholder="宽度" />
                  </template>
                </el-table-column>
                <el-table-column label="高(米)" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.high" size="mini" placeholder="高度" />
                  </template>
                </el-table-column>
                <el-table-column label="复尺人" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.thecompoundFootPeople" size="mini" placeholder="请输入复尺人" />
                  </template>
                </el-table-column>
                <el-table-column label="复尺次数" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.multipldTimes" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.multipld_times"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="三方签名日期" width="160">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.tripartiteSignatureDate"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDeclaration" size="mini" placeholder="请输入异常说明" />
                  </template>
                </el-table-column>
                <el-table-column label="技术交底内容" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.content" size="mini" placeholder="请输入技术交底内容" />
                  </template>
                </el-table-column>
                <el-table-column label="再次复尺原因" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.cause" size="mini" placeholder="请输入再次复尺原因" />
                  </template>
                </el-table-column>
                <el-table-column label="界面划分内容" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.interfaceDivision" size="mini" placeholder="请输入界面划分内容" />
                  </template>
                </el-table-column>
                <el-table-column label="参考图片" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.referencePictures"
                      :field-name="'referencePictures'"
                      v-bind="fileUploadConfig"
                      :use-minio-delete="true"
                      :hide-remove="isViewMode"
                      @change="handleFileChange('referencePictures', $event, scope.row)"
                      @file-change="handleFileListChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="界面划分附件" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.appendix"
                      :field-name="'appendix'"
                      v-bind="fileUploadConfig"
                      :use-minio-delete="true"
                      :hide-remove="isViewMode"
                      @change="handleFileChange('appendix', $event, scope.row)"
                      @file-change="handleFileListChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveMeasurement(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddMeasurement">新增</el-button>
              </div>
            </div>
            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="备注" prop="remarks">
                  <el-input v-model="form.remarks" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="相关文件" prop="formatFile">
                  <general-file-upload
                    v-model="form.formatFile"
                    :field-name="'formatFile'"
                    v-bind="fileUploadConfig"
                    :use-minio-delete="true"
                    :hide-remove="isViewMode"
                    @change="handleFileChange('formatFile', $event)"
                    @file-change="handleFileListChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="projectOverview" label="项目概况" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjFuchi:edit','tumaiSjFuchi:del','tumaiSjFuchi:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiSjFuchi from '@/api/aios/designport/wholeHousedesign/tumaiSjFuchi'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, customerName: null, projectNumber: null, projectName: null, remarks: null, interfaceDivisionContent: null, againCompoundFootPerson: null, recordAgain: null, tripartiteSignatureDateAgain: null, number: null, tabulationDate: null, uid: null, projectid: null, projectAddress: null, projectNature: null, typeOfService: null, projectDeliveryPeriod: null, projectOverview: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, createtime: null, isturn: null, appendix2: null, measurementData: null, formatFile: null }
export default {
  name: 'TumaiSjFuchi',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'space', 'multipld_times'],
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjFuchi:add'],
        edit: ['admin', 'tumaiSjFuchi:edit'],
        del: ['admin', 'tumaiSjFuchi:del'],
        view: ['admin', 'tumaiSjFuchi:edit', 'tumaiSjFuchi:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '项目编号不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        sitecompoundSizeDate: [
          { required: true, message: '现场复尺日期不能为空', trigger: 'blur' }
        ],
        multipldTimes: [
          { required: true, message: '复尺次数不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      measurementList: [],
      // 文件字段列表
      fileFields: ['referencePictures', 'appendix', 'appendix2', 'formatFile'],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 5,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true
      }
    }
  },
  cruds() {
    return CRUD({ title: '现场复尺', url: 'api/tumaiSjFuchi', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjFuchi }})
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'referencePictures',
        'appendix2'
      ])
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
      // 初始化现场复尺列表
      this.initMeasurementList()
      // 初始化子表单中的文件字段 (使用新工具)
      this.initSubformFileFields(this.measurementList, 'referencePictures')
      this.initSubformFileFields(this.measurementList, 'appendix')
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['formatFile'])
      deleteSubformFiles(data, 'measurementData', 'referencePictures')
      deleteSubformFiles(data, 'measurementData', 'appendix')
      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'referencePictures',
        'appendix2'
      ])
      this.initGeneralFileFields(this.fileFields)
      this.initMeasurementList()
      this.initSubformFileFields(this.measurementList, 'referencePictures')
      this.initSubformFileFields(this.measurementList, 'appendix')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.referencePictures = '[]'
      this.form.appendix2 = '[]'
      this.form.formatFile = '[]'

      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)

      // 清空现场复尺列表
      this.measurementList = []

      // 添加一个空的现场复尺记录
      this.handleAddMeasurement()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      try {
        const errors = this.validateMeasurementList()
        if (errors.length > 0) {
          errors.forEach(msg => this.$message.error(msg))
          return false
        }
        console.log('提交前原始子表单数据:', JSON.stringify(this.measurementList))
        console.log('提交前原始表单数据:', JSON.stringify({
          referencePictures: this.form.referencePictures,
          appendix2: this.form.appendix2
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields(['referencePictures', 'appendix2'])

        // 处理通用文件字段
        this.prepareGeneralFileFields(this.fileFields)

        console.log('处理主表单附件后:', JSON.stringify({
          referencePictures: this.form.referencePictures,
          appendix2: this.form.appendix2
        }))

        // 处理子表单中的文件字段 (使用新工具)
        this.prepareSubformFileFields(this.measurementData, 'referencePictures')
        this.prepareSubformFileFields(this.measurementData, 'appendix')

        // 处理日期字段 - 转换为时间戳
        this.measurementList.forEach(item => {
          // 处理现场复尺日期
          if (item.sitecompoundSizeDate) {
            if (typeof item.sitecompoundSizeDate === 'string') {
              // 如果是ISO字符串
              item.sitecompoundSizeDate = new Date(item.sitecompoundSizeDate).getTime()
            } else if (item.sitecompoundSizeDate instanceof Date) {
              // 如果是Date对象
              item.sitecompoundSizeDate = item.sitecompoundSizeDate.getTime()
            }
          }

          // 处理三方签名日期
          if (item.tripartiteSignatureDate) {
            if (typeof item.tripartiteSignatureDate === 'string') {
              // 如果是ISO字符串
              item.tripartiteSignatureDate = new Date(item.tripartiteSignatureDate).getTime()
            } else if (item.tripartiteSignatureDate instanceof Date) {
              // 如果是Date对象
              item.tripartiteSignatureDate = item.tripartiteSignatureDate.getTime()
            }
          }
        })

        // 直接发送现场复尺数据
        crud.form.measurementData = JSON.stringify(this.measurementList)

        console.log('处理后的表单数据:', crud.form.measurementData)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化现场复尺列表
    initMeasurementList() {
      try {
        this.measurementList = this.form.measurementData ? JSON.parse(this.form.measurementData) : []
        if (!Array.isArray(this.measurementList)) {
          this.measurementList = []
        }

        // 确保每行都有附件字段
        this.measurementList.forEach(item => {
          if (!item.referencePictures) {
            item.referencePictures = '[]'
          }
          if (!item.appendix) {
            item.appendix = '[]'
          }
        })
      } catch (e) {
        console.error('解析现场复尺数据失败:', e)
        this.measurementList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.measurementList.length === 0) {
        this.handleAddMeasurement()
      }
    },

    handleAddMeasurement() {
      this.measurementList.push({
        // 使用与后端实体类属性完全一致的字段名
        sitecompoundSizeDate: '',
        tripartiteSignatureDate: '',
        space: '',
        area1: '',
        long: '',
        wide: '',
        high: '',
        thecompoundFootPeople: '',
        multipldTimes: '',
        exceptionDeclaration: '',
        content: '',
        cause: '',
        interfaceDivision: '',

        // 文件字段
        referencePictures: '[]',
        appendix: '[]'
      })
    },

    handleRemoveMeasurement(index) {
      // 获取要删除的行数据
      const rowData = this.measurementList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        deleteSubformRowFiles(rowData, 'referencePictures')
        deleteSubformRowFiles(rowData, 'appendix')
      }
      this.measurementList.splice(index, 1)

      // 确保至少有一条记录
      if (this.measurementList.length === 0) {
        this.handleAddMeasurement()
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, row || this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files, row) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        if (row) {
          // 子表单的处理
          row[fieldName] = jsonStr
          console.log(`子表单字段${fieldName}更新为:`, row[fieldName])
        } else {
          // 主表单的处理
          this.form[fieldName] = jsonStr
          console.log(`主表单字段${fieldName}更新为:`, this.form[fieldName])
        }
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    },
    validateMeasurementList() {
      const errors = []

      this.measurementList.forEach((item, index) => {
        if (!item.sitecompoundSizeDate) {
          errors.push(`第 ${index + 1} 行缺少现场复尺日期`)
        }

        if (!item.multipldTimes) {
          errors.push(`第 ${index + 1} 行缺少复尺次数`)
        }

        if (isNaN(item.long) || isNaN(item.wide) || isNaN(item.high)) {
          errors.push(`第 ${index + 1} 行尺寸必须为数字`)
        }
      })

      return errors
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
