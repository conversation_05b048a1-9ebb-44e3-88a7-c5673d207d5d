import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/cailiaoQuotationsSheetMetalSummary',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/cailiaoQuotationsSheetMetalSummary/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/cailiaoQuotationsSheetMetalSummary',
    method: 'put',
    data
  })
}

export default { add, edit, del }
