/**
 * AIOS 查看按钮集成辅助工具
 * 提供通用的查看功能相关方法
 */

import CRUD from '@crud/crud'

export const AiosViewButtonHelper = {
  /**
   * 生成查看权限配置
   * @param {string} moduleName - 模块名称，如 'tumaiSjJingzhuang'
   * @returns {object} 包含查看权限的权限配置
   */
  generatePermissions(moduleName) {
    return {
      add: ['admin', `${moduleName}:add`],
      edit: ['admin', `${moduleName}:edit`],
      del: ['admin', `${moduleName}:del`],
      view: ['admin', `${moduleName}:edit`, `${moduleName}:view`]
    }
  },

  /**
   * 生成查看权限检查数组
   * @param {string} moduleName - 模块名称
   * @returns {array} 权限检查数组
   */
  generatePermissionCheck(moduleName) {
    return ['admin', `${moduleName}:edit`, `${moduleName}:del`, `${moduleName}:view`]
  },

  /**
   * 设置表单只读状态的通用方法
   * @param {boolean} readonly - 是否只读
   * @param {object} vueInstance - Vue实例
   */
  setFormReadonly(readonly, vueInstance) {
    vueInstance.$nextTick(() => {
      const form = vueInstance.$refs.form
      if (form && form.$el) {
        // 处理输入框和文本域
        const inputs = form.$el.querySelectorAll('input, textarea')
        inputs.forEach(input => {
          if (readonly) {
            input.setAttribute('readonly', 'readonly')
            input.style.backgroundColor = '#f5f7fa'
            input.style.cursor = 'not-allowed'
          } else {
            input.removeAttribute('readonly')
            input.style.backgroundColor = ''
            input.style.cursor = ''
          }
        })

        // 处理选择器
        const selects = form.$el.querySelectorAll('.el-select')
        selects.forEach(select => {
          if (readonly) {
            select.style.pointerEvents = 'none'
            select.style.backgroundColor = '#f5f7fa'
          } else {
            select.style.pointerEvents = ''
            select.style.backgroundColor = ''
          }
        })

        // 处理日期选择器
        const datePickers = form.$el.querySelectorAll('.el-date-editor')
        datePickers.forEach(picker => {
          if (readonly) {
            picker.style.pointerEvents = 'none'
            picker.style.backgroundColor = '#f5f7fa'
          } else {
            picker.style.pointerEvents = ''
            picker.style.backgroundColor = ''
          }
        })

        // 处理开关组件
        const switches = form.$el.querySelectorAll('.el-switch')
        switches.forEach(switchEl => {
          if (readonly) {
            switchEl.style.pointerEvents = 'none'
          } else {
            switchEl.style.pointerEvents = ''
          }
        })

        // 处理单选框和复选框
        const radiosAndCheckboxes = form.$el.querySelectorAll('.el-radio, .el-checkbox')
        radiosAndCheckboxes.forEach(el => {
          if (readonly) {
            el.style.pointerEvents = 'none'
          } else {
            el.style.pointerEvents = ''
          }
        })

        // 隐藏/显示表单内的操作按钮（排除对话框标题栏按钮）
        const formButtons = form.$el.querySelectorAll('.el-button:not(.el-dialog__headerbtn)')
        formButtons.forEach(button => {
          // 只隐藏表单内的操作按钮，保留取消和确认按钮的父容器
          const isInDialogFooter = button.closest('.el-dialog__footer')
          if (!isInDialogFooter) {
            if (readonly) {
              button.style.display = 'none'
            } else {
              button.style.display = ''
            }
          }
        })

        // 处理文件上传组件
        const uploads = form.$el.querySelectorAll('.el-upload')
        uploads.forEach(upload => {
          if (readonly) {
            upload.style.pointerEvents = 'none'
            upload.style.opacity = '0.6'
          } else {
            upload.style.pointerEvents = ''
            upload.style.opacity = ''
          }
        })

        // 处理表格内的操作按钮
        const tableButtons = form.$el.querySelectorAll('.el-table .el-button')
        tableButtons.forEach(button => {
          if (readonly) {
            button.style.display = 'none'
          } else {
            button.style.display = ''
          }
        })
      }
    })
  },

  /**
   * 生成查看相关的钩子方法
   * @param {object} options - 配置选项
   * @returns {object} 钩子方法对象
   */
  generateViewHooks(options = {}) {
    const {
      hasFileFields = false,
      hasSubFormData = false,
      customBeforeView = null,
      customAfterView = null,
      customBeforeCancel = null
    } = options

    return {
      // 查看前的操作
      [CRUD.HOOK.beforeToView](crud, form) {
        // 初始化文件字段
        if (hasFileFields && this.initGeneralFileFields) {
          this.initGeneralFileFields(this.fileFields)
        }

        // 初始化子表单数据
        if (hasSubFormData && this.initSubFormData) {
          this.initSubFormData()
        }

        // 执行自定义逻辑
        if (customBeforeView) {
          customBeforeView.call(this, crud, form)
        }

        // 设置表单为只读模式
        AiosViewButtonHelper.setFormReadonly(true, this)
        return true
      },

      // 查看后的操作
      [CRUD.HOOK.afterToView](crud, form) {
        if (customAfterView) {
          customAfterView.call(this, crud, form)
        }
      },

      // 查看取消前的操作
      [CRUD.HOOK.beforeViewCancel](crud, form) {
        if (customBeforeCancel) {
          customBeforeCancel.call(this, crud, form)
        }

        // 恢复表单可编辑状态
        AiosViewButtonHelper.setFormReadonly(false, this)
        return true
      }
    }
  },

  /**
   * 创建查看按钮的mixin
   * @param {object} options - 配置选项
   * @returns {object} Vue mixin对象
   */
  createViewMixin(options = {}) {
    const hooks = this.generateViewHooks(options)

    return {
      methods: {
        ...hooks,

        // 设置表单只读状态的实例方法
        setFormReadonly(readonly) {
          AiosViewButtonHelper.setFormReadonly(readonly, this)
        }
      }
    }
  }
}

export default AiosViewButtonHelper
