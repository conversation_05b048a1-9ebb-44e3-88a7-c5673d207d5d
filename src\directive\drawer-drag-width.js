// Vue 2版本的拖拽指令
const drawerDragWidth = {
  bind(el, binding) {
    const drawerEle = el.querySelector('.ai-chat-container') || el

    // 创建触发拖拽的元素
    const dragItem = document.createElement('div')
    // 将元素放置到抽屉的左边边缘
    dragItem.style.cssText = 'height: 100%;width: 5px;cursor: w-resize;position: absolute;left: 0;z-index: 11;'
    drawerEle.append(dragItem)

    dragItem.onmousedown = (downEvent) => {
      // 阻止默认事件
      downEvent.preventDefault()
      // 拖拽时禁用文本选中
      document.body.style.userSelect = 'none'

      document.onmousemove = function(moveEvent) {
        // 获取鼠标距离浏览器右边缘的距离
        let realWidth = document.body.clientWidth - moveEvent.clientX
        const width30 = document.body.clientWidth * 0.2
        const width80 = document.body.clientWidth * 0.8
        // 宽度不能大于浏览器宽度 80%，不能小于宽度的 20%
        realWidth = realWidth > width80 ? width80 : realWidth < width30 ? width30 : realWidth
        drawerEle.style.width = realWidth + 'px'
      }

      document.onmouseup = function() {
        // 拖拽结束时，取消禁用文本选中
        document.body.style.userSelect = ''
        document.onmousemove = null
        document.onmouseup = null
      }
    }
  }
}

export default drawerDragWidth
