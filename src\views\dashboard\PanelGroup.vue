<template>
  <el-row :gutter="40" class="panel-group">
    <el-col :xs="18" :sm="18" :lg="4" class="card-panel-col">
      <div v-permission="['admin','menu:list']" class="card-panel" @click="linkTo('loupan')">
        <div class="card-panel-icon-wrapper icon-people">
          <svg-icon icon-class="buildings" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            楼盘云
          </div>
          <!-- <count-to :start-val="0" :end-val="102400" :duration="2600" class="card-panel-num" /> -->
        </div>
      </div>
    </el-col>
    <el-col :xs="18" :sm="18" :lg="4" class="card-panel-col">
      <div v-permission="['admin','menu:list']" class="card-panel" @click="linkTo('xiaoshou')">
        <div class="card-panel-icon-wrapper icon-message">
          <svg-icon icon-class="money" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            销售云
          </div>
        </div>
      </div>
    </el-col>
    <el-col v-permission="['admin','menu:list']" :xs="18" :sm="18" :lg="4" class="card-panel-col">
      <div class="card-panel" @click="linkTo('sheji')">
        <div class="card-panel-icon-wrapper icon-money">
          <svg-icon icon-class="edit" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            设计云
          </div>
        </div>
      </div>
    </el-col>
    <el-col v-permission="['admin','menu:list']" :xs="18" :sm="18" :lg="4" class="card-panel-col">
      <div class="card-panel" @click="linkTo('yanfa')">
        <div class="card-panel-icon-wrapper icon-research">
          <svg-icon icon-class="app" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            研发云
          </div>
        </div>
      </div>
    </el-col>
    <el-col v-permission="['admin','menu:list']" :xs="18" :sm="18" :lg="4" class="card-panel-col">
      <div class="card-panel" @click="linkTo('cailiao')">
        <div class="card-panel-icon-wrapper icon-shopping">
          <svg-icon icon-class="tools" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            材料云
          </div>
        </div>
      </div>
    </el-col>
    <el-col v-permission="['admin','menu:list']" :xs="18" :sm="18" :lg="4" class="card-panel-col">
      <div class="card-panel" @click="linkTo('yonghu')">
        <div class="card-panel-icon-wrapper icon-user">
          <svg-icon icon-class="user" class-name="card-panel-icon" />
        </div>
        <div class="card-panel-description">
          <div class="card-panel-text">
            用户云
          </div>
        </div>
      </div>
    </el-col>
  </el-row>
</template>

<script>
// import CountTo from 'vue-count-to'

export default {
  data() {
    return {
      user: this.$store.state.user.user
    }
  },
  methods: {
    handleSetLineChartData(type) {
      this.$emit('handleSetLineChartData', type)
    },
    linkTo(type) {
      switch (type) {
        case 'loupan':
          localStorage.setItem('title', 'loupan')
          localStorage.setItem('titleCN', '楼盘云')
          break
        case 'xiaoshou':
          localStorage.setItem('title', 'xiaoshou')
          localStorage.setItem('titleCN', '销售云')
          break
        case 'sheji':
          localStorage.setItem('title', 'sheji')
          localStorage.setItem('titleCN', '设计云')
          break
        case 'yanfa':
          localStorage.setItem('title', 'yanfa')
          localStorage.setItem('titleCN', '研发云')
          break
        case 'cailiao':
          localStorage.setItem('title', 'cailiao')
          localStorage.setItem('titleCN', '材料云')
          break
        case 'yonghu':
          localStorage.setItem('title', 'yonghu')
          localStorage.setItem('titleCN', '用户云')
          break
      }
      window.location.href = '/bi'
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  // margin-top: 18px;
  // margin-left: 30px;

  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 60px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, .05);
    border-color: rgba(0, 0, 0, .05);
    border-radius: 12px;

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3
      }

      .icon-research {
        background: #BFBFBF
      }

      .icon-user {
        background: #e7d152
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3
    }

    .icon-research {
      color: #BFBFBF
    }

    .icon-user{
      color: #e7d152
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 7px 0 0 7px;
      padding: 6px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 31px;
    }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 12px;
      margin-left: 8px;

      .card-panel-text {
        line-height: 34px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 24px;
        padding-right: 25px;
        // margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

@media (max-width:550px) {
  .card-panel-description {
    display: none;
  }

  .card-panel-icon-wrapper {
    float: none !important;
    width: 100%;
    height: 100%;
    margin: 0 !important;

    .svg-icon {
      display: block;
      margin: 14px auto !important;
      float: none !important;
    }
  }
}
</style>
