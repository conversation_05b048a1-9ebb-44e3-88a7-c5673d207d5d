<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :title="crud.status.title" width="900px" :visible="crud.status.cu > 0" @update:visible="val => crud.status.cu = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.makeDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号" prop="projectNumber">
                  <el-input v-model="form.projectNumber" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="未税零售总金额(元)">
                  <el-input v-model="form.grossRetailSales" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="未税B2B总金额(元)">
                  <el-input v-model="form.b2bAmount" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              <el-input v-model="form.notes" :rows="2" type="textarea" style="width: 100%" />
            </el-form-item>

            <!-- 软装BOM子表单 -->
            <el-divider content-position="left"><i class="el-icon-shopping-cart-2" /> 软装BOM清单</el-divider>
            <div class="table-container">
              <el-table :data="bomItemsList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="类别" min-width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.categoryRz" filterable placeholder="请选择" size="small" style="width: 100%">
                      <el-option
                        v-for="item in dict.category_rz"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="区域/位置" min-width="160">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.zone" placeholder="请输入区域/位置" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="物品编号" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialNumber" placeholder="请输入物品编号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="物品名称" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialName" placeholder="请输入物品名称" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="图片" min-width="120">
                  <template slot-scope="scope">
                    <file-upload
                      size="small"
                      :field-value.sync="scope.row.picture"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      @change="(event) => handleChangeRowPictureFile(scope.$index, event)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="品牌" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.brand" placeholder="请输入品牌" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="型号" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.xinghao" placeholder="请输入型号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="规格" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.guige" placeholder="请输入规格" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="颜色" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.pigment" placeholder="请输入颜色" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="材质" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialQuality" placeholder="请输入材质" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="参数描述" min-width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.parameter" type="textarea" :rows="2" placeholder="请输入参数描述" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="数量" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.count" placeholder="请输入数量" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="单位" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.unit" placeholder="请输入单位" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="未税B2B单价(元)" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.price" placeholder="请输入单价" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="B2B金额" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.b2bPrice" placeholder="请输入B2B金额" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="零售单价(元)" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.retailUnitPrice" placeholder="请输入零售单价" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="零售金额(元)" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.retailValue" placeholder="请输入零售金额" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="供应商名称" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.supplierName" placeholder="请输入供应商名称" size="small" />
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveBomItem(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddBomItem">新增物品</el-button>
              </div>
            </div>

            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>

          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="makeDate" label="制表日期" />
        <el-table-column prop="grossRetailSales" label="未税零售总金额(元)" />
        <el-table-column prop="b2bAmount" label="未税B2B总金额(元)" />
        <el-table-column v-if="checkPer(['admin','tumaiSjRzbom:edit','tumaiSjRzbom:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiSjRzbom from '@/api/aios/designport/softdesign/tumaiSjRzbom'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, projectNumber: null,
  projectName: null, projectOverview: null, notes: null, projectNature: null, makeDate: null,
  position01: null, uid: null, projectAddress: null, typeOfService: null, projectid: null,
  projectDeliveryPeriod: null, fengge: null, huxingStructure: null, areaOfStructure: null,
  softOutfitBudget: null, pdfFormaFile2: null, finalLayoutPlan: null, category1: null,
  supplierName: null, brandContact: null, contactInformation: null, finalDeepeningPlan: null,
  optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null,
  isturn: null, amountInTotal: null, createtime: null, grossRetailSales: null, b2bAmount: null,
  // 子表单数据
  bomItems: '[]'
}
export default {
  name: 'TumaiSjRzbom',
  components: { pagination, crudOperation, rrOperation, udOperation, FileUpload },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin],
  dicts: ['project_nature', 'huxing_structure', 'status', 'category_rz'],
  cruds() {
    return CRUD({ title: '软装BOM', url: 'api/tumaiSjRzbom', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjRzbom }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjRzbom:add'],
        edit: ['admin', 'tumaiSjRzbom:edit'],
        del: ['admin', 'tumaiSjRzbom:del']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // BOM物品列表
      bomItemsList: [],
      rules: {
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '项目编号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi'])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'pdfFormaFile2',
        'finalLayoutPlan',
        'finalDeepeningPlan'
      ])

      // 初始化BOM物品列表
      this.initBomItemsList()

      // 初始化子表单中的文件字段
      this.bomItemsList.forEach(item => {
        if (item.picture && !item.pictureFiles) {
          item.pictureFiles = []
          const urls = item.picture.split(',').filter(url => url.trim())
          if (urls.length > 0) {
            item.pictureFiles = urls.map((url, index) => ({
              name: `物品图片${index + 1}`,
              url: url
            }))
          }
        }
      })
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.pdfFormaFile2 = '[]'
      this.form.finalLayoutPlan = '[]'
      this.form.finalDeepeningPlan = '[]'

      this.bomItemsList = []

      // 添加一个空的BOM记录
      this.handleAddBomItem()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        // 检查BOM物品列表中的必填项
        for (let i = 0; i < this.bomItemsList.length; i++) {
          const item = this.bomItemsList[i]
          if (!item.materialName || item.materialName.trim() === '') {
            this.$message.error(`第 ${i + 1} 行的物品名称不能为空`)
            return false
          }
          if (!item.categoryRz || item.categoryRz.trim() === '') {
            this.$message.error(`第 ${i + 1} 行的类别不能为空`)
            return false
          }
        }

        console.log('提交前原始表单数据:', JSON.stringify({
          pdfFormaFile2: this.form.pdfFormaFile2,
          finalLayoutPlan: this.form.finalLayoutPlan,
          finalDeepeningPlan: this.form.finalDeepeningPlan
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'pdfFormaFile2',
          'finalLayoutPlan',
          'finalDeepeningPlan'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          pdfFormaFile2: this.form.pdfFormaFile2,
          finalLayoutPlan: this.form.finalLayoutPlan,
          finalDeepeningPlan: this.form.finalDeepeningPlan
        }))

        // 处理BOM物品图片并更新到表单
        this.updateFormBomItems()

        console.log('处理后的表单数据:', this.form.bomItems)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化BOM物品列表
    initBomItemsList() {
      try {
        this.bomItemsList = this.form.bomItems ? JSON.parse(this.form.bomItems) : []
        if (!Array.isArray(this.bomItemsList)) {
          this.bomItemsList = []
        }
      } catch (e) {
        console.error('解析BOM物品数据失败:', e)
        this.bomItemsList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.bomItemsList.length === 0) {
        this.handleAddBomItem()
      }
    },

    // 更新表单中的BOM物品数据
    updateFormBomItems() {
      // 当数据提交前，确保每行的JSON格式图片数据被正确处理
      this.bomItemsList.forEach(item => {
        if (typeof item.picture === 'string' && item.picture.startsWith('[')) {
          try {
            // 尝试解析JSON
            const fileList = JSON.parse(item.picture)
            // 提取URL并转换为逗号分隔的字符串
            if (Array.isArray(fileList) && fileList.length > 0) {
              const urls = fileList.map(file => file.url).filter(url => url)
              item.picture = urls.join(',')
            } else {
              item.picture = ''
            }
          } catch (e) {
            console.error('处理物品图片时出错:', e)
            item.picture = ''
          }
        }
      })

      this.form.bomItems = JSON.stringify(this.bomItemsList)
    },

    // 添加BOM物品
    handleAddBomItem() {
      this.bomItemsList.push({
        brand: null,
        materialNumber: null,
        materialName: null,
        xinghao: null,
        guige: null,
        parameter: null,
        unit: null,
        count: null,
        price: null,
        supplierName: null,
        pigment: null,
        zone: null,
        materialQuality: null,
        categoryRz: null,
        picture: null,
        pictureFiles: [],
        retailUnitPrice: null,
        retailValue: null,
        b2bPrice: null
      })
    },

    // 移除BOM物品
    handleRemoveBomItem(index) {
      this.bomItemsList.splice(index, 1)
      if (this.bomItemsList.length === 0) {
        this.handleAddBomItem()
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        if (row === this.form) {
          // 主表单文件处理
          const result = this.handleAttachmentChange(event, row)
          console.log('主表单处理后数据:', JSON.stringify({
            pdfFormaFile2: this.form.pdfFormaFile2,
            finalLayoutPlan: this.form.finalLayoutPlan,
            finalDeepeningPlan: this.form.finalDeepeningPlan
          }))
          return result
        }

        console.error('未识别的文件处理目标:', row)
        return false
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理行的图片上传、移除和更改
    async handleChangeRowPictureFile(index, event) {
      console.log('子表单图片变更事件:', index, JSON.stringify(event))

      if (index >= 0 && index < this.bomItemsList.length) {
        try {
          // 确保event包含fileList和操作类型
          if (event && event.fileList) {
            // 直接使用JSON数据，不再需要自己维护pictureFiles数组
            if (typeof this.bomItemsList[index].picture !== 'string') {
              this.bomItemsList[index].picture = '[]'
            }
          } else {
            console.warn('图片变更事件格式不正确', event)
          }
        } catch (error) {
          console.error('处理子表单图片变更出错:', error)
          this.$message.error('图片处理出错，请重试')
        }
      }
    },

    // 转换图片为Base64 (保留此方法用于子表单的图片处理)
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = error => reject(error)
      })
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
