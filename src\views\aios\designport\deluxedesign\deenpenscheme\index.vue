<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 370px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" style="width: 370px;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddr" style="width: 370px;" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="效果图计划完成时间">
                  <el-date-picker v-model="form.planCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户确认时间">
                  <el-date-picker v-model="form.customerConfirmTime1" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddress" style="width: 370px;" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" style="width: 100%" />
            </el-form-item>

            <el-divider content-position="left"><i class="el-icon-document" /> 深化方案版本</el-divider>
            <div class="table-container">
              <el-table :data="schemeVersionsList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="深化方案制作开始日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.dateCommenced" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="绘制人" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.draftsman1" placeholder="请输入绘制人" />
                  </template>
                </el-table-column>
                <el-table-column label="PDF格式附件" width="140">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.pdfFormaFile1"
                      size="small"
                      :field-name="'pdfFormaFile1'"
                      v-bind="tablePdfFileUploadConfig"
                      :use-minio-delete="true"
                      list-type="text"
                      show-file-list
                      show-file-name
                      :hide-remove="isViewMode"
                      @change="handleSubformFileChange($event, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" width="130">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription1" placeholder="请输入异常说明" :rows="2" type="textarea" />
                  </template>
                </el-table-column>
                <el-table-column label="版本" width="100">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.drawingVersion" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="100">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.commitStatus" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.commit_status"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="实际完成日期" width="140">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.actualvcompletionDate" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveSchemeVersion(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddSchemeVersion">新增</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>
            <el-form-item label="最终深化方案PDF附件">
              <general-file-upload
                v-model="form.finalDeepeningPlan"
                :field-name="'finalDeepeningPlan'"
                v-bind="pdfFileUploadConfig"
                :hide-remove="isViewMode"
                @change="handleFileChange('finalDeepeningPlan', $event)"
              />
            </el-form-item>
            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjShenhua:edit','tumaiSjShenhua:del','tumaiSjShenhua:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>

import crudTumaiSjShenhua from '@/api/aios/designport/deluxedesign/tumaiSjShenhua'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, tabDate: null, oddNumbers: null,
  projectNumber: null, projectName: null, projectAddr: null, projectNature: null,
  serviceType: null, projectOverview: null, planCompletionTime: null, uid: null,
  optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null,
  isturn: null, projectAddress: null, typeOfService: null, customerConfirmTime1: null,
  customerSignFile1: null, projectid: null, projectDeliveryPeriod: null, planlist: null,
  createtime: null, finalDeepeningPlan: null,
  // 深化方案版本数据JSON字符串
  schemeVersionsData: '[]'
}
export default {
  name: 'TumaiSjShenhua',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'drawing_version', 'commit_status'],
  cruds() {
    return CRUD({ title: '深化方案设计（全案）', url: 'api/tumaiSjShenhua', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjShenhua }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjShenhua:add'],
        edit: ['admin', 'tumaiSjShenhua:edit'],
        del: ['admin', 'tumaiSjShenhua:del'],
        view: ['admin', 'tumaiSjShenhua:edit', 'tumaiSjShenhua:view']
      },
      // 文件上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 深化方案版本列表
      schemeVersionsList: [],
      // PDF文件上传配置
      pdfFileUploadConfig: {
        accept: '.pdf,application/pdf',
        maxFiles: 5,
        tipText: '仅支持上传PDF格式文件',
        buttonText: '上传PDF文件',
        listType: 'text',
        useMinioDelete: true
      },
      // 表格PDF文件上传配置
      tablePdfFileUploadConfig: {
        accept: '.pdf,application/pdf',
        maxFiles: 2,
        tipText: '仅支持上传PDF格式文件',
        buttonText: '上传PDF',
        listType: 'text',
        useMinioDelete: true,
        showFileList: true,
        showFileName: true
      },
      // 文件字段定义
      fileFields: {
        pdfFormaFile1: 'PDF格式附件',
        finalDeepeningPlan: '最终深化方案PDF附件'
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectid: [
          { required: true, message: '项目ID不能为空', trigger: 'blur' },
          {
            pattern: /^\d+$/,
            message: '项目ID必须为纯数字',
            trigger: ['blur', 'change']
          }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    } else {
      console.log('文件删除API已配置:', this.minioDeleteApi)
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 修改钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'customerSignFile1',
        'planlist'
        // 'finalDeepeningPlan' // 现在使用GeneralFileHandler处理
      ])
      // 初始化通用文件字段
      this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
      // 初始化深化方案版本列表
      this.initSchemeVersionsList()
      // 初始化子表单中的文件字段
      this.initSubformFileFields(this.schemeVersionsList, 'pdfFormaFile1')
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'customerSignFile1',
        'planlist'
      ])
      this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
      this.initSchemeVersionsList()
      this.initSubformFileFields(this.schemeVersionsList, 'pdfFormaFile1')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      // 删除主表单的文件
      deleteRecordFiles(data, ['finalDeepeningPlan'])

      // 删除子表单中的文件
      deleteSubformFiles(data, 'schemeVersionsData', 'pdfFormaFile1')

      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 修改钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.customerSignFile1 = '[]'
      this.form.planlist = '[]'
      this.form.finalDeepeningPlan = '[]'
      this.schemeVersionsList = []

      // 添加一个空的版本记录
      this.handleAddSchemeVersion()
    },

    // 修改钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始子表单数据:', JSON.stringify(this.schemeVersionsList))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'customerSignFile1',
          'planlist'
          // 'finalDeepeningPlan' // 现在使用GeneralFileHandler处理
        ])

        // 处理通用文件字段
        this.prepareGeneralFileFields && this.prepareGeneralFileFields(this.fileFields)

        // 处理子表单中的文件字段
        this.prepareSubformFileFields(this.schemeVersionsList, 'pdfFormaFile1')

        // 将处理后的子表单数据同步回表单
        this.form.schemeVersionsData = JSON.stringify(this.schemeVersionsList)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化深化方案版本列表
    initSchemeVersionsList() {
      try {
        console.log('初始化深化方案版本列表')
        this.schemeVersionsList = this.form.schemeVersionsData ? JSON.parse(this.form.schemeVersionsData) : []
        if (!Array.isArray(this.schemeVersionsList)) {
          this.schemeVersionsList = []
        }

        // 确保每行都有文件字段
        this.schemeVersionsList.forEach(item => {
          if (!item.pdfFormaFile1) {
            item.pdfFormaFile1 = '[]'
          } else if (typeof item.pdfFormaFile1 === 'string' && !item.pdfFormaFile1.startsWith('[')) {
            // 处理URL格式的情况，转换为标准JSON格式
            item.pdfFormaFile1 = this.convertUrlsToFileArray(item.pdfFormaFile1)
          }
        })
      } catch (e) {
        console.error('解析深化方案版本数据失败:', e)
        this.schemeVersionsList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.schemeVersionsList.length === 0) {
        this.handleAddSchemeVersion()
      }
    },

    // 将URL字符串转换为文件数组
    convertUrlsToFileArray(urlStr) {
      if (!urlStr) return '[]'

      try {
        // 如果已经是JSON格式，直接返回
        if (urlStr.startsWith('[') && urlStr.endsWith(']')) {
          return urlStr
        }

        // 处理逗号分隔的多个URL
        const urls = urlStr.split(',')
        const files = urls.map(url => {
          // 从URL提取文件名
          const fileName = url.substring(url.lastIndexOf('/') + 1)
          return {
            name: fileName,
            url: url,
            size: 0,
            type: ''
          }
        })

        return JSON.stringify(files)
      } catch (e) {
        console.error('URL转换文件数组出错:', e)
        return '[]'
      }
    },

    // 添加深化方案版本
    handleAddSchemeVersion() {
      this.schemeVersionsList.push({
        dateCommenced: null,
        draftsman1: null,
        pdfFormaFile1: '[]',
        exceptionDescription1: null,
        drawingVersion: null,
        commitStatus: null,
        actualvcompletionDate: null
      })
    },

    // 移除深化方案版本
    handleRemoveSchemeVersion(index) {
      // 获取要删除的行数据
      const rowData = this.schemeVersionsList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        deleteSubformRowFiles(rowData, 'pdfFormaFile1')
      }
      this.schemeVersionsList.splice(index, 1)
      if (this.schemeVersionsList.length === 0) {
        this.handleAddSchemeVersion()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 处理子表单的文件变更
    handleSubformFileChange(event, row) {
      if (!event || !event.fieldName || !row) {
        return false
      }

      const fieldName = event.fieldName
      const files = event.files || []

      console.log(`子表单文件 ${fieldName} 更新:`, files)

      // 将文件数组转换为JSON字符串存储
      row[fieldName] = JSON.stringify(files)

      return true
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },

    // 文件变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      // 确保事件对象格式正确
      if (event && event.action === 'success' && event.file && event.file.response) {
        // 根据上下文自动判断字段名
        if (!event.fieldName) {
          if (row === this.form) {
            // 主表单字段处理 - 自动识别字段名
            if (event.target) {
              // 通过target识别具体是哪个字段的变更
            }
          } else if (this.schemeVersionsList.includes(row)) {
            // 子表单字段 - 使用后端字段名
            event.fieldName = 'pdfFormaFile1'
          }
        }
      }

      let result = false

      try {
        if (row === this.form) {
          // 主表单文件处理
          result = this.handleAttachmentChange(event, row)
        } else {
          // 子表单文件处理
          result = this.handleSubformFileChange(event, row)
        }

        if (!result) {
          console.warn('文件处理返回失败结果')
          this.$message.warning('文件处理失败，请重试')
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },
    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
