<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
    </div>

    <!--表单组件-->
    <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" width="1000px" :title="crud.status.title" @update:visible="val => crud.status.cuv = val ? 2 : 0">
      <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
        <div class="form-section">
          <!-- 基本信息 Tab -->
          <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item label="项目编号">
                <el-input v-model="form.projectNumber" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目名称">
                <el-input v-model="form.projectName" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="30">
            <el-col :span="12">
              <el-form-item label="项目交期">
                <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="固装预算">
                <el-input v-model="form.fixedPackBudget">
                  <template slot="append">万元</template>
                </el-input>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="单号">
                <el-input v-model="form.oddNumbers" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="制表日期">
                <el-date-picker v-model="form.tabulationDate" type="datetime" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="流程状态">
                <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                  <el-option
                    v-for="item in dict.status"
                    :key="item.id"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item label="项目概况">
            <el-input v-model="form.projectOverview" type="textarea" :rows="3" />
          </el-form-item>
          <el-form-item label="特殊功能说明">
            <el-input v-model="form.specialFunctionDescription" type="textarea" :rows="3" />
          </el-form-item>

          <!-- 图纸信息 Tab -->
          <el-divider content-position="left"><i class="el-icon-picture" /> 平面效果图</el-divider>

          <div class="image-upload-container">
            <el-form-item label="量尺图片">
              <general-file-upload
                v-model="form.sizePictureAttachment"
                :field-name="'sizePictureAttachment'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('sizePictureAttachment', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
          </div>

          <!-- 产品需求 Tab -->
          <el-divider content-position="left"><i class="el-icon-shopping-cart-full" /> 产品需求</el-divider>
          <div class="table-container">
            <el-table :data="productRequirementList" size="small" border style="width: 100%">
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="空间" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.space1" placeholder="请输入空间" />
                </template>
              </el-table-column>
              <el-table-column label="产品名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productName" placeholder="请输入产品名称" />
                </template>
              </el-table-column>
              <el-table-column label="产品编号" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productNumber" placeholder="请输入产品编号" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-delete" @click="handleRemoveProductRequirement(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddProductRequirement">新增</el-button>
            </div>
          </div>

          <!-- 柜子模块 Tab -->
          <el-divider content-position="left"><i class="el-icon-suitcase" /> 柜子</el-divider>
          <div class="table-container">
            <el-table :data="cabinetList" size="small" border style="width: 100%">
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="空间" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.space2" placeholder="请输入空间" />
                </template>
              </el-table-column>
              <el-table-column label="产品名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productName" placeholder="请输入产品名称" />
                </template>
              </el-table-column>
              <el-table-column label="产品编号" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.productNumber" placeholder="请输入产品编号" />
                </template>
              </el-table-column>
              <el-table-column label="拉手编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.shakehandsNumberAndname" placeholder="请输入拉手编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="台面编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.mesanumberAndName" placeholder="请输入台面编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="柜门色板编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.cabinetdoorcolorPlatenumberAndname" placeholder="请输入柜门色板编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="柜体色板编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.cabinetbodyrcolorPlatenumberAndname" placeholder="请输入柜体色板编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="门铰编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.doorhingeNumberAndname" placeholder="请输入门铰编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remarks2" placeholder="请输入备注" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-delete" @click="handleRemoveCabinetItem(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddCabinetItem">新增</el-button>
            </div>
          </div>

          <!-- 柜类相关文件 -->
          <el-divider content-position="left"><i class="el-icon-picture-outline" /> 柜类相关文件</el-divider>
          <div class="image-upload-container">
            <el-form-item label="柜类相关文件">
              <general-file-upload
                v-model="form.cabinetRelatedPictures"
                :field-name="'cabinetRelatedPictures'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('cabinetRelatedPictures', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
          </div>

          <!-- 护墙板 Tab -->
          <el-divider content-position="left"><i class="el-icon-s-grid" /> 护墙板</el-divider>
          <div class="table-container">
            <el-table :data="wallPanelList" size="small" border style="width: 100%">
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="空间" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.space3" placeholder="请输入空间" />
                </template>
              </el-table-column>
              <el-table-column label="部位" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.place1" placeholder="请输入部位" />
                </template>
              </el-table-column>
              <el-table-column label="色板编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.colorplateNumberAndname1" placeholder="请输入色板编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="五金编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.hardwarenumberAndName1" placeholder="请输入五金编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="面料编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.plusmaterialNumberAndname1" placeholder="请输入面料编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remarks3" placeholder="请输入备注" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-delete" @click="handleRemoveWallPanel(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddWallPanel">新增</el-button>
            </div>
          </div>

          <!-- 护墙相关文件 -->
          <el-divider content-position="left"><i class="el-icon-picture-outline" /> 护墙相关文件</el-divider>
          <div class="image-upload-container">
            <el-form-item label="护墙相关文件">
              <general-file-upload
                v-model="form.wallprotectionRelatedPictures"
                :field-name="'wallprotectionRelatedPictures'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('wallprotectionRelatedPictures', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
          </div>

          <!-- 门类 Tab -->
          <el-divider content-position="left"><i class="el-icon-set-up" /> 门类</el-divider>
          <div class="table-container">
            <el-table :data="doorCategoryList" size="small" border style="width: 100%">
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="空间" min-width="120">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.space" placeholder="请输入空间" />
                </template>
              </el-table-column>
              <el-table-column label="色板编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.colorplateNumberAndname2" placeholder="请输入色板编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="面料编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.plusmaterialNumberAndname2" placeholder="请输入面料编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="不锈钢编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.stainlesssteelNumberAndname" placeholder="请输入不锈钢编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="门锁编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.gatelockNumberAndname" placeholder="请输入门锁编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="地吸编号名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.tosuckNumberAndname" placeholder="请输入地吸编号名称" />
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remarks4" placeholder="请输入备注" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-delete" @click="handleRemoveDoorCategory(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDoorCategory">新增</el-button>
            </div>
          </div>

          <!-- 门类相关文件 -->
          <el-divider content-position="left"><i class="el-icon-picture-outline" /> 门类相关文件</el-divider>
          <div class="image-upload-container">
            <el-form-item label="门类相关文件">
              <general-file-upload
                v-model="form.categoryRelatedPictures"
                :field-name="'categoryRelatedPictures'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('categoryRelatedPictures', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
          </div>

          <!-- 设备及功能需求 Tab -->
          <el-divider content-position="left"><i class="el-icon-cpu" /> 设备及功能需求</el-divider>
          <div class="table-container">
            <el-table :data="equipmentList" size="small" border style="width: 100%">
              <el-table-column label="序号" width="50" align="center">
                <template slot-scope="scope">
                  {{ scope.$index + 1 }}
                </template>
              </el-table-column>
              <el-table-column label="设备名称" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.deviceName" placeholder="请输入设备名称" />
                </template>
              </el-table-column>
              <el-table-column label="设备品牌名" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.equipmentBrandName" placeholder="请输入设备品牌名" />
                </template>
              </el-table-column>
              <el-table-column label="设备编号" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.equipmentNumber" placeholder="请输入设备编号" />
                </template>
              </el-table-column>
              <el-table-column label="设备型号规格" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.equipmentModelSpecification" placeholder="请输入设备型号规格" />
                </template>
              </el-table-column>
              <el-table-column label="设备链接" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.deviceLink" placeholder="请输入设备链接" />
                </template>
              </el-table-column>
              <el-table-column label="备注" min-width="150">
                <template slot-scope="scope">
                  <el-input v-model="scope.row.remarks5" placeholder="请输入备注" />
                </template>
              </el-table-column>
              <el-table-column label="操作" width="80" align="center" fixed="right">
                <template slot-scope="scope">
                  <el-button type="text" icon="el-icon-delete" @click="handleRemoveEquipmentItem(scope.$index)" />
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddEquipmentItem">新增</el-button>
            </div>
          </div>

          <!-- 设备相关附件 -->
          <el-divider content-position="left"> <i class="el-icon-picture-outline" /> 设备相关文件</el-divider>
          <div class="image-upload-container">
            <el-form-item label="设备相关附件">
              <general-file-upload
                v-model="form.equipmentRelatedAttachments"
                :field-name="'equipmentRelatedAttachments'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('equipmentRelatedAttachments', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="crud.cancelCU">取消</el-button>
        <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
      </div>

      <!-- 图片预览弹窗 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="Preview Image">
      </el-dialog>
    </el-dialog>

    <!--表格渲染-->
    <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
      <el-table-column type="selection" width="55" />
      <el-table-column prop="id" label="序号" width="80" />
      <el-table-column prop="nickName" label="申请人" width="120" />
      <el-table-column prop="projectName" label="项目名称" width="150" />
      <el-table-column prop="projectOverview" label="项目概况" show-overflow-tooltip />
      <el-table-column prop="projectDeliveryPeriod" label="项目交期" width="180" />
      <el-table-column prop="status" label="流程状态" width="120">
        <template slot-scope="scope">
          {{ dict.label.status[scope.row.status] }}
        </template>
      </el-table-column>
      <el-table-column v-if="checkPer(['admin','tumaiOaQwsj:edit','tumaiOaQwsj:del','tumaiOaQwsj:view'])" label="操作" width="180px" align="center">
        <template slot-scope="scope">
          <aiosUDVOperation
            :data="scope.row"
            :permission="permission"
          />
        </template>
      </el-table-column>
    </el-table>
    <!--分页组件-->
    <pagination />
  </div>
</template>

<script>
import crudTumaiOaQwsj from '@/api/aios/shop/tumaiOaQwsj'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

// 扩展默认表单数据，增加子表单数据
const defaultForm = {
  // 主表数据
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, space3: null,
  projectNumber: null, customerName: null, projectName: null, remarks: null, uid: null, projectid: null, projectDeliveryPeriod: null,
  buildingName: null, huxingStructure: null, jihua: null, typeOfService: null, projectNature: null, projectOverview: null,
  projectAddress: null, fixedPackBudget: null, tabulationDate: null, specialFunctionDescription: null,
  sizePictureAttachment: null, cabinetRelatedPictures: null, wallprotectionRelatedPictures: null, categoryRelatedPictures: null, equipmentRelatedAttachments: null, areaOfStructure: null, optdt: null, optid: null, optname: null, applydt: null, explain: null,
  status: null, createtime: null, isturn: null,

  // 子表单数据(JSON格式)
  cabinetItems: '[]', // 柜子信息
  productRequirements: '[]', // 产品需求
  wallPanels: '[]', // 护墙板
  doorCategories: '[]', // 门类
  equipmentItems: '[]' // 设备及功能需求
}

export default {
  name: 'WholeHouse',
  components: { pagination, crudOperation, rrOperation, aiosUDVOperation, GeneralFileUpload },
  mixins: [
    presenter(),
    header(),
    form(defaultForm),
    crud(),
    GeneralFileHandler.mixin,
    AiosViewButtonHelper.createViewMixin({
      hasFileFields: true,
      hasSubFormData: true
    })
  ],
  dicts: ['status'],
  cruds() {
    return CRUD({
      title: '设计立项（全屋定制）',
      url: 'api/tumaiOaQwsj',
      idField: 'id',
      sort: 'id,desc',
      crudMethod: { ...crudTumaiOaQwsj }
    })
  },
  data() {
    return {
      activeTab: 'basicInfo',
      dialogVisible: false,
      dialogImageUrl: '',

      // 子表单数据列表
      cabinetList: [], // 柜子列表
      productRequirementList: [], // 产品需求列表
      wallPanelList: [], // 护墙板列表
      doorCategoryList: [], // 门类列表
      equipmentList: [], // 设备及功能需求列表

      permission: {
        add: ['admin', 'tumaiOaQwsj:add'],
        edit: ['admin', 'tumaiOaQwsj:edit'],
        del: ['admin', 'tumaiOaQwsj:del'],
        view: ['admin', 'tumaiOaQwsj:edit', 'tumaiOaQwsj:view']
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 文件字段列表，用于初始化和提交前处理
      fileFields: [
        'sizePictureAttachment',
        'cabinetRelatedPictures',
        'wallprotectionRelatedPictures',
        'categoryRelatedPictures',
        'equipmentRelatedAttachments'
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 5,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      },
      // 文件字段映射到前端显示名
      fileFieldsMapping: {
        'sizePictureAttachment': '尺寸图',
        'cabinetRelatedPictures': '柜类相关文件',
        'wallprotectionRelatedPictures': '护墙相关文件',
        'categoryRelatedPictures': '门类相关文件',
        'equipmentRelatedAttachments': '设备相关附件'
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  watch: {
    // ... existing watch code ...
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    } else {
      console.log('文件删除API已配置:', this.minioDeleteApi)
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd]() {
      // 初始化文件字段
      this.form.sizePictureAttachment = '[]'
      this.form.cabinetRelatedPictures = '[]'
      this.form.wallprotectionRelatedPictures = '[]'
      this.form.categoryRelatedPictures = '[]'
      this.form.equipmentRelatedAttachments = '[]'

      // 清空子表单数据
      this.clearSubFormData && this.clearSubFormData()

      // 为每个子表单添加一条默认记录
      this.handleAddProductRequirement()
      this.handleAddCabinetItem()
      this.handleAddWallPanel()
      this.handleAddDoorCategory()
      this.handleAddEquipmentItem()
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      // 获取并初始化文件字段
      this.initGeneralFileFields(this.fileFields)
      // 初始化子表单数据
      this.initSubFormData && this.initSubFormData()
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, [
        'sizePictureAttachment',
        'cabinetRelatedPictures',
        'wallprotectionRelatedPictures',
        'categoryRelatedPictures',
        'equipmentRelatedAttachments'
      ])
      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initGeneralFileFields(this.fileFields)
      this.initSubFormData && this.initSubFormData()
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit]() {
      // 使用通用方法处理文件字段，转换为需要的格式
      this.prepareGeneralFileFields(this.fileFields)

      // 保存子表单数据到主表单
      this.saveSubFormData && this.saveSubFormData()
      return true
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 初始化子表单数据
    initSubFormData() {
      try {
        // 柜子
        this.cabinetList = this.form.cabinetItems ? JSON.parse(this.form.cabinetItems) : []
        // 产品需求
        this.productRequirementList = this.form.productRequirements ? JSON.parse(this.form.productRequirements) : []
        // 护墙板
        this.wallPanelList = this.form.wallPanels ? JSON.parse(this.form.wallPanels) : []
        // 门类
        this.doorCategoryList = this.form.doorCategories ? JSON.parse(this.form.doorCategories) : []
        // 设备及功能需求
        this.equipmentList = this.form.equipmentItems ? JSON.parse(this.form.equipmentItems) : []

        // 确保每个子表单至少有一条记录
        if (this.cabinetList.length === 0) this.handleAddCabinetItem()
        if (this.productRequirementList.length === 0) this.handleAddProductRequirement()
        if (this.wallPanelList.length === 0) this.handleAddWallPanel()
        if (this.doorCategoryList.length === 0) this.handleAddDoorCategory()
        if (this.equipmentList.length === 0) this.handleAddEquipmentItem()
      } catch (e) {
        console.error('解析子表单数据失败', e)
        this.clearSubFormData()

        // 发生错误时，添加默认行
        this.handleAddProductRequirement()
        this.handleAddCabinetItem()
        this.handleAddWallPanel()
        this.handleAddDoorCategory()
        this.handleAddEquipmentItem()
      }
    },

    // 清空子表单数据
    clearSubFormData() {
      this.cabinetList = []
      this.productRequirementList = []
      this.wallPanelList = []
      this.doorCategoryList = []
      this.equipmentList = []
    },

    // 保存子表单数据到主表单
    saveSubFormData() {
      this.form.cabinetItems = JSON.stringify(this.cabinetList)
      this.form.productRequirements = JSON.stringify(this.productRequirementList)
      this.form.wallPanels = JSON.stringify(this.wallPanelList)
      this.form.doorCategories = JSON.stringify(this.doorCategoryList)
      this.form.equipmentItems = JSON.stringify(this.equipmentList)
    },

    // 产品需求相关方法
    handleAddProductRequirement() {
      this.productRequirementList.push({
        space1: '',
        productName: '',
        productNumber: ''
      })
    },
    handleRemoveProductRequirement(index) {
      // 如果只有一条记录，则不允许删除
      if (this.productRequirementList.length <= 1) {
        this.$message.warning('至少保留一条产品需求记录')
        return
      }
      this.productRequirementList.splice(index, 1)
    },

    // 柜子相关方法
    handleAddCabinetItem() {
      this.cabinetList.push({
        space2: '',
        productName: '',
        productNumber: '',
        shakehandsNumberAndname: '',
        mesanumberAndName: '',
        cabinetdoorcolorPlatenumberAndname: '',
        cabinetbodyrcolorPlatenumberAndname: '',
        doorhingeNumberAndname: '',
        remarks2: ''
      })
    },
    handleRemoveCabinetItem(index) {
      // 如果只有一条记录，则不允许删除
      if (this.cabinetList.length <= 1) {
        this.$message.warning('至少保留一条柜子记录')
        return
      }
      this.cabinetList.splice(index, 1)
    },

    // 护墙板相关方法
    handleAddWallPanel() {
      this.wallPanelList.push({
        space3: '',
        place1: '',
        colorplateNumberAndname1: '',
        hardwarenumberAndName1: '',
        plusmaterialNumberAndname1: '',
        remarks3: ''
      })
    },
    handleRemoveWallPanel(index) {
      // 如果只有一条记录，则不允许删除
      if (this.wallPanelList.length <= 1) {
        this.$message.warning('至少保留一条护墙板记录')
        return
      }
      this.wallPanelList.splice(index, 1)
    },

    // 门类相关方法
    handleAddDoorCategory() {
      this.doorCategoryList.push({
        space: '',
        colorplateNumberAndname2: '',
        plusmaterialNumberAndname2: '',
        stainlesssteelNumberAndname: '',
        gatelockNumberAndname: '',
        tosuckNumberAndname: '',
        remarks4: ''
      })
    },
    handleRemoveDoorCategory(index) {
      // 如果只有一条记录，则不允许删除
      if (this.doorCategoryList.length <= 1) {
        this.$message.warning('至少保留一条门类记录')
        return
      }
      this.doorCategoryList.splice(index, 1)
    },

    // 设备及功能需求相关方法
    handleAddEquipmentItem() {
      this.equipmentList.push({
        deviceName: '',
        equipmentBrandName: '',
        equipmentNumber: '',
        equipmentModelSpecification: '',
        deviceLink: '',
        remarks5: ''
      })
    },
    handleRemoveEquipmentItem(index) {
      // 如果只有一条记录，则不允许删除
      if (this.equipmentList.length <= 1) {
        this.$message.warning('至少保留一条设备记录')
        return
      }
      this.equipmentList.splice(index, 1)
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    }
  }
}
</script>

<style scoped>
.search-box {
  margin-bottom: 20px;
}
.search-row {
  margin-bottom: 20px;
}
.filter-item {
  display: inline-block;
  vertical-align: middle;
}
.btn-col {
  display: flex;
  justify-content: flex-end;
}
.el-divider {
  margin: 15px 0;
}
.el-upload--picture-card {
  width: 120px;
  height: 120px;
  line-height: 120px;
}

.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  margin-bottom: 20px;
}

.image-upload-container .el-form-item {
  width: 100%;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.table-container {
  margin-bottom: 20px;
}

/* Fix for input fields in tables to ensure they take full width */
.el-table .el-input {
  width: 100%;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
