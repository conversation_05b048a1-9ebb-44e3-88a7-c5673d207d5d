<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">状态</label>
        <el-input v-model="query.status" clearable placeholder="状态" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="100px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 楼盘基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘编号" prop="baseSericnum">
                  <el-input v-model="form.baseSericnum" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼盘名称" prop="buildingName">
                  <el-input v-model="form.buildingName" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="物业类型" prop="propertyType">
                  <el-select v-model="form.propertyType" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.property_type"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="参考均价" prop="referenceAveragePrice">
                  <el-input v-model="form.referenceAveragePrice">
                    <template slot="append">元/平</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑类型">
                  <el-select v-model="form.constructionType" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.construction_type"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="装修标准">
                  <el-select v-model="form.decorationStandard" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.decoration_standard"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="产权年限" prop="titleFixedNumberOfYear">
                  <el-select v-model="form.titleFixedNumberOfYear" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.Title_fixed_number_of_year"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="开发商名称" prop="corporateName">
                  <el-input v-model="form.corporateName" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-location" /> 位置信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="省市区" prop="regionCode">
                  <el-cascader
                    v-model="form.regionCode"
                    :options="regionOptions"
                    style="width: 100%"
                    placeholder="请选择省/市/区"
                    filterable
                    :props="{
                      expandTrigger: 'hover',
                      value: 'value',
                      label: 'label',
                      children: 'children',
                      checkStrictly: false,
                      emitPath: true
                    }"
                    clearable
                    @change="handleRegionChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="楼盘地址">
              <el-input v-model="form.buildingAddress" />
            </el-form-item>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-sell" /> 销售信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="销售状态" prop="salesStatus">
                  <el-select v-model="form.salesStatus" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.Sales_status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="售楼地址" prop="salesAddress">
                  <el-input v-model="form.salesAddress" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="9">
                <el-form-item label="销售楼栋">
                  <el-input v-model="form.sellTheBuilding" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="销售户型">
                  <el-input v-model="form.sellHouseType" />
                </el-form-item>
              </el-col> -->
              <el-col :span="15">
                <checkbox-field
                  v-model="form.sellHouseType"
                  label="销售户型"
                  prop="sellHouseType"
                  field-name="sellHouseType"
                />
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="最新开盘日期" prop="theLatestOpeningDate">
                  <el-date-picker v-model="form.theLatestOpeningDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最近交房日期" prop="recentDeliveryDate">
                  <el-date-picker v-model="form.recentDeliveryDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="9">
                <el-form-item label="价格有效期">
                  <el-date-picker v-model="form.validityPeriodOfPrice" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="楼盘状况" prop="buildingPresentSituation">
                  <el-input v-model="form.buildingPresentSituation" />
                </el-form-item>
              </el-col> -->
              <el-col :span="15">
                <checkbox-field
                  v-model="form.buildingPresentSituation"
                  label="楼盘状况"
                  prop="buildingPresentSituation"
                  field-name="buildingPresentSituation"
                />
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 小区概况</el-divider>
            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="占地面积">
                  <el-input v-model="form.floorSpace">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="建筑面积">
                  <el-input v-model="form.coveredArea">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="绿化面积">
                  <el-input v-model="form.greenArea">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="绿化率">
                  <el-input v-model="form.greenRate" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="容积率">
                  <el-input v-model="form.plotRatio" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="入住率">
                  <el-input v-model="form.occupancyRate" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item label="规划车位">
                  <el-input v-model="form.planningParkingSpace" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="车位比">
                  <el-input v-model="form.parkingThan" />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="规划户数">
                  <el-input v-model="form.numberOfPlanningHouseholds">
                    <template slot="append">户</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="规划楼栋">
                  <el-input v-model="form.planningBuilding" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="工程进度">
                  <el-input v-model="form.jobSchedule" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-connection" /> 设施与物业</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="物业公司">
                  <el-input v-model="form.propertyManagementCompany" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="物业费用">
                  <el-input v-model="form.propertyCosts" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="6">
                <el-form-item label="供暖方式">
                  <el-select v-model="form.heatingMethod" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.Heating_method"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="供水">
                  <el-select v-model="form.waterSupply" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.water_supply"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="供电">
                  <el-select v-model="form.supplyElectricity" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.supply_electricity"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="燃气">
                  <el-select v-model="form.gas" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.gas"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-s-flag" /> 状态信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="业务员">
                  <el-input v-model="form.salesman" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司类型" prop="brandLp">
                  <el-select v-model="form.brandLp" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.brand_lp"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="制表时间" prop="createtime">
                  <el-date-picker v-model="form.createtime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态" prop="status">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>
            <el-progress v-if="uploadProgress > 0 && uploadProgress < 100" :percentage="uploadProgress" />

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘证照(附件)">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.realEstateLicense"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="位置图(附件)">
                  <div class="image-upload-container">
                    <file-upload
                      :field-value.sync="form.locationMap1"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="handleFileListChange($event, form)"
                    />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="外景图(附件)">
              <div class="image-upload-container">
                <file-upload
                  :field-value.sync="form.exteriorView"
                  :limit="5"
                  :upload-to-server="true"
                  :api-url="minioUploadApi"
                  :hide-remove="isViewMode"
                  @change="handleFileListChange($event, form)"
                />
              </div>
            </el-form-item>
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>

      <!-- 图片预览对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body title="图片预览" @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="buildingName" label="楼盘名称" />
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="brandLp" label="公司类型">
          <template slot-scope="scope">
            {{ dict.label.brand_lp[scope.row.brandLp] }}
          </template>
        </el-table-column>
        <el-table-column prop="createtime" label="制表时间" />
        <el-table-column v-if="checkPer(['admin','tumaiOaLoupan:edit','tumaiOaLoupan:del','tumaiOaLoupan:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaLoupan from '@/api/aios/shop/tumaiOaLoupan'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import CheckboxField from '@/components/CheckBox/CheckboxField'
import { regionData, codeToText } from 'element-china-area-data'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, comid: null, nickName: null, shopid: null, oddNumbers: null, baseSericnum: null, basicInformation: null, buildingName: null, name: null, propertyType: null, referenceAveragePrice: null, constructionType: null, decorationStandard: null, titleFixedNumberOfYear: null, province: null, city: null, inTheArea: null, realEstatePictures: null, locationMap: null, realEstateLicense: null, locationMap1: null, buildingNumber: null, corporateName: null, marketingInformation: null, salesStatus: null, sellTheBuilding: null, sellHouseType: null, theLatestOpeningDate: null, recentDeliveryDate: null, salesAddress: null, communityOverview: null, floorSpace: null, coveredArea: null, greenArea: null, greenRate: null, plotRatio: null, planningParkingSpace: null, parkingThan: null, planningBuilding: null, numberOfPlanningHouseholds: null, jobSchedule: null, propertyManagementCompany: null, propertyCosts: null, heatingMethod: null, waterSupply: null, supplyElectricity: null, gas: null, auditStatus: null, salesman: null, tabulationDate: null, exteriorView: null, test: null, buildingPresentSituation: null, occupancyRate: null, uid: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null, projectid: null, buildingAddress: null, buildingData: null, validityPeriodOfPrice: null, brandLp: null, createtime: null }
export default {
  name: 'TumaiOaLoupan',
  components: { pagination, crudOperation, rrOperation, FileUpload, CheckboxField, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['property_type', 'construction_type', 'decoration_standard', 'Title_fixed_number_of_year', 'Sales_status', 'Heating_method', 'water_supply', 'supply_electricity', 'gas', 'status', 'brand_lp'],
  cruds() {
    return CRUD({ title: '楼盘资料', url: 'api/shop/tumaiOaLoupan', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaLoupan }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaLoupan:add'],
        edit: ['admin', 'tumaiOaLoupan:edit'],
        del: ['admin', 'tumaiOaLoupan:del'],
        view: ['admin', 'tumaiOaLoupan:edit', 'tumaiOaLoupan:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 进度条
      uploadProgress: 0,
      realEstatePicturesList: [],
      locationMapList: [],
      realEstateLicenseList: [],
      locationMap1List: [],
      exteriorViewList: [],
      // 上传文件映射字段名
      fileFieldMap: {
        'realEstatePictures': '楼盘图片',
        'locationMap': '外景图',
        'realEstateLicense': '楼盘证照',
        'locationMap1': '位置图',
        'exteriorView': '外景图附件'
      },
      // 表单验证规则
      rules: {
        baseSericnum: [
          { required: true, message: '楼盘编号不能为空', trigger: 'blur' }
        ],
        buildingName: [
          { required: true, message: '楼盘名称不能为空', trigger: 'blur' }
        ],
        propertyType: [
          { required: true, message: '物业类型不能为空', trigger: 'blur' }
        ],
        referenceAveragePrice: [
          { required: true, message: '参考均价 (元/平)不能为空', trigger: 'blur' }
        ],
        titleFixedNumberOfYear: [
          { required: true, message: '产权年限不能为空', trigger: 'blur' }
        ],
        // regionCode: [
        //   { required: true, message: '请选择省市区', trigger: 'change' }
        // ],
        corporateName: [
          { required: true, message: '开发商名称不能为空', trigger: 'blur' }
        ],
        salesStatus: [
          { required: true, message: '销售状态不能为空', trigger: 'blur' }
        ],
        theLatestOpeningDate: [
          { required: true, message: '最新开盘日期不能为空', trigger: 'blur' }
        ],
        recentDeliveryDate: [
          { required: true, message: '最近交房日期不能为空', trigger: 'blur' }
        ],
        salesAddress: [
          { required: true, message: '售楼地址不能为空', trigger: 'blur' }
        ],
        buildingPresentSituation: [
          { required: true, message: '楼盘现状不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'blur' }
        ],
        brandLp: [
          { required: true, message: '公司类型不能为空', trigger: 'blur' }
        ],
        createtime: [
          { required: true, message: '制表时间不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'status', display_name: '状态' }
      ],
      regionOptions: regionData
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'realEstatePictures',
        'locationMap',
        'realEstateLicense',
        'locationMap1',
        'exteriorView'
      ])

      // 初始化省市区级联选择器值
      this.initRegionCode()
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.realEstatePictures = '[]'
      this.form.locationMap = '[]'
      this.form.realEstateLicense = '[]'
      this.form.locationMap1 = '[]'
      this.form.exteriorView = '[]'

      // 初始化区域代码
      this.form.regionCode = []
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        // 确保省市区数据正确设置
        if (this.form.regionCode && this.form.regionCode.length) {
          // 如果regionCode已设置，确保province, city, inTheArea字段已正确设置
          const provinceCode = this.form.regionCode[0]
          const cityCode = this.form.regionCode.length > 1 ? this.form.regionCode[1] : null
          const areaCode = this.form.regionCode.length > 2 ? this.form.regionCode[2] : null

          // 转换代码为文本
          this.form.province = provinceCode ? codeToText[provinceCode] : ''
          this.form.city = cityCode ? codeToText[cityCode] : ''
          this.form.inTheArea = areaCode ? codeToText[areaCode] : ''
        }

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'realEstatePictures',
          'locationMap',
          'realEstateLicense',
          'locationMap1',
          'exteriorView'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          realEstatePictures: this.form.realEstatePictures,
          locationMap: this.form.locationMap,
          realEstateLicense: this.form.realEstateLicense,
          locationMap1: this.form.locationMap1,
          exteriorView: this.form.exteriorView
        }))

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      let result = false

      try {
        // 主表单文件处理
        result = this.handleAttachmentChange(event, row)
        console.log('主表单处理后数据:', JSON.stringify({
          realEstatePictures: this.form.realEstatePictures,
          locationMap: this.form.locationMap,
          realEstateLicense: this.form.realEstateLicense,
          locationMap1: this.form.locationMap1,
          exteriorView: this.form.exteriorView
        }))

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 更新对话框的visible
    updateDialogVisible(visible) {
      if (!visible) {
        this.crud.status.cu = 0
      }
    },

    // 获取今天的日期字符串
    getTodayDateString() {
      const today = new Date()
      const year = today.getFullYear()
      const month = String(today.getMonth() + 1).padStart(2, '0')
      const day = String(today.getDate()).padStart(2, '0')
      return `${year}${month}${day}`
    },

    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['realEstatePictures', 'locationMap', 'realEstateLicense', 'locationMap1', 'exteriorView'])
      return true
    },

    handleRegionChange(value) {
      console.log('省市区变更:', value)
      if (value && value.length) {
        // 设置省市区的值
        const provinceCode = value[0]
        const cityCode = value.length > 1 ? value[1] : null
        const areaCode = value.length > 2 ? value[2] : null

        // 转换代码为文本
        this.form.province = codeToText[provinceCode]
        this.form.city = cityCode ? codeToText[cityCode] : ''
        this.form.inTheArea = areaCode ? codeToText[areaCode] : ''

        console.log('更新后的省市区:', this.form.province, this.form.city, this.form.inTheArea)
      } else {
        // 清空省市区
        this.form.province = ''
        this.form.city = ''
        this.form.inTheArea = ''
      }
    },

    // 初始化区域代码
    initRegionCode() {
      if (this.form.province && this.form.province !== null) {
        // 如果已有省市区数据，需要转换为级联选择器的值
        this.form.regionCode = []

        // 查找省级代码
        const findProvinceCode = () => {
          for (const provinceItem of regionData) {
            if (provinceItem.label === this.form.province) {
              return provinceItem.value
            }
          }
          return null
        }

        // 查找市级代码
        const findCityCode = (provinceCode) => {
          if (!provinceCode) return null

          for (const provinceItem of regionData) {
            if (provinceItem.value === provinceCode) {
              if (this.form.city && provinceItem.children) {
                for (const cityItem of provinceItem.children) {
                  if (cityItem.label === this.form.city) {
                    return cityItem.value
                  }
                }
              }
            }
          }
          return null
        }

        // 查找区/县级代码
        const findAreaCode = (provinceCode, cityCode) => {
          if (!provinceCode || !cityCode) return null

          for (const provinceItem of regionData) {
            if (provinceItem.value === provinceCode && provinceItem.children) {
              for (const cityItem of provinceItem.children) {
                if (cityItem.value === cityCode && cityItem.children) {
                  if (this.form.inTheArea) {
                    for (const areaItem of cityItem.children) {
                      if (areaItem.label === this.form.inTheArea) {
                        return areaItem.value
                      }
                    }
                  }
                }
              }
            }
          }
          return null
        }

        // 获取各级代码
        const provinceCode = findProvinceCode()
        const cityCode = findCityCode(provinceCode)
        const areaCode = findAreaCode(provinceCode, cityCode)

        // 设置级联选择器的值
        if (provinceCode) this.form.regionCode.push(provinceCode)
        if (cityCode) this.form.regionCode.push(cityCode)
        if (areaCode) this.form.regionCode.push(areaCode)

        console.log('初始化区域代码:', this.form.regionCode)
      }
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01);
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
