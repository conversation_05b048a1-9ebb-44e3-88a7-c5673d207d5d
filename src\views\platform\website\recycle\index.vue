<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <!-- <crudOperation :permission="permission" /> -->
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="网站ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="网站名称">
            <el-input v-model="form.webName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="网站网址">
            <el-input v-model="form.webUrl" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="添加日期">
            <el-date-picker v-model="form.addDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="添加者">
            <el-input v-model="form.addBy" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="网站ID" />
        <el-table-column prop="webName" label="网站名称" />
        <el-table-column prop="addDate" label="添加日期" />
        <el-table-column prop="addBy" label="添加者" />
        <el-table-column v-if="checkPer(['admin','webBusinessWebsite:edit','webBusinessWebsite:del','webBusinessWebsite:recycle'])" label="操作" width="280px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :hide-edit="true"
              :hide-delete="true"
              :hide-recycle="false"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudWebBusinessWebsite, { queryDel } from '@/api/website/webBusinessWebsite'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, webName: null, webUrl: null, addDate: null, addBy: null, delFlag: null }
export default {
  name: 'WebBusinessWebsite',
  components: { pagination, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({
      title: '网站商城',
      url: null,
      idField: 'id',
      sort: 'id,desc',
      crudMethod: {
        ...crudWebBusinessWebsite,
        get: queryDel // 👈 使用 queryDel 方法替代默认的 get 方法
      },
      optShow: {
        add: true,
        edit: true,
        del: false,
        download: false // 👈 关键点：关闭导出按钮
      }
    })
  },
  data() {
    return {
      permission: {
        add: ['admin', 'webBusinessWebsite:add'],
        edit: ['admin', 'webBusinessWebsite:edit'],
        del: ['admin', 'webBusinessWebsite:del'],
        recycle: ['admin', 'webBusinessWebsite:recycle']
      },
      rules: {
      }
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      const self = this
      const { currentPage, size } = this.crud.page
      const params = { pageNum: currentPage, pageSize: size }

      queryDel(params).then(res => {
        const list = res.content || []
        // 手动初始化 dataStatus
        list.forEach(item => {
          const id = item.id
          if (!self.crud.dataStatus[id]) {
            self.$set(self.crud.dataStatus, id, {
              create: 0,
              update: 0,
              delete: 0
            })
          }
        })

        self.crud.data = list
        self.crud.page.total = res.totalElements
        self.crud.loading = false
      }).catch(() => {
        self.crud.loading = false
      })

      return false // 阻止默认刷新逻辑
    },
    handleExport(row) {
      // 调用接口或者触发下载逻辑
      if (typeof window !== 'undefined') {
        const url = 'https://pc.talmdcloud.com' + row.webUrl
        window.open(url, '_blank')
      }
    }
  }
}
</script>
