<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="精装概念计划完成时间">
                  <el-date-picker v-model="form.planCompletionTime2" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddress" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="客户确认时间(精装概念方案)">
                  <el-date-picker v-model="form.customerConfirmTime2" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-form-item label="最终精装概念方案PDF格式附件">
              <general-file-upload
                v-model="form.pdfFormaFile2"
                :field-name="'pdfFormaFile2'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :hide-remove="isViewMode"
                @change="handleFileChange('pdfFormaFile2', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>

            <el-dialog :visible.sync="dialogVisible">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>

            <el-divider content-position="left"><i class="el-icon-document" /> 精装设计版本信息</el-divider>
            <div class="table-container">
              <el-table :data="designDetailsList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="设计师" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.designer" placeholder="请输入设计师" />
                  </template>
                </el-table-column>
                <el-table-column label="PDF格式附件" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.pdfFormaFile3"
                      size="small"
                      :field-name="'pdfFormaFile3'"
                      v-bind="tableFileUploadConfig"
                      :use-minio-delete="true"
                      list-type="text"
                      show-file-list
                      show-file-name
                      :hide-remove="isViewMode"
                      @change="handleSubformFileChange($event, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription2" placeholder="请输入异常说明" :rows="3" type="textarea" />
                  </template>
                </el-table-column>
                <el-table-column label="版本" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.drawingVersion" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="开始制作日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.startTheProductionDate" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="实际完成日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.actualvcompletionDate" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.commitStatus" placeholder="请输入状态" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveDesignDetail(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDesignDetail">新增</el-button>
              </div>
            </div>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjJingzhuang:edit','tumaiSjJingzhuang:del','tumaiSjJingzhuang:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiSjJingzhuang from '@/api/aios/designport/deluxedesign/tumaiSjJingzhuang'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, tabDate: null,
  projectNumber: null, projectName: null, projectOverview: null, planCompletionTime: null,
  projectNature: null, uid: null, planCompletionTime2: null, optdt: null, optid: null,
  optname: null, applydt: null, explain: null, status: null, isturn: null, approval: null,
  cadFormatFile2: null, finalFloorPlanCoverPage: null, pdfFormaFile2: null, finalLayoutPlan: null,
  projectid: null, projectDeliveryPeriod: null, projectAddress: null, typeOfService: null,
  drawingVersion: null, customerConfirmTime2: null, pdfFormaFile3: null, createtime: null,
  customerSignFile2: null,
  // 精装设计详情以数组形式存储
  designDetails: '[]'
}
export default {
  name: 'TumaiSjJingzhuang',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'drawing_version'],
  cruds() {
    return CRUD({ title: '精装概念设计', url: 'api/tumaiSjJingzhuang', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjJingzhuang }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjJingzhuang:add'],
        edit: ['admin', 'tumaiSjJingzhuang:edit'],
        del: ['admin', 'tumaiSjJingzhuang:del'],
        view: ['admin', 'tumaiSjJingzhuang:edit', 'tumaiSjJingzhuang:view']
      },
      // 精装设计详情列表
      designDetailsList: [],
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // PDF文件上传配置
      fileUploadConfig: {
        accept: '.pdf', // 仅限PDF格式
        maxFiles: 5,
        tipText: '只支持上传PDF格式文件',
        buttonText: '上传PDF文件',
        listType: 'text',
        useMinioDelete: true
      },
      // 表格中文件上传配置
      tableFileUploadConfig: {
        accept: '.pdf,application/pdf',
        maxFiles: 2,
        tipText: '仅支持上传PDF文件',
        buttonText: '上传PDF',
        listType: 'text',
        useMinioDelete: true,
        showFileList: true,
        showFileName: true
      },
      // 文件字段定义
      fileFields: {
        pdfFormaFile2: 'PDF格式文件',
        pdfFormaFile3: 'PDF格式附件'
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，PDF文件删除功能可能无法正常工作')
    } else {
      console.log('文件删除API已配置:', this.minioDeleteApi)
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'cadFormatFile2',
        'finalFloorPlanCoverPage',
        // 'pdfFormaFile2', // 现在使用GeneralFileHandler处理
        'finalLayoutPlan',
        'customerSignFile2'
      ])
      // 初始化通用文件字段
      this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
      // 初始化精装设计详情列表
      this.initDesignDetailsList()
      // 初始化子表单中的文件字段
      this.initSubformFileFields(this.designDetailsList, 'pdfFormaFile3')
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      // 删除主表单的文件
      deleteRecordFiles(data, ['pdfFormaFile2'])

      // 删除子表单中的文件
      deleteSubformFiles(data, 'designDetails', 'pdfFormaFile3')

      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'cadFormatFile2',
        'finalFloorPlanCoverPage',
        'finalLayoutPlan',
        'customerSignFile2'
      ])
      this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)
      this.initDesignDetailsList()
      this.initSubformFileFields(this.designDetailsList, 'pdfFormaFile3')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.cadFormatFile2 = '[]'
      this.form.finalFloorPlanCoverPage = '[]'
      this.form.pdfFormaFile2 = '[]'
      this.form.finalLayoutPlan = '[]'
      this.form.customerSignFile2 = '[]'

      this.designDetailsList = []

      // 添加一个空的详情记录
      this.handleAddDesignDetail()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始子表单数据:', JSON.stringify(this.designDetailsList))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'cadFormatFile2',
          'finalFloorPlanCoverPage',
          // 'pdfFormaFile2', // 现在使用GeneralFileHandler处理
          'finalLayoutPlan',
          'customerSignFile2'
        ])

        // 处理通用文件字段
        this.prepareGeneralFileFields && this.prepareGeneralFileFields(this.fileFields)

        // 处理子表单中的文件字段
        this.prepareSubformFileFields(this.designDetailsList, 'pdfFormaFile3')

        // 将处理后的子表单数据同步回表单
        this.updateFormDesignDetails()

        console.log('处理后的表单数据:', this.form.designDetails)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化精装设计详情列表
    initDesignDetailsList() {
      try {
        console.log('初始化精装设计详情列表')
        this.designDetailsList = this.form.designDetails ? JSON.parse(this.form.designDetails) : []
        if (!Array.isArray(this.designDetailsList)) {
          this.designDetailsList = []
        }

        // 确保每行都有PDF附件字段
        this.designDetailsList.forEach(item => {
          if (!item.pdfFormaFile3) {
            item.pdfFormaFile3 = '[]'
          } else if (typeof item.pdfFormaFile3 === 'string' && !item.pdfFormaFile3.startsWith('[')) {
            // 处理URL格式的情况，转换为标准JSON格式
            item.pdfFormaFile3 = this.convertUrlsToFileArray(item.pdfFormaFile3)
          }
        })

        // 如果没有数据，默认添加一条空记录
        if (this.designDetailsList.length === 0) {
          this.handleAddDesignDetail()
        }
      } catch (e) {
        console.error('解析精装设计详情数据失败:', e)
        this.designDetailsList = []
      }
    },

    // 将URL字符串转换为文件数组
    convertUrlsToFileArray(urlStr) {
      if (!urlStr) return '[]'

      try {
        // 如果已经是JSON格式，直接返回
        if (urlStr.startsWith('[') && urlStr.endsWith(']')) {
          return urlStr
        }

        // 处理逗号分隔的多个URL
        const urls = urlStr.split(',')
        const files = urls.map(url => {
          // 从URL提取文件名
          const fileName = url.substring(url.lastIndexOf('/') + 1)
          return {
            name: fileName,
            url: url,
            size: 0,
            type: ''
          }
        })

        return JSON.stringify(files)
      } catch (e) {
        console.error('URL转换文件数组出错:', e)
        return '[]'
      }
    },

    // 更新表单中的精装设计详情
    updateFormDesignDetails() {
      try {
        console.log('将精装设计详情列表同步回表单')
        this.form.designDetails = JSON.stringify(this.designDetailsList)
        return true
      } catch (error) {
        console.error('同步精装设计详情数据出错:', error)
        this.$message.error('同步精装设计详情数据失败')
        return false
      }
    },

    // 添加精装设计详情
    handleAddDesignDetail() {
      this.designDetailsList.push({
        designer: null,
        pdfFormaFile3: '[]',
        exceptionDescription2: null,
        drawingVersion: null,
        startTheProductionDate: null,
        actualvcompletionDate: null,
        commitStatus: null
      })
    },

    // 移除精装设计详情
    handleRemoveDesignDetail(index) {
      // 获取要删除的行数据
      const rowData = this.designDetailsList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        console.log('删除子表单行文件，行数据:', rowData)
        deleteSubformRowFiles(rowData, 'pdfFormaFile3')
      }
      this.designDetailsList.splice(index, 1)
      // 如果删除后列表为空，添加一个新的空行
      if (this.designDetailsList.length === 0) {
        this.handleAddDesignDetail()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', event)

      if (row === this.form) {
        // 主表单文件处理
        return this.handleAttachmentChange(event, row)
      } else {
        // 子表单文件处理
        return this.handleSubformFileChange(event, row)
      }
    },

    // 处理子表单的文件变更
    handleSubformFileChange(event, row) {
      if (!event || !event.fieldName || !row) {
        return false
      }

      const fieldName = event.fieldName
      const files = event.files || []

      console.log(`子表单文件 ${fieldName} 更新:`, files)

      // 将文件数组转换为JSON字符串存储
      row[fieldName] = JSON.stringify(files)

      return true
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },
    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 120px;
  height: 120px;
  line-height: 120px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 120px;
  height: 120px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.el-table .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 80px;
}

.el-table .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}
</style>
