<template>
  <div class="general-file-upload">
    <el-upload
      :action="uploadUrl"
      :limit="maxFiles"
      :file-list="fileList"
      :on-exceed="handleExceed"
      :on-success="handleSuccess"
      :before-remove="handleBeforeRemove"
      :on-remove="handleRemove"
      :on-error="handleError"
      :on-preview="handlePreview"
      :before-upload="beforeUpload"
      :headers="headers"
      :accept="accept"
      :disabled="disabled"
      :multiple="multiple"
      :list-type="listType"
      class="general-uploader"
    >
      <el-button v-if="!imageMode" :disabled="disabled" size="small" type="primary">
        <i class="el-icon-upload" /> {{ buttonText }}
      </el-button>
      <div v-else class="general-uploader-image">
        <i class="el-icon-plus" />
        <div class="general-uploader-text">{{ buttonText }}</div>
      </div>

      <div v-if="showTip" slot="tip" class="el-upload__tip">
        <span>{{ tipText }}</span>
        <span v-if="showLimitTip">，最多上传 {{ maxFiles }} 个文件</span>
      </div>
    </el-upload>

    <!-- 文件预览对话框 -->
    <el-dialog
      :visible.sync="previewVisible"
      :append-to-body="true"
    >
      <div slot="title" class="file-preview-title">
        <span>{{ previewFile.name || '文件预览' }}</span>
      </div>

      <!-- 图片预览 -->
      <div v-if="isPreviewImage">
        <img width="100%" :src="previewFile.url" alt="图片预览">
      </div>

      <!-- PDF预览 -->
      <div v-else-if="isPreviewPdf" class="preview-pdf-container">
        <iframe :src="previewFile.url" class="preview-pdf" frameborder="0" />
      </div>

      <!-- 其他文件类型 -->
      <div v-else class="preview-download-container">
        <div class="preview-download-icon">
          <span :class="getFileIcon(previewFile.name)" />
        </div>
        <div class="preview-download-info">
          <p>当前文件类型不支持在线预览，请下载后查看</p>
          <p v-if="previewFile.size">文件大小: {{ formatSize(previewFile.size) }}</p>
          <el-button type="primary" size="small" @click="downloadFile(previewFile)">
            <class="el-icon-download" /> 下载文件
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios'
import { getToken } from '@/utils/auth'
import { getFileIcon, formatFileSize } from '@/utils/generalFileUpload'

export default {
  name: 'GeneralFileUpload',
  props: {
    uploadUrl: {
      type: String,
      default: '/api/minio/upload'
    },
    deleteUrl: {
      type: String,
      default: '/api/minio/delete'
    },
    maxFiles: {
      type: Number,
      default: 5
    },
    maxSize: {
      type: Number,
      default: 15
    },
    accept: {
      type: String,
      default: '.jpg,.jpeg,.png,.gif,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx'
    },
    buttonText: {
      type: String,
      default: '上传文件'
    },
    tipText: {
      type: String,
      default: '支持jpg、png、pdf等常见文件格式，单个文件不超过15MB'
    },
    showTip: {
      type: Boolean,
      default: true
    },
    showLimitTip: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple: {
      type: Boolean,
      default: true
    },
    value: {
      type: Array,
      default: () => []
    },
    fieldName: {
      type: String,
      default: 'file'
    },
    listType: {
      type: String,
      default: 'text',
      validator: val => ['text', 'picture', 'picture-card'].includes(val)
    },
    useMinioDelete: {
      type: Boolean,
      default: true
    },
    // 是否隐藏删除按钮（查看模式下使用）
    hideRemove: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      previewVisible: false,
      previewFile: {},
      processingList: [] // 正在处理的文件列表
    }
  },
  computed: {
    imageMode() {
      return this.listType === 'picture-card'
    },
    isPreviewImage() {
      if (!this.previewFile.name) return false
      const ext = this.previewFile.name.split('.').pop().toLowerCase()
      return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(ext)
    },
    isPreviewPdf() {
      if (!this.previewFile.name) return false
      const ext = this.previewFile.name.split('.').pop().toLowerCase()
      return ext === 'pdf'
    },
    headers() {
      return {
        Authorization: getToken()
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        this.initFileList(newVal)
      },
      immediate: true
    }
  },
  created() {
    this.initFileList(this.value)
  },
  methods: {
    initFileList(value) {
      if (!value) {
        this.fileList = []
        return
      }
      if (typeof value === 'string') {
        if (value.startsWith('[') && value.endsWith(']')) {
          try {
            const parsed = JSON.parse(value)
            if (Array.isArray(parsed)) {
              this.fileList = parsed.map(item => {
                return {
                  name: item.name || this.getFileName(item.url),
                  url: item.url,
                  size: item.size
                }
              })
              return
            }
          } catch (e) {
            console.error('解析文件列表JSON失败:', e)
          }
        }
        if (value) {
          const urls = value.split(',').filter(url => url.trim())
          this.fileList = urls.map(url => {
            return {
              name: this.getFileName(url),
              url: url
            }
          })
        } else {
          this.fileList = []
        }
      } else if (Array.isArray(value)) {
        this.fileList = value.map(item => {
          if (typeof item === 'string') {
            return {
              name: this.getFileName(item),
              url: item
            }
          } else {
            return {
              name: item.name || this.getFileName(item.url),
              url: item.url,
              size: item.size
            }
          }
        })
      }
    },
    getFileName(url) {
      if (!url) return '未知文件'
      const parts = url.split('/')
      return parts[parts.length - 1] || '未知文件'
    },
    formatSize(size) {
      return formatFileSize(size)
    },
    getFileIcon(filename) {
      return getFileIcon(filename)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`最多只能上传 ${this.maxFiles} 个文件`)
    },
    beforeUpload(file) {
      const isLtSize = file.size / 1024 / 1024 < this.maxSize
      if (!isLtSize) {
        this.$message.error(`文件大小不能超过 ${this.maxSize}MB`)
        return false
      }
      this.processingList.push(file)
      return true
    },

    // 添加获取完整URL的方法
    getFullImageUrl(url) {
      if (!url) return ''

      // 如果已经是完整URL则直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        return url
      }

      // 如果URL包含域名但没有协议，添加HTTPS协议
      if (url.includes('.com/') || url.includes('.cn/') || url.includes('.net/')) {
        return `https://${url}`
      }

      // 如果是相对路径，通过后端API获取
      return `/api/minio/url?fileName=${encodeURIComponent(url)}`
    },

    handleSuccess(response, file, fileList) {
      if (!response || !response.url) {
        this.$message.error('上传失败: 未获取到文件URL')
        fileList = fileList.filter(item => item.uid !== file.uid)
        this.updateValue(fileList)
        return
      }

      // 处理返回的URL，确保是完整URL
      const fullUrl = this.getFullImageUrl(response.url)

      const fileItem = fileList.find(item => item.uid === file.uid)
      if (fileItem) {
        fileItem.url = fullUrl
        fileItem.name = file.name || this.getFileName(fullUrl)
        fileItem.size = file.size || response.size
      }
      const procIndex = this.processingList.findIndex(item => item.uid === file.uid)
      if (procIndex > -1) {
        this.processingList.splice(procIndex, 1)
      }
      const event = {
        action: 'success',
        file: {
          name: file.name,
          url: response.url,
          size: file.size,
          type: file.type,
          response
        },
        fieldName: this.fieldName
      }
      this.fileList = [...fileList]
      this.updateValue(fileList)
      this.$emit('file-change', event)
    },

    // 删除前确认
    handleBeforeRemove(file, fileList) {
      // 如果隐藏删除按钮，则阻止删除操作
      if (this.hideRemove) {
        console.log('查看模式下禁止删除文件')
        return false
      }

      // 删除确认提示
      const confirmMessage = `确定要删除吗？\n\n删除后需要点击"提交"按钮保存更改。`

      return this.$confirm(confirmMessage, '⚠️ 删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false,
        customClass: 'delete-confirm-dialog'
      }).then(() => {
        // 用户确认删除，返回true允许删除
        return true
      }).catch(() => {
        // 用户取消删除，抛出错误阻止删除
        console.log('用户取消删除操作')
        throw new Error('用户取消删除')
      })
    },

    // 处理文件移除（删除确认后执行）
    handleRemove(file) {
      const fileList = this.fileList.filter(item => item.uid !== file.uid)
      this.fileList = [...fileList]
      const data = fileList.map(item => ({
        name: item.name || this.getFileName(item.url) || '未命名文件',
        url: item.url || '',
        size: item.size || 0,
        type: item.type || ''
      }))
      this.$emit('input', JSON.stringify(data))
      this.$emit('change', data)
      this.$emit('file-change', {
        action: 'remove',
        fieldName: this.fieldName,
        file: file,
        fileList: data
      })

      if (this.useMinioDelete && file.url && this.deleteUrl) {
        const fileUrl = file.url
        const fileName = fileUrl.split('/').pop()
        console.log('删除文件，完整URL:', fileUrl, '文件名:', fileName)
        axios.get(`${this.deleteUrl}?fileName=${encodeURIComponent(fileUrl)}`, {
          headers: this.headers
        })
          .then(response => {
            if (response.data && response.data.success) {
              this.$notify({
                title: '✅ 删除成功',
                message: '文件已删除，🔔 请记得点击"确认"按钮保存更改！',
                type: 'warning',
                duration: 4000,
                position: 'top-right'
              })
              console.log('文件删除成功:', fileUrl)
            } else {
              this.$message.error('删除文件失败')
              console.error('删除文件失败:', response.data)
            }
          })
          .catch(error => {
            this.$message.error('删除文件出错')
            console.error('删除文件出错:', error)
          })
      } else {
        // 没有服务器删除时也给个提示
        this.$notify({
          title: '📝 文件已移除',
          message: '🔔 请记得点击"提交"按钮保存更改！',
          type: 'warning',
          duration: 5000,
          position: 'top-right'
        })
      }
    },
    handleError(err, file, fileList) {
      this.$message.error('上传失败: ' + (err.message || '未知错误'))
      const procIndex = this.processingList.findIndex(item => item.uid === file.uid)
      if (procIndex > -1) {
        this.processingList.splice(procIndex, 1)
      }
    },
    handlePreview(file) {
      const isImage = file.name && /\.(jpg|jpeg|png|gif|bmp|webp)$/i.test(file.name)
      if (isImage) {
        this.previewFile = file
        this.previewVisible = true
      } else {
        this.downloadFile(file)
      }
    },
    downloadFile(file) {
      if (!file.url) {
        this.$message.error('无法下载: 文件URL为空')
        return
      }
      try {
        const link = document.createElement('a')
        link.href = file.url
        const fileName = file.name || this.getFileName(file.url)
        link.download = fileName
        link.target = '_blank'
        document.body.appendChild(link)
        link.click()
        setTimeout(() => {
          document.body.removeChild(link)
        }, 100)
        this.$message.success('开始下载文件: ' + fileName)
      } catch (error) {
        console.error('下载文件出错:', error)
        this.$message.error('下载文件失败，请重试')
        window.open(file.url, '_blank')
      }
    },
    updateValue(fileList) {
      const validFiles = fileList.filter(file =>
        file.url || (file.response && file.response.url)
      )
      const data = validFiles.map(file => {
        const url = file.url || (file.response && file.response.url) || ''
        return {
          name: file.name || this.getFileName(url) || '未命名文件',
          url: url,
          size: file.size || 0,
          type: file.type || ''
        }
      })
      this.fileList = validFiles.map(file => {
        if (!file.url && file.response && file.response.url) {
          file.url = file.response.url
        }
        return { ...file }
      })
      const dataJson = JSON.stringify(data)
      this.$emit('input', dataJson)
      this.$emit('change', data)
      this.$nextTick(() => {
        if (this.fieldName) {
          this.$emit('file-change', {
            action: 'sync',
            fieldName: this.fieldName,
            fileList: data
          })
        }
      })
    },
    submit() {
      return new Promise((resolve, reject) => {
        if (this.processingList.length > 0) {
          this.$message.warning('有文件正在上传中，请稍候...')
          reject(new Error('文件上传中'))
          return
        }
        resolve(this.fileList)
      })
    },
    clearFiles() {
      this.fileList = []
      this.$emit('input', '[]')
      this.$emit('change', [])
    }
  }
}
</script>

<style lang="scss" scoped>
.general-file-upload {
  width: 100%;

  .general-uploader {
    width: 100%;
  }

  .general-uploader-image {
    width: 148px;
    height: 148px;
    line-height: 148px;
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    text-align: center;

    &:hover {
      border-color: #409EFF;
    }

    .el-icon-plus {
      font-size: 28px;
      color: #8c939d;
    }

    .general-uploader-text {
      font-size: 12px;
      color: #606266;
      position: absolute;
      bottom: 10px;
      left: 0;
      right: 0;
      line-height: 1.2;
    }
  }

  .file-preview-title {
    word-break: break-all;
  }

  .preview-pdf-container {
    height: 600px;
    width: 100%;

    .preview-pdf {
      width: 100%;
      height: 100%;
    }
  }

  .preview-download-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 20px;
    text-align: center;

    .preview-download-icon {
      font-size: 60px;
      color: #606266;
      margin-bottom: 20px;
    }

    .preview-download-info {
      p {
        margin: 5px 0;
        color: #606266;
      }
    }
  }
}
</style>
