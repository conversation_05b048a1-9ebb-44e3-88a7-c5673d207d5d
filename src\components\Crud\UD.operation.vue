<template>
  <div>
    <el-tooltip class="item" effect="dark" content="编辑" placement="top">
      <el-button v-if="!hideEdit" v-permission="permission.edit" :loading="crud.status.cu === 2" :disabled="disabledEdit" size="mini" type="primary" icon="el-icon-edit" @click.stop="crud.toEdit(data)" />
    </el-tooltip>
    <el-tooltip class="item" effect="dark" content="删除" placement="top">
      <el-popover v-model="pop" v-permission="permission.del" placement="top" width="180" trigger="manual" @show="onPopoverShow" @hide="onPopoverHide">
        <p>{{ msg }}</p>
        <div style="text-align: right; margin: 0">
          <el-button size="mini" type="text" @click="doCancel">取消</el-button>
          <el-button :loading="crud.dataStatus[crud.getDataId(data)].delete === 2" type="primary" size="mini" @click="crud.doDelete(data)">确定</el-button>
        </div>
        <el-button v-if="!hideDelete" slot="reference" :disabled="disabledDle" type="danger" icon="el-icon-delete" size="mini" @click.stop="toDelete" />
      </el-popover>
    </el-tooltip>
    <el-tooltip class="item" effect="dark" content="恢复" placement="top">
      <el-popover v-model="recyclePop" v-permission="permission.recycle" placement="top" width="180" trigger="manual" @show="onRecyclePopoverShow" @hide="onRecyclePopoverHide">
        <p>是否恢复数据？</p>
        <div style="text-align: right; margin: 0">
          <el-button size="mini" type="text" @click="cancelRecycle">取消</el-button>
          <el-button :loading="recycleLoading" type="primary" size="mini" @click="confirmRecycle">确定</el-button>
        </div>
        <el-button v-if="!hideRecycle" slot="reference" :disabled="disabledRecycle" type="warning" icon="el-icon-refresh-right" size="mini" @click.stop="toRecycle" />
      </el-popover>
    </el-tooltip>
  </div>
</template>
<script>
import CRUD, { crud } from '@crud/crud'
import { recycle } from '@/api/website/webBusinessWebsite'

export default {
  mixins: [crud()],
  props: {
    data: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    disabledEdit: {
      type: Boolean,
      default: false
    },
    disabledDle: {
      type: Boolean,
      default: false
    },
    disabledRecycle: {
      type: Boolean,
      default: false
    },
    msg: {
      type: String,
      default: '确定删除本条数据吗？'
    },
    hideRecycle: { type: Boolean, default: true }, // true默认隐藏，false默认显示
    hideEdit: { type: Boolean, default: false },
    hideDelete: { type: Boolean, default: false }
  },
  data() {
    return {
      pop: false,
      recyclePop: false,
      recycleLoading: false
    }
  },
  methods: {
    doCancel() {
      this.pop = false
      this.crud.cancelDelete(this.data)
    },
    toDelete() {
      this.pop = true
    },
    [CRUD.HOOK.afterDelete](crud, data) {
      if (data === this.data) {
        this.pop = false
      }
    },
    onPopoverShow() {
      setTimeout(() => {
        document.addEventListener('click', this.handleDocumentClick)
      }, 0)
    },
    onPopoverHide() {
      document.removeEventListener('click', this.handleDocumentClick)
    },
    handleDocumentClick(event) {
      this.pop = false
    },
    toRecycle() {
      this.recyclePop = true
    },
    cancelRecycle() {
      this.recyclePop = false
    },
    confirmRecycle() {
      this.recycleLoading = true
      const id = this.data.id
      recycle({ id })
        .then(res => {
          this.recyclePop = false
          this.crud.refresh()
          // 使用统一提示
          this.crud.recycleSuccessNotify() // 显示 “恢复成功”
        })
        .catch(() => {
          this.crud.notify('恢复失败', CRUD.NOTIFICATION_TYPE.ERROR)
        })
        .finally(() => {
          this.recycleLoading = false
        })
    },
    onRecyclePopoverShow() {
      setTimeout(() => {
        document.addEventListener('click', this.handleRecycleDocumentClick)
      }, 0)
    },
    onRecyclePopoverHide() {
      document.removeEventListener('click', this.handleRecycleDocumentClick)
    },
    handleRecycleDocumentClick(event) {
      this.recyclePop = false
    }
  }
}
</script>

<style>
.box {
  width: 400px;

  .top {
    text-align: center;
  }

  .left {
    float: left;
    width: 60px;
  }

  .right {
    float: right;
    width: 60px;
  }

  .bottom {
    clear: both;
    text-align: center;
  }

  .item {
    margin: 4px;
  }

  .left .el-tooltip__popper,
  .right .el-tooltip__popper {
    padding: 8px 10px;
  }
}
</style>
