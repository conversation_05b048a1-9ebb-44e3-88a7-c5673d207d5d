<template>
  <div class="statistics-item">
    <div class="tit-bar">
      <span>{{ title }}</span>
      <img class="tit_spot" :src="spotImg" alt="">
    </div>

    <div class="statistics-sub">
      <div class="statistics-tit">国家</div>
      <div class="statistics-unit">数量(个)</div>
    </div>

    <div class="statistics-cart">
      <div
        v-for="(item, index) in items"
        :key="index"
        class="cart-item"
      >
        <div class="statistics-region">{{ item.region }}</div>
        <div class="statistics-num">{{ item.num }}</div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      required: true
    },
    items: {
      type: Array,
      default: () => []
    },
    spotImg: {
      type: String,
      default: 'https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png'
    }
  }
}
</script>

<style rel='stylesheet/scss' lang='scss' scoped>
@import '@/assets/styles/bi.scss';

</style>
