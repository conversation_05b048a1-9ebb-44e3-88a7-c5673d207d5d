<template>
  <div class="table-data-container">
    <!-- 表格头部信息 -->
    <div class="table-header">
      <div class="table-info">
        <div class="table-name-section">
          <span v-if="tableName" class="table-name">{{ tableName }}</span>
          <span
            v-if="tableComment"
            class="table-comment"
            title="点击编辑表注释"
            @click="showCommentDialog = true"
          >
            ({{ tableComment }})
          </span>
          <span
            v-else
            class="add-comment-link"
            title="点击添加表注释"
            @click="showCommentDialog = true"
          >
            <i class="el-icon-edit comment-icon" />
            添加注释
          </span>
        </div>
        <span class="record-count">共 {{ totalItems }} 条记录</span>
      </div>
      <div class="table-actions">
        <el-button size="small" :loading="loading" icon="el-icon-refresh" @click="refreshData">
          刷新
        </el-button>
        <el-button size="small" icon="el-icon-download" @click="exportData">
          导出
        </el-button>
      </div>
    </div>

    <!-- 可滚动的表格容器 -->
    <div v-loading="loading" class="table-scroll-container" element-loading-text="加载中...">
      <div v-if="tableData.length" class="table-wrapper">
        <table class="data-table">
          <thead>
            <tr>
              <th v-for="header in headers" :key="header" class="table-header-cell">
                <div class="header-content">
                  <span class="header-text">{{ header }}</span>
                  <span class="header-type">{{ getColumnType(header) }}</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(row, index) in tableData" :key="index" class="table-row">
              <td v-for="header in headers" :key="header" class="table-cell">
                <div class="cell-content" :title="String(row[header])">
                  {{ formatCellValue(row[header]) }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <div v-else class="no-data-container">
        <div class="no-data">
          <i class="el-icon-document no-data-icon" />
          <p>暂无数据</p>
        </div>
      </div>
    </div>

    <!-- 分页组件 -->
    <div class="pagination-container">
      <el-pagination
        :current-page="currentPage"
        :page-size="pageSize"
        :page-sizes="[10, 20, 50, 100, 200, 500, 1000, 2000, 5000]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="totalItems"
        background
        @size-change="handleSizeChange"
        @current-change="handlePageChange"
      />
    </div>

    <!-- 表注释对话框 -->
    <el-dialog
      :visible.sync="showCommentDialog"
      title="表注释"
      width="350px"
      :before-close="handleCloseCommentDialog"
    >
      <el-form :model="commentForm" label-width="60px">
        <el-form-item label="表名">
          <el-input v-model="tableName" disabled />
        </el-form-item>
        <el-form-item label="注释">
          <el-input
            v-model="commentForm.comment"
            placeholder="例如：商品表、用户表（限10字）"
            maxlength="10"
            show-word-limit
            @keyup.enter="saveTableComment"
          />
          <div class="comment-tip">
            简短描述表的用途，方便非开发人员理解
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showCommentDialog = false">取消</el-button>
        <el-button type="primary" :loading="commentSaving" @click="saveTableComment">
          {{ tableComment ? '更新' : '保存' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTableData } from '@/api/AnalyseDB/DbConnection'
import { saveTableComment as saveTableCommentApi, getTableComment as getTableCommentApi } from '@/api/AnalyseDB/TableData'

export default {
  name: 'TableData',
  props: {
    dbName: {
      type: String,
      default: ''
    },
    tableName: {
      type: String,
      default: ''
    },
    page: {
      type: Number,
      default: 1
    },
    size: {
      type: Number,
      default: 10
    }
  },
  data() {
    return {
      tableData: [],
      headers: [],
      HandletableData: [],
      columnComment: [],

      // 分页相关数据
      currentPage: 1,
      pageSize: 10,
      totalItems: 0,
      loading: false,

      // 表注释相关
      tableComment: '',
      showCommentDialog: false,
      commentSaving: false,
      commentForm: {
        comment: ''
      }
    }
  },
  watch: {
    dbName: {
      handler() {
        this.handlePropsChange()
      }
    },
    tableName: {
      handler() {
        this.handlePropsChange()
      }
    },
    page: {
      handler() {
        this.handlePropsChange()
      }
    },
    size: {
      handler() {
        this.handlePropsChange()
      }
    },
    showCommentDialog: {
      handler(newVal) {
        if (newVal) {
          this.commentForm.comment = this.tableComment
        }
      }
    }
  },
  methods: {
    async handlePropsChange() {
      if (this.dbName && this.tableName) {
        await this.fetchTableData(this.dbName, this.tableName, this.page, this.size)
        await this.loadTableComment()
      }
    },

    async fetchTableData(dbName, tableName, currentPage, pageSize) {
      this.loading = true
      try {
        // 构造请求参数对象
        const getTableDataDto = {
          dbName,
          tableName,
          currentPage,
          pageSize
        }
        // 调用后端接口
        const response = await getTableData(getTableDataDto)

        // 处理返回数据
        this.headers = response.headers || []
        this.tableData = response.data || []
        this.totalItems = response.total || 0
        this.columnComment = response.columnComment || []

        // 将 tableData 转换为数组格式
        if (response.data && response.data.length > 0) {
          this.HandletableData = response.data.map(row => {
            return this.headers.map(header => row[header])
          })
        } else {
          this.HandletableData = []
        }

        this.$emit('table-data', { headers: this.headers, data: this.HandletableData, tableName })
      } catch (error) {
        this.$message.error('获取表数据失败')
      } finally {
        this.loading = false
      }
    },

    // 处理页码变化
    handleSizeChange(newSize) {
      this.pageSize = newSize
      this.currentPage = 1 // 重置到第一页
      this.$emit('update:size', newSize)
      this.fetchTableData(this.dbName, this.tableName, this.currentPage, this.pageSize)
    },

    // 处理页数变化
    handlePageChange(newPage) {
      this.currentPage = newPage
      this.$emit('update:page', newPage)
      this.fetchTableData(this.dbName, this.tableName, this.currentPage, this.pageSize)
    },

    // 格式化单元格值
    formatCellValue(value) {
      if (value === null || value === undefined) {
        return 'NULL'
      }
      if (typeof value === 'string' && value.length > 100) {
        return value.substring(0, 100) + '...'
      }
      return String(value)
    },

    getColumnType(header) {
      const index = this.headers.indexOf(header)
      return index !== -1 ? (this.columnComment[index] || '') : ''
    },

    // 刷新数据
    refreshData() {
      if (this.dbName && this.tableName) {
        this.fetchTableData(this.dbName, this.tableName, this.currentPage, this.pageSize)
      }
    },

    // 导出数据
    exportData() {
      if (!this.tableData.length) {
        this.$message.warning('没有数据可导出')
        return
      }

      try {
        // 创建CSV内容
        const csvContent = [
          this.headers.join(','), // 表头
          ...this.tableData.map(row =>
            this.headers.map(header => {
              const value = row[header]
              // 处理包含逗号或引号的值
              if (value && (value.toString().includes(',') || value.toString().includes('"'))) {
                return `"${value.toString().replace(/"/g, '""')}"`
              }
              return value || ''
            }).join(',')
          )
        ].join('\n')

        // 创建下载链接
        const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' })
        const link = document.createElement('a')
        const url = URL.createObjectURL(blob)
        link.setAttribute('href', url)
        link.setAttribute('download', `${this.tableName || 'table_data'}_${new Date().toISOString().slice(0, 10)}.csv`)
        link.style.visibility = 'hidden'
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)

        this.$message.success('数据导出成功')
      } catch (error) {
        this.$message.error('导出失败')
      }
    },

    // 加载表注释
    async loadTableComment() {
      if (!this.dbName || !this.tableName) return

      try {
        const response = await getTableCommentApi({
          tableName: this.tableName
        })
        this.tableComment = response.comment || ''
      } catch (error) {
        console.error('获取表注释失败:', error)
        this.tableComment = ''
      }
    },

    // 保存表注释
    async saveTableComment() {
      if (!this.dbName || !this.tableName) {
        this.$message.error('缺少数据库名或表名')
        return
      }

      this.commentSaving = true
      try {
        await saveTableCommentApi({
          dbName: this.dbName,
          tableName: this.tableName,
          comment: this.commentForm.comment
        })

        this.tableComment = this.commentForm.comment
        this.showCommentDialog = false
        this.$message.success('表注释保存成功')
      } catch (error) {
        console.error('保存表注释失败:', error)
        this.$message.error('保存表注释失败')
      } finally {
        this.commentSaving = false
      }
    },

    // 处理关闭注释对话框
    handleCloseCommentDialog() {
      this.commentForm.comment = this.tableComment
      this.showCommentDialog = false
    }
  }
}
</script>

<style scoped>
.table-data-container {
  height: 100vh;
  width: 100%;
  display: flex;
  flex-direction: column;
  background: #fafafa;
  overflow: hidden; /* 防止整体滚动 */
}

/* 表格头部信息 */
.table-header {
  padding: 12px 16px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* 防止头部被压缩 */
}

.table-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.table-name-section {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-comment {
  font-size: 14px;
  color: #666;
  cursor: pointer;
  transition: color 0.2s ease;
}

.table-comment:hover {
  color: #3b82f6;
}

.add-comment-link {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #3b82f6;
  cursor: pointer;
  padding: 2px 6px;
  border-radius: 4px;
  transition: all 0.2s ease;
  border: 1px dashed #d1d5db;
  background: #f8fafc;
}

.add-comment-link:hover {
  color: #1d4ed8;
  background: #eff6ff;
  border-color: #3b82f6;
  transform: translateY(-1px);
}

.comment-icon {
  font-size: 12px;
}

.comment-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

.table-name {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

.record-count {
  font-size: 14px;
  color: #7f8c8d;
  background: #ecf0f1;
  padding: 4px 8px;
  border-radius: 4px;
}

/* 可滚动的表格容器 */
.table-scroll-container {
  flex: 1;
  overflow: auto;
  background: white;
  position: relative;
}

.table-wrapper {
  width: 100%;
  height: 100%;
  overflow: auto; /* 同时支持水平和垂直滚动 */
}

.data-table {
  width: 100%;
  min-width: 800px; /* 设置最小宽度确保有水平滚动 */
  border-collapse: collapse;
  font-size: 14px;
  table-layout: auto; /* 允许列宽自动调整 */
}

/* 表头样式 */
.table-header-cell {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 0;
  border: 1px solid #5a67d8;
  position: sticky;
  top: 0;
  z-index: 10;
  min-width: 120px; /* 最小列宽 */
}

.header-content {
  padding: 12px 16px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-text {
  font-weight: 600;
  font-size: 14px;
}

.header-type {
  font-size: 11px;
  opacity: 0.8;
  background: rgba(255, 255, 255, 0.2);
  padding: 2px 6px;
  border-radius: 3px;
  align-self: flex-start;
}

/* 表格行样式 */
.table-row {
  transition: background-color 0.2s ease;
}

.table-row:nth-child(even) {
  background-color: #f8f9fa;
}

.table-row:hover {
  background-color: #e3f2fd;
}

.table-cell {
  border: 1px solid #e0e0e0;
  padding: 0;
  min-width: 120px; /* 最小列宽 */
  max-width: 300px; /* 最大列宽 */
}

.cell-content {
  padding: 12px 16px;
  word-wrap: break-word;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: default;
}

.cell-content:hover {
  white-space: normal;
  overflow: visible;
  background: #fff3cd;
  position: relative;
  z-index: 5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  border-radius: 4px;
}

/* 无数据状态 */
.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
}

.no-data {
  text-align: center;
  color: #95a5a6;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.no-data-icon {
  font-size: 48px;
  color: #bdc3c7;
}

.no-data p {
  margin: 0;
  font-size: 16px;
}

/* 分页容器 */
.pagination-container {
  padding: 12px 16px;
  background: white;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0; /* 防止分页被压缩 */
}

/* 滚动条样式 */
.table-scroll-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-scroll-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-scroll-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.table-wrapper::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.table-wrapper::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.table-wrapper::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .table-data-container {
    margin: 8px;
  }

  .table-header {
    padding: 12px 16px;
  }

  .pagination-container {
    padding: 12px 16px;
  }

  .header-content,
  .cell-content {
    padding: 8px 12px;
  }
}
</style>
