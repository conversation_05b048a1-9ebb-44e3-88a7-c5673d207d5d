import request from '@/utils/request'
import { fetchEventSource } from '@microsoft/fetch-event-source'
import { getToken } from '@/utils/auth'
import { getDbToken } from '@/utils/request'

export function ChaWithAi(content) {
  return request({
    url: '/api/aichat/interflow',
    method: 'post',
    data: content
  })
}

export function ChaWithAiStream(aiRequest, callbacks) {
  // 创建 AbortController 用于中断请求
  const controller = new AbortController()

  // 构建请求头，包含认证信息
  const headers = {
    'Content-Type': 'application/json'
  }

  // 添加主要认证token
  if (getToken()) {
    headers['Authorization'] = getToken()
  }

  // 添加数据库专用token
  if (getDbToken()) {
    headers['X-DB-Token'] = getDbToken()
  }

  // 开始流式请求
  fetchEventSource('/api/aichat/stream', {
    method: 'POST',
    headers: headers,
    body: JSON.stringify(aiRequest),
    signal: controller.signal,
    openWhenHidden: true,

    async onopen(response) {
      if (!response.ok || !response.headers.get('content-type').includes('text/event-stream')) {
        const error = await response.text()
        if (callbacks.onError) {
          callbacks.onError(`SSE连接失败: ${response.status} - ${error}`)
        }
        controller.abort()
      }
    },

    onmessage(event) {
      console.log('收到事件:', event.event, '数据:', event.data) // 添加调试日志

      // 处理DONE标记
      if (event.data === '[DONE]') {
        console.log('收到结束标记')
        return
      }

      // 根据事件类型处理
      if (event.event === 'botcontent') {
        console.log('处理botcontent事件:', event.data)
        if (callbacks.onBotContent) {
          callbacks.onBotContent(event.data)
        }
      } else if (event.event === 'reportGenerating') {
        console.log('处理reportGenerating事件:', event.data)
        if (callbacks.onReportGenerating) {
          callbacks.onReportGenerating(event.data)
        }
      } else if (event.event === 'chart') {
        console.log('处理chart事件:', event.data)
        if (callbacks.onChart) {
          callbacks.onChart(event.data)
        }
      } else if (event.event === 'fullContent') {
        console.log('处理fullContent事件')
        if (callbacks.onComplete) {
          callbacks.onComplete(event.data)
        }
      } else if (event.event === 'content') {
        if (callbacks.onContent) {
          callbacks.onContent(event.data)
        }
      } else if (event.event === 'image') {
        console.log('处理image事件')
        if (callbacks.onImage) {
          callbacks.onImage(event.data)
        }
      } else if (event.event === 'error') {
        console.log('处理error事件')
        if (callbacks.onError) {
          callbacks.onError(event.data)
        }
      } else if (event.event === 'end') {
        console.log('处理end事件')
        // 处理结束事件
        if (callbacks.onComplete) {
          // 不需要传参数，因为fullContent事件会提供完整内容
        }
      } else {
        console.log('未知事件类型，作为content处理:', event.event)
        // 如果没有指定事件类型，默认作为content处理
        if (callbacks.onContent) {
          callbacks.onContent(event.data)
        }
      }
    },

    onclose() {
      // 确保触发完成回调
      if (callbacks.onComplete && !controller.signal.aborted) {
        // const fullContent = ""; // 如果前端没有收到完整内容
        // callbacks.onComplete(fullContent);
      }
    },

    onerror(error) {
      if (callbacks.onError) {
        callbacks.onError(`流式传输错误: ${error.message}`)
      }
      controller.abort()
    }
  })

  // 返回控制器方法
  return {
    abort: () => {
      controller.abort()
      console.log('AI流请求已取消')
    }
  }
}
