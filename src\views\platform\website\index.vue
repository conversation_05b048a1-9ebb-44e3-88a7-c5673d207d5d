<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="网站ID">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="网站名称">
            <el-input v-model="form.webName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="网站网址">
            <el-input v-model="form.webUrl" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="添加日期">
            <el-date-picker v-model="form.addDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="添加者">
            <el-input v-model="form.addBy" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="网站ID" />
        <el-table-column prop="webName" label="网站名称" />
        <el-table-column prop="addDate" label="添加日期" />
        <el-table-column prop="addBy" label="添加者" />
        <el-table-column v-if="checkPer(['admin','webBusinessWebsite:edit','webBusinessWebsite:del'])" label="操作" width="280px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
              :hide-edit="true"
            />
            <el-button
              size="mini"
              type="primary"
              class="toshop"
              @click="handleExport(scope.row)"
            >跳转至商城</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudWebBusinessWebsite from '@/api/website/webBusinessWebsite'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, webName: null, webUrl: null, addDate: null, addBy: null, delFlag: null }
export default {
  name: 'WebBusinessWebsite',
  components: { pagination, crudOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({
      title: '网站商城',
      url: 'api/webBusinessWebsite',
      idField: 'id',
      sort: 'id,desc',
      crudMethod: { ...crudWebBusinessWebsite },
      optShow: {
        add: true,
        edit: true,
        del: false,
        download: false // 👈 关键点：关闭导出按钮
      }
    })
  },
  data() {
    return {
      permission: {
        add: ['admin', 'webBusinessWebsite:add'],
        edit: ['admin', 'webBusinessWebsite:edit'],
        del: ['admin', 'webBusinessWebsite:del']
      },
      rules: {
      }
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    handleExport(row) {
      // 调用接口或者触发下载逻辑
      console.log('当前用户角色:', this.$store.getters.roles)
      if (typeof window !== 'undefined') {
        const url = 'https://pc.talmdcloud.com' + row.webUrl
        window.open(url, '_blank')
      }
    }
  }
}
</script>

<style scoped>
.toshop{
  position: relative;
  bottom: 28px;
  right: 75px;
}
</style>
