<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目类型" prop="projectTypeLx">
                  <el-select v-model="form.projectTypeLx" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_type_lx"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目状态" prop="projectstate">
                  <el-select v-model="form.projectstate" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.projectstate"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目面积">
                  <el-input v-model="form.projectArea">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目现状">
                  <el-input v-model="form.projectStatusQuo" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目交期" prop="projectDeliveryPeriod">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddress" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" />
            </el-form-item>
            <checkbox-field
              v-model="form.typeOfServiceDianmian"
              prop="typeOfServiceDianmian"
              label="需求内容"
              field-name="typeOfServiceDianmian"
            />
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-user" /> 客户基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="客户名称">
                  <el-input v-model="form.customerName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户编号">
                  <el-input v-model="form.customerId" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="客户性质">
                  <el-select v-model="form.customerNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.customer_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="form.gender" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.gender"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="客户职务">
                  <el-input v-model="form.customerPosition" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="form.contactNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.mailbox" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="备用邮箱">
                  <el-input v-model="form.email" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 店面详情</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="店面形式">
                  <el-select v-model="form.storeForm" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.Store_form"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑结构">
                  <el-select v-model="form.buildingStructure" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.building_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="合作性质">
                  <el-select v-model="form.cooperativeNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.Cooperative_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <checkbox-field
              v-model="form.storeModule"
              prop="storeModule"
              label="店面模块"
              field-name="storeModule"
            />

          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-date" /> 时间进度</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="负责人">
                  <el-input v-model="form.personInCharge" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="计划完成时间">
                  <el-date-picker v-model="form.plannedCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.dateOfTabulation" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请日期">
                  <el-date-picker v-model="form.applydt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="操作时间">
                  <el-date-picker v-model="form.optdt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作人">
                  <el-input v-model="form.optname" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              <el-input v-model="form.remarks" :rows="3" type="textarea" />
            </el-form-item>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-picture" /> 项目图纸文档</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="配置附件">
                  <general-file-upload
                    v-model="form.configureTheAttachment"
                    :field-name="'configureTheAttachment'"
                    v-bind="fileUploadConfig"
                    :use-minio-delete="true"
                    :hide-remove="isViewMode"
                    @change="handleFileChange('configureTheAttachment', $event)"
                    @file-change="handleFileListChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="预览图片">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectTypeLx" label="项目类型">
          <template slot-scope="scope">
            {{ dict.label.project_type_lx[scope.row.projectTypeLx] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiOaDianmian:edit','tumaiOaDianmian:del','tumaiOaDianmian:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaDianmian from '@/api/aios/shop/tumaiOaDianmian'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import CheckboxField from '@/components/CheckBox/CheckboxField'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, oddNumbers: null, comid: null, projectNumber: null, projectType: null, projectArea: null, projectStatusQuo: null, storeForm: null, buildingStructure: null, gender: null, customerPosition: null, contactNumber: null, mailbox: null, cooperativeNature: null, remarks: null, personInCharge: null, plannedCompletionTime: null, dateOfTabulation: null, storeModule: null, uid: null, typeOfServiceDianmian: null, email: null, projectName: null, customerNature: null, projectAddress: null, projectOverview: null, customerName: null, projectTypeLx: null, projectDeliveryPeriod: null, customerId: null, projectstate: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, createtime: null, isturn: null, configureTheAttachment: null }
export default {
  name: 'TumaiOaDianmian',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, CheckboxField, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaDianmian:add'],
        edit: ['admin', 'tumaiOaDianmian:edit'],
        del: ['admin', 'tumaiOaDianmian:del'],
        view: ['admin', 'tumaiOaDianmian:edit', 'tumaiOaDianmian:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectTypeLx: [
          { required: true, message: '项目类型不能为空', trigger: 'blur' }
        ],
        projectDeliveryPeriod: [
          { required: true, message: '项目交期不能为空', trigger: 'blur' }
        ],
        projectstate: [
          { required: true, message: '项目状态不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 文件字段列表，用于初始化和提交前处理
      fileFields: [
        'configureTheAttachment'
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 10,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      }
    }
  },
  dicts: ['Store_form', 'building_structure', 'gender', 'Cooperative_nature', 'customer_nature', 'project_type_lx', 'projectstate', 'status'],
  cruds() {
    return CRUD({ title: '项目立项（适用加盟店面）', url: 'api/tumaiOaDianmian', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaDianmian }})
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit]() {
      // 获取并初始化文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['configureTheAttachment'])
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd]() {
      // 初始化文件字段为空数组
      this.form.configureTheAttachment = '[]'
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      try {
        // 处理附件字段
        this.prepareGeneralFileFields(this.fileFields)
        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }

}
</script>

<style scoped>
.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01);
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
