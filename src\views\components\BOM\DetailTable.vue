<template>
  <div class="table-wrapper">
    <el-table
      :data="tableData"
      border
      style="width: 100%"
      size="medium"
      header-row-class-name="custom-header"
    >
      <el-table-column prop="category" label="类别" width="120" header-align="center" />
      <el-table-column prop="zone" label="区域/位置" width="120" header-align="center" />
      <el-table-column v-if="type !== 'solid'" prop="materialNumber" label="物品编号" width="120" header-align="center" />
      <el-table-column v-if="type !== 'solid'" prop="materialName" label="物料名称" width="100" header-align="center" />
      <el-table-column v-if="type === 'solid'" prop="productNumber" label="产品编号" width="120" header-align="center" />
      <el-table-column v-if="type === 'solid'" prop="productName" label="产品名称" width="100" header-align="center" />
      <el-table-column prop="picture" label="图片" width="180" header-align="center">
        <template #default="{row}">
          <el-image
            :src="row.picture ? `${VUE_APP_IMAGE_BASE_URL}BOM/${row.picture}` : require('@/assets/images/default.png')"
            :style="{
              width: '100%',
              height: calcHeight('100%') // 根据宽度计算高度
            }"
            fit="scale-down"
          />
        </template>
      </el-table-column>
      <el-table-column prop="brand" label="品牌" width="120" header-align="center" />
      <el-table-column prop="xinghao" label="型号" width="120" header-align="center" />
      <el-table-column prop="guige" label="规格" width="120" header-align="center" />
      <el-table-column v-if="type === 'hard'" prop="parameter" label="参数描述" width="200" header-align="center" />
      <el-table-column v-if="type !== 'hard'" prop="pigment" label="颜色" width="80" header-align="center" />
      <el-table-column v-if="type !== 'hard'" prop="materialQuality" label="材质" width="80" header-align="center" />
      <el-table-column prop="count" label="数量" width="80" header-align="center" />
      <el-table-column prop="unit" label="单位" width="80" header-align="center" />
      <el-table-column prop="b2BPrice" label="B2B未税单价(元)" width="120" header-align="center" />
      <el-table-column prop="b2BAmount" label="B2B金额(元)" width="80" header-align="center" />
      <el-table-column prop="supplierName" label="供应商名称" width="120" header-align="center" />
      <el-table-column label="操作" width="120" header-align="center">
        <template #default="{ row }">
          <el-button
            :loading="loadingBtn"
            type="primary"
            size="mini"
            @click.stop="handleEditImage(row)"
          >修改图片</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-backtop
      target=".table-wrapper"
      :right="40"
      :bottom="40"
      visibility-height="200"
    >
      <el-button
        type="primary"
        circle
        class="back-top-btn"
      >
        <i class="el-icon-arrow-up" />
      </el-button>
    </el-backtop>
  </div>
</template>

<script>

export default {
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      VUE_APP_IMAGE_BASE_URL: process.env.VUE_APP_IMAGE_BASE_URL
    }
  },
  created() {
    console.log(this.type)
  },
  methods: {
    calcHeight(width) {
      const aspectRatio = 3 / 2 // 自定义宽高比
      return `${parseInt(width) * aspectRatio}px`
    },
    handleEditImage(row) {
      console.log(row)
      // this.loadingBtn = true
      // if (file.file.size === 0) {
      //   this.$message.error('文件内容不能为空')
      //   return false // 阻止上传
      // }
      // const validTypes = ['png', 'jpg', 'jpeg']
      // if (!validTypes.includes(file.file.type)) {
      //   this.$message.error('仅支持png/jpg/jpeg格式文件')
      //   return false
      // }
      // this.uploadFile = file.file
      // changeImage(this.uploadFile, row.id, row.mid, this.type)
      //   .then(res => {
      //     this.$message.success(res)
      //     this.dialogVisible = false
      //     this.$emit('refresh')
      //   })
      //   .catch(err => {
      //     this.$message.error('导入失败：' + (err.message || '未知错误'))
      //   })
      //   .finally(() => {
      //     this.loadingBtn = false
      //     this.dataInit()
      //   })
    }
  }
}
</script>

<style lang="scss" scoped>
.back-top-btn {
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s;

  &:hover {
    transform: scale(1.1);
  }

  i {
    font-size: 18px;
    vertical-align: middle;
  }
}
</style>
