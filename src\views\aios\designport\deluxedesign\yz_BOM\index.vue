<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="900px" :before-close="crud.cancelCU" :visible="crud.status.cu > 0" @update:visible="val => crud.status.cu = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号" prop="projectNumber">
                  <el-input v-model="form.projectNumber" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.createdAt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">

              <el-col :span="12">
                <el-form-item label="未税B2B总金额（元）">
                  <el-input v-model="form.amountInTotal" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="未税零售总金额（元）">
                  <el-input v-model="form.grossRetailSales" style="width: 100%" />
                </el-form-item>
              </el-col>

              <el-col :span="24">
                <el-form-item label="备注">
                  <el-input v-model="form.remarks" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-goods" /> 物品列表</el-divider>
            <div class="table-container">
              <el-table :data="bomItemsList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="物品编号" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialNumber" placeholder="物品编号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="物品名称" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialName" placeholder="物品名称" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="品牌" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.brand" placeholder="品牌" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="型号" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.xinghao" placeholder="型号" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="规格" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.guige" placeholder="规格" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="参数描述" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.parameter" placeholder="参数描述" type="textarea" :rows="2" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="单位" min-width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.unit" placeholder="单位" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="数量" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.count" placeholder="数量" size="small" type="number" />
                  </template>
                </el-table-column>
                <el-table-column label="未税B2B单价(元)" min-width="140">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.price" placeholder="单价" size="small" type="number" />
                  </template>
                </el-table-column>
                <el-table-column label="B2B金额(元)" min-width="140">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.amount" placeholder="金额" size="small" type="number" />
                  </template>
                </el-table-column>
                <el-table-column label="供应商名称" min-width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.supplierName" placeholder="供应商" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="类别" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.materialCategory" placeholder="类别" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="区域/位置" min-width="160">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.zone" placeholder="区域/位置" size="small" />
                  </template>
                </el-table-column>
                <el-table-column label="零售单价(元)" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.retailUnitPrice" placeholder="零售单价" size="small" type="number" />
                  </template>
                </el-table-column>
                <el-table-column label="零售金额(元)" min-width="140">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.retailValue" placeholder="零售金额" size="small" type="number" />
                  </template>
                </el-table-column>
                <el-table-column label="图片" min-width="140">
                  <template slot-scope="scope">
                    <file-upload
                      size="small"
                      :field-value.sync="scope.row.picture"
                      :limit="5"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      @change="handleFileListChange($event, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveBomItem(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddBomItem">新增物品</el-button>
              </div>
            </div>

          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="amountInTotal" label="未税B2B总金额(元)" />
        <el-table-column prop="grossRetailSales" label="未税零售总金额(元)" />
        <el-table-column prop="createdAt" label="制表日期" />
        <el-table-column v-if="checkPer(['admin','tumaiSjYzbom:edit','tumaiSjYzbom:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>

import crudtumaiSjYzbom from '@/api/aios/designport/deluxedesign/tumaiSjYzbom'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'

const defaultForm = {
  id: null,
  mid: null,
  sort: null,
  comid: null,
  projectNumber: null,
  projectName: null,
  nickName: null,
  createdAt: null,
  projectAddress: null,
  projectOverview: null,
  finalLayoutPlan: null,
  amountInTotal: null,
  grossRetailSales: null,
  oddNumbers: null,
  remarks: null,
  // 子表表单字段
  bomItems: '[]'
}
export default {
  name: 'TumaiSjYzbom',
  components: { pagination, crudOperation, rrOperation, udOperation, FileUpload },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin],
  cruds() {
    return CRUD({ title: '硬装BOM', url: 'api/tumaiSjYzbom', idField: 'id', sort: 'id,desc', crudMethod: { ...crudtumaiSjYzbom }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjYzbom:add'],
        edit: ['admin', 'tumaiSjYzbom:edit'],
        del: ['admin', 'tumaiSjYzbom:del']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      pictureFileList: [],
      // BOM物品列表
      bomItemsList: [],
      rules: {
        materialName: [
          { required: true, message: '物品名称不能为空', trigger: 'blur' }
        ],
        unit: [
          { required: true, message: '单位不能为空', trigger: 'blur' }
        ],
        projectNumber: [
          { required: true, message: '单位不能为空', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi'])
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields(['finalLayoutPlan'])

      // 初始化BOM物品列表
      this.initBomItemsList()

      // 初始化子表单中的文件字段 (使用新工具)
      this.initSubformFileFields(this.bomItemsList, 'picture')
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化附件为空JSON数组
      this.form.finalLayoutPlan = '[]'
      this.bomItemsList = []

      // 添加一个空的BOM项
      this.handleAddBomItem()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始子表单数据:', JSON.stringify(this.bomItemsList))
        console.log('提交前原始表单数据:', JSON.stringify({
          finalLayoutPlan: this.form.finalLayoutPlan
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields(['finalLayoutPlan'])

        console.log('处理主表单附件后:', JSON.stringify({
          finalLayoutPlan: this.form.finalLayoutPlan
        }))

        // 处理子表单中的文件字段 (使用新工具)
        this.prepareSubformFileFields(this.bomItemsList, 'picture')

        // 更新表单中的BOM物品数据
        crud.form.bomItems = JSON.stringify(this.bomItemsList)

        // 计算总金额
        let totalAmount = 0
        let totalRetail = 0
        this.bomItemsList.forEach(item => {
          if (item.amount && !isNaN(parseFloat(item.amount))) {
            totalAmount += parseFloat(item.amount)
          }
          if (item.retailValue && !isNaN(parseFloat(item.retailValue))) {
            totalRetail += parseFloat(item.retailValue)
          }
        })
        crud.form.amountInTotal = totalAmount.toFixed(2)
        crud.form.grossRetailSales = totalRetail.toFixed(2)

        console.log('处理后的表单数据:', crud.form.bomItems)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化BOM物品列表
    initBomItemsList() {
      try {
        this.bomItemsList = this.form.bomItems ? JSON.parse(this.form.bomItems) : []
        if (!Array.isArray(this.bomItemsList)) {
          this.bomItemsList = []
        }

        console.log('原始BOM物品数据:', JSON.stringify(this.bomItemsList))

        // 确保每行都有picture附件字段
        this.bomItemsList.forEach(item => {
          if (!item.picture) {
            item.picture = '[]'
          } else if (typeof item.picture === 'string' && !item.picture.startsWith('[')) {
            // 将URL字符串转换为JSON格式
            try {
              const urls = item.picture.split(',').filter(url => url && url.trim())
              const fileList = urls.map((url, index) => {
                return {
                  name: `图片${index + 1}`,
                  url: url
                }
              })

              item.picture = JSON.stringify(fileList)
              console.log(`初始化子表单图片字段从URL为JSON:`, item.picture)
            } catch (e) {
              console.error(`初始化子表单图片字段失败:`, e)
              item.picture = '[]'
            }
          }
        })
      } catch (e) {
        console.error('解析BOM物品数据失败:', e)
        this.bomItemsList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.bomItemsList.length === 0) {
        this.handleAddBomItem()
      }
    },

    // 添加BOM物品
    handleAddBomItem() {
      this.bomItemsList.push({
        materialNumber: null,
        materialName: null,
        brand: null,
        xinghao: null,
        guige: null,
        parameter: null,
        unit: null,
        count: null,
        price: null,
        amount: null,
        supplierName: null,
        materialCategory: null,
        zone: null,
        retailUnitPrice: null,
        retailValue: null,
        picture: '[]'
      })
    },

    // 移除BOM物品
    handleRemoveBomItem(index) {
      this.bomItemsList.splice(index, 1)
      if (this.bomItemsList.length === 0) {
        this.handleAddBomItem()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      // 确保事件对象格式正确
      if (event && event.action === 'success' && event.file && event.file.response) {
        // 手动补充事件信息
        if (!event.fieldName && row.picture !== undefined) {
          // 针对子表单的硬装BOM清单，指定正确的字段名
          event.fieldName = 'picture'
        }
      }

      let result = false

      try {
        if (row === this.form) {
          // 主表单文件处理
          result = this.handleAttachmentChange(event, row)
          console.log('主表单处理后数据:', JSON.stringify({
            finalLayoutPlan: this.form.finalLayoutPlan
          }))
        } else {
          // 子表单文件处理 (使用新工具)
          result = this.handleSubformFileChange(event, row)
          console.log('子表单处理后行数据:', JSON.stringify(row))
        }

        if (!result) {
          console.warn('文件处理返回失败结果')
          this.$message.warning('文件处理失败，请重试')
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
