import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/tumaiOa<PERSON>ji<PERSON>',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/tumaiOa<PERSON>an/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/tumaiOaKongjian',
    method: 'put',
    data
  })
}

export default { add, edit, del }
