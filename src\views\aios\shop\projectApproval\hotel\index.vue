<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="20">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="8">
                <el-form-item label="项目类型" prop="projectTypeLx">
                  <el-select v-model="form.projectTypeLx" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_type_lx"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目状态" prop="projectstate">
                  <el-select v-model="form.projectstate" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.projectstate"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="项目交期" prop="projectDeliveryPeriod">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider content-position="left"><i class="el-icon-info" /> 项目信息</el-divider>
            <checkbox-field
              v-model="form.projectNature"
              label="项目性质"
              prop="projectNature"
              field-name="projectNature"
            />
            <checkbox-field
              v-model="form.projectTypeMinsu"
              label="项目类型"
              prop="projectTypeMinsu"
              field-name="projectTypeMinsu"
            />
            <checkbox-field
              v-model="form.typeOfServiceMinsu"
              label="需求内容"
              prop="typeOfServiceMinsu"
              field-name="typeOfServiceMinsu"
            />
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 项目信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddress" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="总投资方">
                  <el-input v-model="form.investor" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="施工总包单位">
                  <el-input v-model="form.construction" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目监理单位">
                  <el-input v-model="form.projectSupervision" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目总管公司">
                  <el-input v-model="form.managementCompany" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目景观单位">
                  <el-input v-model="form.landscapeUnit" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目精装单位">
                  <el-input v-model="form.hardcoverUnit" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目总面积">
                  <el-input v-model="form.totalArea">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目建筑层数">
                  <el-input v-model="form.buildingLayers" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="建筑风格">
                  <el-input v-model="form.architecturalStyle" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客房间数">
                  <el-input v-model="form.guestRooms" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="24">
                <el-form-item label="项目目前实施阶段">
                  <el-input v-model="form.projectPlan" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="24">
                <checkbox-field
                  v-model="form.roomClassification"
                  label="客房分类"
                  prop="roomClassification"
                  field-name="roomClassification"
                />
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="24">
                <checkbox-field
                  v-model="form.levelpricePositioning"
                  label="项目等级/价格定位"
                  prop="levelpricePositioning"
                  field-name="levelpricePositioning"
                />
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="24">
                <checkbox-field
                  v-model="form.fenggeMinsu"
                  label="室内风格"
                  prop="fenggeMinsu"
                  field-name="fenggeMinsu"
                />
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="24">
                <checkbox-field
                  v-model="form.winningCategory"
                  label="中标类别"
                  prop="winningCategory"
                  field-name="winningCategory"
                />
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-user" /> 客户基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="客户编号">
                  <el-input v-model="form.customerId" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司名称">
                  <el-input v-model="form.corporateName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="姓名">
                  <el-input v-model="form.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="form.gender" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.gender"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="职务">
                  <el-input v-model="form.post" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="form.contactNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.email" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="微信">
                  <el-input v-model="form.wechat" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司性质">
                  <el-input v-model="form.companyNature" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-money" /> 预算信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="设计费预算(万)">
                  <el-input v-model="form.designFeeBudget">
                    <template slot="append">万元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="硬装预算(元)">
                  <el-input v-model="form.hardPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="软装预算(元)">
                  <el-input v-model="form.softOutfitBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目总预算(元)">
                  <el-input v-model="form.generalBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-date" /> 时间信息</el-divider>
            <el-row :gutter="30">

              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="硬装完成时间">
                  <el-date-picker v-model="form.completionTimeYz" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="软装进场时间">
                  <el-date-picker v-model="form.outfitTimeRz" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="预计开放时间">
                  <el-date-picker v-model="form.openingTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="申请日期">
                  <el-date-picker v-model="form.applydt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="操作时间">
                  <el-date-picker v-model="form.optdt" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="操作人">
                  <el-input v-model="form.optname" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-document" /> 其他信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-picture" /> 图纸文档</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="施工图纸">
                  <file-upload
                    :field-value.sync="form.constructionDrawings"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑图纸">
                  <file-upload
                    :field-value.sync="form.buildingDrawings"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="消防/机电图纸">
                  <file-upload
                    :field-value.sync="form.drawingsXf"
                    :limit="5"
                    :upload-to-server="true"
                    :api-url="minioUploadApi"
                    :hide-remove="isViewMode"
                    @change="handleFileListChange($event, form)"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-dialog :visible.sync="dialogVisible" append-to-body>
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质" />
        <el-table-column prop="tabDate" label="制表日期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiOaMinsu:edit','tumaiOaMinsu:del','tumaiOaMinsu:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaMinsu from '@/api/aios/shop/tumaiOaMinsu'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import CheckboxField from '@/components/CheckBox/CheckboxField'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, typeOfServiceMinsu: null, projectName: null, architecturalStyle: null, constructionDrawings: null, corporateName: null, name: null, contactNumber: null, post: null, postbox: null, sexuality: null, projectNumber: null, uid: null, email: null, gender: null, projectNature: null, projectTypeLx: null, companyNature: null, wechat: null, projectType: null, projectAddress: null, projectDeliveryPeriod: null, customerId: null, projectstate: null, tabDate: null, buildingDrawings: null, drawingsXf: null, projectPlan: null, winningCategory: null, completionTimeYz: null, outfitTimeRz: null, openingTime: null, investor: null, construction: null, projectSupervision: null, managementCompany: null, landscapeUnit: null, hardcoverUnit: null, totalArea: null, buildingLayers: null, roomClassification: null, guestRooms: null, levelpricePositioning: null, designFeeBudget: null, hardPackBudget: null, softOutfitBudget: null, generalBudget: null, fenggeMinsu: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, createtime: null, isturn: null }
export default {
  name: 'TumaiOaMinsu',
  components: { pagination, crudOperation, rrOperation, FileUpload, CheckboxField, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['gender', 'project_type_lx', 'projectstate', 'status'],
  cruds() {
    return CRUD({ title: '项目立项（适用酒店、民宿）', url: 'api/shop/tumaiOaMinsu', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaMinsu }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaMinsu:add'],
        edit: ['admin', 'tumaiOaMinsu:edit'],
        del: ['admin', 'tumaiOaMinsu:del'],
        view: ['admin', 'tumaiOaMinsu:edit', 'tumaiOaMinsu:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      constructionDrawingsList: [],
      buildingDrawingsList: [],
      drawingsXfList: [],
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        projectTypeLx: [
          { required: true, message: '项目类型（酒店名宿类型）不能为空', trigger: 'blur' }
        ],
        projectDeliveryPeriod: [
          { required: true, message: '项目交期不能为空', trigger: 'blur' }
        ],
        projectstate: [
          { required: true, message: '项目状态不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'projectNumber', display_name: '项目编号' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'constructionDrawings',
        'buildingDrawings',
        'drawingsXf'
      ])
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, [
        'constructionDrawings',
        'buildingDrawings',
        'drawingsXf'
      ])
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.constructionDrawings = '[]'
      this.form.buildingDrawings = '[]'
      this.form.drawingsXf = '[]'
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始表单数据:', JSON.stringify({
          constructionDrawings: this.form.constructionDrawings,
          buildingDrawings: this.form.buildingDrawings,
          drawingsXf: this.form.drawingsXf
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'constructionDrawings',
          'buildingDrawings',
          'drawingsXf'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          constructionDrawings: this.form.constructionDrawings,
          buildingDrawings: this.form.buildingDrawings,
          drawingsXf: this.form.drawingsXf
        }))

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        // 主表单文件处理
        const result = this.handleAttachmentChange(event, row)
        console.log('主表单处理后数据:', JSON.stringify({
          constructionDrawings: this.form.constructionDrawings,
          buildingDrawings: this.form.buildingDrawings,
          drawingsXf: this.form.drawingsXf
        }))

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01)
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
