<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddr" style="width: 100%" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="计划完成时间">
                  <el-date-picker v-model="form.planCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户确认时间">
                  <el-date-picker v-model="form.customerConfirmTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" style="width: 100%" />
            </el-form-item>

            <!-- 设计版本子表单 -->
            <el-divider content-position="left"><i class="el-icon-document" /> 设计版本信息</el-divider>
            <div class="table-container">
              <el-table :data="designVersionsList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="开始制作日期" width="160">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.startDate" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="实际完成日期" width="160">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.actualFinishDate" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="设计师" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.designer" placeholder="请输入设计师" />
                  </template>
                </el-table-column>
                <el-table-column label="版本" width="120" prop="drawingVersion">
                  <template slot="header">
                    <span class="required-star">版本</span>
                  </template>
                  <template slot-scope="scope">
                    <el-select
                      v-model="scope.row.drawingVersion"
                      filterable
                      placeholder="请选择"
                      style="width: 100%"
                      :class="{ 'is-invalid': !scope.row.drawingVersion && scope.$index === designVersionsList.length - 1 }"
                    >
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.commitStatus" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.commit_status"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription" placeholder="请输入异常说明" type="textarea" :rows="2" />
                  </template>
                </el-table-column>
                <el-table-column label="PDF格式附件" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.pdfFormaFile"
                      :field-name="'pdfFormaFile'"
                      v-bind="fileUploadConfig"
                      :use-minio-delete="true"
                      list-type="text"
                      show-file-list
                      show-file-name
                      :hide-remove="isViewMode"
                      @change="handleSubformFileChange($event, scope.row)"
                      @file-change="handleFileListChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveDesignVersion(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDesignVersion">新增</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>

            <el-form-item label="最终版PDF格式附件">
              <general-file-upload
                v-model="form.finalEdition"
                :field-name="'finalEdition'"
                v-bind="fileUploadConfig"
                :use-minio-delete="true"
                :accept="'.pdf'"
                :hide-remove="isViewMode"
                @change="handleFileChange('finalEdition', $event)"
                @file-change="handleFileListChange"
              />
            </el-form-item>
            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column v-if="checkPer(['admin','tumaiSjRzgn:edit','tumaiSjRzgn:del','tumaiSjRzgn:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiSjRzgn from '@/api/aios/designport/softdesign/tumaiSjRzgn'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null,
  projectDesignNumber: null, projectNumber: null, projectName: null,
  projectAddr: null, projectNature: null, projectOverview: null,
  planCompletionTime: null, uid: null, customerConfirmTime: null,
  customerSignFile: null, optdt: null, optid: null, optname: null,
  applydt: null, explain: null, status: null, isturn: null,
  projectAddress: null, typeOfService: null, projectid: null,
  projectDeliveryPeriod: null, tabDate: null, createtime: null,
  finalEdition: null,
  // 子表单字段
  designVersions: '[]'
}
export default {
  name: 'TumaiSjRzgn',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'drawing_version', 'commit_status'],
  cruds() {
    return CRUD({ title: '软装概念设计', url: 'api/tumaiSjRzgn', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjRzgn }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjRzgn:add'],
        edit: ['admin', 'tumaiSjRzgn:edit'],
        del: ['admin', 'tumaiSjRzgn:del'],
        view: ['admin', 'tumaiSjRzgn:edit', 'tumaiSjRzgn:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 设计版本列表
      designVersionsList: [],
      // 文件字段列表
      fileFields: ['finalEdition'],
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        drawingVersion: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      fileUploadConfig: {
        accept: '.pdf',
        maxFiles: 5,
        tipText: '只支持上传PDF文件',
        buttonText: '上传PDF文件',
        listType: 'text',
        useMinioDelete: true,
        showFileList: true,
        showFileName: true
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'customerSignFile',
        'finalEdition'
      ])
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
      // 初始化设计版本列表
      this.initDesignVersionsList()
      // 初始化子表单中的文件字段 (使用新工具)
      this.initSubformFileFields(this.designVersionsList, 'pdfFormaFile')
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      // 删除主表单的文件
      deleteRecordFiles(data, ['finalEdition'])
      // 删除子表单中的文件
      deleteSubformFiles(data, 'designVersions', 'pdfFormaFile')

      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'customerSignFile',
        'finalEdition'
      ])
      this.initGeneralFileFields(this.fileFields)
      this.initDesignVersionsList()
      this.initSubformFileFields(this.designVersionsList, 'pdfFormaFile')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.customerSignFile = '[]'
      this.form.finalEdition = '[]'

      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)

      this.designVersionsList = []

      // 添加一个空的版本记录
      this.handleAddDesignVersion()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')
      // 新增版本字段校验
      for (let i = 0; i < this.designVersionsList.length; i++) {
        const version = this.designVersionsList[i]
        if (!version.drawingVersion || version.drawingVersion.trim() === '') {
          this.$message.error(`第 ${i + 1} 行的版本不能为空`)
          return false
        }
      }

      try {
        console.log('提交前原始子表单数据:', JSON.stringify(this.designVersionsList))
        console.log('提交前原始表单数据:', JSON.stringify({
          customerSignFile: this.form.customerSignFile,
          finalEdition: this.form.finalEdition
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'customerSignFile',
          'finalEdition'
        ])

        // 处理通用文件字段
        this.prepareGeneralFileFields(this.fileFields)

        console.log('处理主表单附件后:', JSON.stringify({
          customerSignFile: this.form.customerSignFile,
          finalEdition: this.form.finalEdition
        }))

        // 处理子表单中的文件字段 (使用新工具)
        this.prepareSubformFileFields(this.designVersionsList, 'pdfFormaFile')

        // 将处理后的子表单数据同步回表单
        crud.form.designVersions = JSON.stringify(this.designVersionsList)

        console.log('处理后的表单数据:', crud.form.designVersions)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化设计版本列表
    initDesignVersionsList() {
      try {
        this.designVersionsList = this.form.designVersions ? JSON.parse(this.form.designVersions) : []
        if (!Array.isArray(this.designVersionsList)) {
          this.designVersionsList = []
        }

        // 确保每行都有PDF附件字段
        this.designVersionsList.forEach(item => {
          if (!item.pdfFormaFile) {
            item.pdfFormaFile = '[]'
          }

          // 转换URL字符串为文件数组格式
          try {
            const fileValue = item.pdfFormaFile
            if (typeof fileValue === 'string' && fileValue) {
              const parsed = JSON.parse(fileValue)
              if (!Array.isArray(parsed)) {
                // 如果不是数组格式，转换URL为文件数组
                item.pdfFormaFile = this.convertUrlsToFileArray(fileValue)
              }
            }
          } catch (e) {
            console.error('处理文件格式失败:', e)
            // 转换URL为文件数组格式
            item.pdfFormaFile = this.convertUrlsToFileArray(item.pdfFormaFile || '')
          }
        })
      } catch (e) {
        console.error('解析设计版本数据失败:', e)
        this.designVersionsList = []
      }
    },

    // 将URL字符串转换为文件数组格式
    convertUrlsToFileArray(urlStr) {
      if (!urlStr || urlStr === '[]') return '[]'

      try {
        // 尝试解析，如果已经是JSON格式则不处理
        const parsed = JSON.parse(urlStr)
        if (Array.isArray(parsed)) {
          return urlStr
        }
      } catch (e) {
        // 不是JSON格式，继续处理
      }

      // 处理逗号分隔的URL字符串
      const urls = urlStr.split(',').filter(url => url && url.trim())
      if (urls.length === 0) return '[]'

      const fileArray = urls.map(url => {
        // 从URL中提取文件名
        const fileName = url.split('/').pop() || '未命名文件'
        return {
          name: fileName,
          url: url
        }
      })

      return JSON.stringify(fileArray)
    },

    // 添加设计版本
    handleAddDesignVersion() {
      this.designVersionsList.push({
        startDate: null,
        actualFinishDate: null,
        designer: null,
        drawingVersion: null,
        commitStatus: null,
        exceptionDescription: null,
        pdfFormaFile: '[]'
      })
    },

    // 移除设计版本
    handleRemoveDesignVersion(index) {
      // 获取要删除的行数据
      const rowData = this.designVersionsList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        deleteSubformRowFiles(rowData, 'pdfFormaFile')
      }
      this.designVersionsList.splice(index, 1)
      if (this.designVersionsList.length === 0) {
        this.handleAddDesignVersion()
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, row || this.form)

        return result
      } catch (error) {
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files, row) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        if (row) {
          // 子表单的处理
          row[fieldName] = jsonStr
        } else {
          // 主表单的处理
          this.form[fieldName] = jsonStr
        }
      }
    },

    // 处理子表单文件变更
    handleSubformFileChange(event, row) {
      console.log('子表单文件变更事件:', JSON.stringify(event))

      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, row)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`子表单文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('子表单文件处理过程中发生错误:', error)
        this.$message.error('子表单文件处理出错，请重试')
        return false
      }
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.filter-item {
  margin: 0 5px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 必填项红星样式 */
.required-star::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

</style>
