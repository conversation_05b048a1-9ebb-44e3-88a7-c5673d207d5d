<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="深化图纸">
                  <el-select v-model="form.structureDraw" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.structure_draw"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-row :gutter="30">
              <!-- <el-col :span="12">
                <el-form-item label="图纸确认计划完成时间">
                  <el-date-picker v-model="form.plannedTime" type="datetime" style="width: 370px;" />
                </el-form-item>
              </el-col> -->
              <el-col :span="12">
                <el-form-item label="图纸确认计划完成时间">
                  <el-date-picker v-model="form.drawingsconfirmCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="图纸深化开始日期" prop="deepenTheStartdate">
                  <el-date-picker v-model="form.deepenTheStartDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="备注">
              <el-input v-model="form.notes" :rows="2" type="textarea" />
            </el-form-item>

            <el-divider content-position="left"><i class="el-icon-info" /> 图纸确认</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="客户最终确认时间">
                  <el-date-picker v-model="form.customersFinalConfirmationTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate3" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="确认尾数（款）">
                  <el-input v-model="form.mantissa" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="尾数产品名称">
                  <el-input v-model="form.entailProductName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="延期天数">
                  <el-input v-model="form.deferredDays" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="延期原因">
                  <el-input v-model="form.delayReason" :rows="3" type="textarea" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="延期后计划完成日期">
                  <el-date-picker v-model="form.scheduledCompletionDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="延期后实际完成日期">
                  <el-date-picker v-model="form.actualCompletionDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="最终DWG格式（附件）">
                  <general-file-upload
                    v-model="form.dwgFormatFile2"
                    :field-name="'dwgFormatFile2'"
                    v-bind="fileUploadConfig"
                    :use-minio-delete="true"
                    :hide-remove="isViewMode"
                    @change="handleFileChange('dwgFormatFile2', $event)"
                    @file-change="handleFileListChange"
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 深化图纸记录子表单 -->
            <el-divider content-position="left"><i class="el-icon-document" /> 深化图纸记录信息</el-divider>
            <div class="table-container">
              <el-table :data="drawingRecordList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="图纸版次" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.drawingsEdition" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="绘制款数" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.theNumberOf" size="mini" placeholder="请输入款数" />
                  </template>
                </el-table-column>
                <el-table-column label="换款次数" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.numberOfChanges" size="mini" placeholder="请输入次数" />
                  </template>
                </el-table-column>
                <el-table-column label="换款款数" width="100">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.changeTheMoney" size="mini" placeholder="请输入款数" />
                  </template>
                </el-table-column>
                <el-table-column label="绘图员" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.draftsman" size="mini" placeholder="请输入绘图员" />
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription" size="mini" type="textarea" :rows="2" placeholder="请输入异常说明" />
                  </template>
                </el-table-column>
                <el-table-column label="图纸深化开始日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.startDate"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="实际完成日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.actualFinishDate"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="深化图纸" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.deepenDrawing" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.structure_draw"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="图纸确认计划完成时间" width="180">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.drawingFinish"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="PDF格式（附件）" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.pdfFormatFile"
                      :field-name="'pdfFormatFile'"
                      v-bind="pdfUploadConfig"
                      :use-minio-delete="true"
                      :hide-remove="isViewMode"
                      @change="handleFileChange('pdfFormatFile', $event, scope.row)"
                      @file-change="handleFileListChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveDrawingRecord(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDrawingRecord">新增</el-button>
              </div>
            </div>
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectOverview" label="项目概况" />
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjWjshenhua:edit','tumaiSjWjshenhua:del','tumaiSjWjshenhua:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiSjWjshenhua from '@/api/aios/designport/wholeHousedesign/tumaiSjWjshenhua'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, projectNumber: null, projectName: null, projectAddr: null, projectNature: null, projectOverview: null, structuralDrawing: null, pdfFormatFileAttachment: null, dwgFormatFileAttachment: null, notes: null, submissionTime: null, draftsman: null, customerFeedbackTime: null, modifyTheCompletionTime: null, exceptionDescription: null, serialNumber: null, position: null, commentsOnModification: null, referencePicture: null, customersFinalConfirmationTime: null, customerSignatureAttachment: null, pdfFormatFileAttachment2: null, dwgFormatFileAttachment2: null, uid: null, projectAddress: null, typeOfService: null, customerfinalConfirmationTime: null, drawingsconfirmCompletionTime: null, projectid: null, projectDeliveryPeriod: null, entailProductName: null, deferredDays: null, delayReason: null, scheduledCompletionDate: null, actualCompletionDate: null, deepenTheStartDate: null, tabDate3: null, dwgFormatFile2: null, mantissa: null, structureDraw: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null, createtime: null, plannedTime: null, drawingRecordData: '[]' }
export default {
  name: 'TumaiSjWjshenhua',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'structure_draw', 'status', 'drawing_version', 'deepen_drawing'],
  cruds() {
    return CRUD({ title: '五金图深化', url: 'api/tumaiSjWjshenhua', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjWjshenhua }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjWjshenhua:add'],
        edit: ['admin', 'tumaiSjWjshenhua:edit'],
        del: ['admin', 'tumaiSjWjshenhua:del'],
        view: ['admin', 'tumaiSjWjshenhua:edit', 'tumaiSjWjshenhua:view']
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 深化图纸记录子表单数据
      drawingRecordList: [],
      // 图片预览
      dialogImageUrl: '',
      dialogVisible: false,
      // 通用文件字段
      fileFields: ['pdfFormatFile', 'dwgFormatFile2'],
      // PDF文件上传配置
      pdfUploadConfig: {
        accept: '.pdf',
        maxFiles: 2,
        tipText: '只支持上传PDF文件',
        buttonText: '上传PDF',
        listType: 'text',
        useMinioDelete: true
      },
      // DWG文件上传配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.dwg',
        maxFiles: 5,
        tipText: '支持上传各种格式文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true
      }
    }
  },
  computed: {
    ...mapGetters([
      'baseApi',
      'minioUploadApi',
      'minioDeleteApi'
    ]),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 不需要这段代码，使用混入的方法来处理文件
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'customerSignatureAttachment',
        'dwgFormatFile2'
      ])
      // 初始化深化图纸记录列表
      this.initDrawingRecordList(form)
      // 初始化子表单中的文件字段
      this.initSubformFileFields(this.drawingRecordList, 'pdfFormatFile')
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'customerSignatureAttachment',
        'dwgFormatFile2'
      ])
      this.initDrawingRecordList(form)
      this.initSubformFileFields(this.drawingRecordList, 'pdfFormatFile')
      this.initGeneralFileFields(this.fileFields)
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['dwgFormatFile2'])
      deleteSubformFiles(data, 'drawingRecordData', 'pdfFormatFile')
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.customerSignatureAttachment = '[]'
      this.form.dwgFormatFile2 = '[]'

      this.drawingRecordList = []

      // 添加一个空的记录
      this.handleAddDrawingRecord()

      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        const errors = this.validateMeasurementList()
        if (errors.length > 0) {
          errors.forEach(msg => this.$message.error(msg))
          return false
        }
        console.log('提交前原始子表单数据:', JSON.stringify(this.drawingRecordList))
        console.log('提交前原始表单数据:', JSON.stringify({
          customerSignatureAttachment: this.form.customerSignatureAttachment,
          dwgFormatFile2: this.form.dwgFormatFile2
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'customerSignatureAttachment',
          'dwgFormatFile2'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          customerSignatureAttachment: this.form.customerSignatureAttachment,
          dwgFormatFile2: this.form.dwgFormatFile2
        }))

        // 处理子表单中的文件字段
        this.prepareSubformFileFields(this.drawingRecordList, 'pdfFormatFile')

        // 处理通用文件字段
        this.prepareGeneralFileFields(this.fileFields)

        // 将处理后的子表单数据同步回表单
        crud.form.drawingRecordData = JSON.stringify(this.drawingRecordList)

        console.log('处理后的表单数据:', crud.form.drawingRecordData)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化深化图纸记录列表
    initDrawingRecordList(form) {
      try {
        this.drawingRecordList = form.drawingRecordData ? JSON.parse(form.drawingRecordData) : []
        if (!Array.isArray(this.drawingRecordList)) {
          this.drawingRecordList = []
        }

        // 确保每行都有文件字段
        this.drawingRecordList.forEach(item => {
          if (!item.pdfFormatFile) {
            item.pdfFormatFile = '[]'
          }
        })
      } catch (e) {
        console.error('解析深化图纸记录数据失败:', e)
        this.drawingRecordList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.drawingRecordList.length === 0) {
        this.handleAddDrawingRecord()
      }
    },

    // 添加深化图纸记录
    handleAddDrawingRecord() {
      this.drawingRecordList.push({
        drawingsEdition: null,
        theNumberOf: null,
        numberOfChanges: null,
        changeTheMoney: null,
        draftsman: null,
        exceptionDescription: null,
        startDate: null,
        actualFinishDate: null,
        deepenDrawing: null,
        drawingFinish: null,
        pdfFormatFile: '[]'
      })
    },

    // 移除深化图纸记录
    handleRemoveDrawingRecord(index) {
      // 获取要删除的行数据
      const rowData = this.drawingRecordList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        deleteSubformRowFiles(rowData, 'pdfFormatFile')
      }
      this.drawingRecordList.splice(index, 1)
      if (this.drawingRecordList.length === 0) {
        this.handleAddDrawingRecord()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件变更处理
    handleFileChange(fieldName, files, row) {
      if (Array.isArray(files)) {
        const jsonStr = JSON.stringify(files)
        if (row) {
          row[fieldName] = jsonStr
          console.log(`子表单字段${fieldName}更新为:`, row[fieldName])
        } else {
          this.form[fieldName] = jsonStr
          console.log(`主表单字段${fieldName}更新为:`, this.form[fieldName])
        }
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))
      try {
        const result = this.handleGeneralFileChange(event, row || this.form)
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }
        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 解析文件列表
    parseFileList(filesStr) {
      try {
        if (!filesStr || filesStr === '[]') {
          return []
        }

        const files = JSON.parse(filesStr)
        return Array.isArray(files) ? files : []
      } catch (error) {
        console.error('解析文件列表失败:', error)
        return []
      }
    },
    validateMeasurementList() {
      const errors = []

      this.drawingRecordList.forEach((item, index) => {
        if (!item.actualFinishDate) {
          errors.push(`第 ${index + 1} 行缺少实际完成日期`)
        }
      })
      return errors
    }

  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.small-upload-container {
  width: 100%;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
