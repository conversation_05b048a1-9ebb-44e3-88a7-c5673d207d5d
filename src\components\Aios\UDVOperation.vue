<template>
  <div class="operation-buttons">
    <!-- 查看按钮 -->
    <el-tooltip class="item" effect="dark" content="查看" placement="top">
      <el-button
        v-if="!hideView"
        v-permission="permission.view || permission.edit"
        :disabled="disabledView"
        size="mini"
        type="info"
        icon="el-icon-view"
        @click.stop="toView"
      />
    </el-tooltip>

    <!-- 编辑按钮 -->
    <el-tooltip class="item" effect="dark" content="编辑" placement="top">
      <el-button
        v-if="!hideEdit"
        v-permission="permission.edit"
        :loading="crud.status.cu === 2"
        :disabled="disabledEdit"
        size="mini"
        type="primary"
        icon="el-icon-edit"
        @click.stop="crud.toEdit(data)"
      />
    </el-tooltip>

    <!-- 删除按钮 -->
    <el-tooltip class="item" effect="dark" content="删除" placement="top">
      <el-popover
        v-model="pop"
        v-permission="permission.del"
        placement="top"
        width="180"
        trigger="manual"
        @show="onPopoverShow"
        @hide="onPopoverHide"
      >
        <p>{{ msg }}</p>
        <div style="text-align: right; margin: 0">
          <el-button size="mini" type="text" @click="doCancel">取消</el-button>
          <el-button
            :loading="crud.dataStatus[crud.getDataId(data)].delete === 2"
            type="primary"
            size="mini"
            @click="crud.doDelete(data)"
          >
            确定
          </el-button>
        </div>
        <el-button
          v-if="!hideDelete"
          slot="reference"
          :disabled="disabledDle"
          type="danger"
          icon="el-icon-delete"
          size="mini"
          @click.stop="toDelete"
        />
      </el-popover>
    </el-tooltip>
  </div>
</template>

<script>
import CRUD, { crud } from '@crud/crud'

export default {
  name: 'AiosUDVOperation',
  mixins: [crud()],
  props: {
    data: {
      type: Object,
      required: true
    },
    permission: {
      type: Object,
      required: true
    },
    disabledEdit: {
      type: Boolean,
      default: false
    },
    disabledDle: {
      type: Boolean,
      default: false
    },
    disabledView: {
      type: Boolean,
      default: false
    },
    msg: {
      type: String,
      default: '确定删除本条数据吗？'
    },
    hideEdit: {
      type: Boolean,
      default: false
    },
    hideDelete: {
      type: Boolean,
      default: false
    },
    hideView: {
      type: Boolean,
      default: false
    },
    // 自定义查看方法，如果不提供则使用默认的查看逻辑
    customViewMethod: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      pop: false
    }
  },
  methods: {
    toView() {
      if (this.customViewMethod) {
        // 如果提供了自定义查看方法，则调用它
        this.customViewMethod(this.data)
      } else {
        // 使用CRUD的toView方法
        this.crud.toView(this.data)
      }
    },

    doCancel() {
      this.pop = false
      this.crud.cancelDelete(this.data)
    },

    toDelete() {
      this.pop = true
    },

    [CRUD.HOOK.afterDelete](crud, data) {
      if (data === this.data) {
        this.pop = false
      }
    },

    onPopoverShow() {
      setTimeout(() => {
        document.addEventListener('click', this.handleDocumentClick)
      }, 0)
    },

    onPopoverHide() {
      document.removeEventListener('click', this.handleDocumentClick)
    },

    handleDocumentClick(event) {
      this.pop = false
    }
  }
}
</script>

<style scoped>
.operation-buttons {
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.item {
  display: inline-block;
}

.item .el-button {
  margin: 0 2px;
}
</style>
