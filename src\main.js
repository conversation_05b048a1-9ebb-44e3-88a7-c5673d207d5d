import Vue from 'vue'

import Cookies from 'js-cookie'

import 'normalize.css/normalize.css'

import Element from 'element-ui'
import Echarts from 'echarts'

// 数据字典
import dict from './components/Dict'

// 权限指令
import checkPer from '@/utils/permission'
import permission from './components/Permission'
import './assets/styles/element-variables.scss'

// global css
import './assets/styles/index.scss'

// 表单验证工具
import formValidator from '@/utils/formValidator'

import App from './App'
import store from './store'
import router from './router/routers'

import './assets/icons' // icon
import './router/index' // permission control

import drawerDragWidth from './directive/drawer-drag-width'

// 全局挂载ECharts
Vue.prototype.$echarts = Echarts // 所有组件可通过 this.$echarts 调用

Vue.use(checkPer)
Vue.use(permission)
Vue.use(dict)
Vue.use(Element, {
  size: Cookies.get('size') || 'small' // set element-ui default size
})
Vue.use(formValidator) // 注册表单验证工具

// 注册全局指令
Vue.directive('drawer-drag-width', drawerDragWidth)

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
