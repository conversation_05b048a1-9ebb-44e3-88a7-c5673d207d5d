import axios from 'axios'
import { getToken } from '@/utils/auth'
import { Message } from 'element-ui'

/**
 * 通用文件上传工具 - 支持多种文件类型
 * 这个工具扩展了现有的文件上传功能，专门支持多种文件类型的上传
 */

/**
 * 通用文件上传函数 - 支持多种文件类型
 * @param {File} file - 要上传的文件对象
 * @param {Function} onSuccess - 上传成功回调函数
 * @param {Function} onError - 上传失败回调函数
 * @param {String} api - 上传接口地址，默认使用MinIO上传接口
 * @param {Number} maxSize - 文件大小限制(MB)，默认20MB
 * @returns {Promise} 上传结果的Promise
 */
export function uploadGeneralFile(file, onSuccess, onError, api = '/api/minio/upload', maxSize = 20) {
  // 验证文件大小
  const fileSizeMB = file.size / 1024 / 1024
  if (fileSizeMB > maxSize) {
    const error = new Error(`文件大小不能超过 ${maxSize}MB!`)
    Message.error(error.message)
    onError && onError(error)
    return Promise.reject(error)
  }

  // 构建上传表单
  const formData = new FormData()
  formData.append('file', file)

  // 设置请求头
  const config = {
    headers: {
      'Authorization': getToken(),
      'Content-Type': 'multipart/form-data'
    }
  }

  // 进行上传
  return axios.post(api, formData, config)
    .then(response => {
      if (response.status === 200 && response.data) {
        const result = {
          name: file.name,
          url: response.data.url || '',
          id: response.data.fileName || file.name,
          size: response.data.size || file.size,
          success: response.data.success || true,
          contentType: file.type || 'application/octet-stream'
        }

        // 调用成功回调
        onSuccess && onSuccess(result)
        return result
      } else {
        throw new Error('上传失败: 服务器返回异常')
      }
    })
    .catch(error => {
      const errorMsg = '文件上传失败: ' + (error.message || '未知错误')
      Message.error(errorMsg)
      onError && onError(error)
      return Promise.reject(error)
    })
}

/**
 * 获取文件图标
 * 根据文件类型返回适当的图标类名
 * @param {String} filename - 文件名或文件类型
 * @returns {String} 图标类名
 */
export function getFileIcon(filename) {
  if (!filename) return 'el-icon-document'

  const extension = filename.split('.').pop().toLowerCase()

  // 图片类型
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
    return 'el-icon-picture'
  }

  // 文档类型
  if (['doc', 'docx', 'txt', 'rtf'].includes(extension)) {
    return 'el-icon-document'
  }

  // PDF文件
  if (extension === 'pdf') {
    return 'el-icon-document-checked'
  }

  // 表格类型
  if (['xls', 'xlsx', 'csv'].includes(extension)) {
    return 'el-icon-tickets'
  }

  // 压缩文件
  if (['zip', 'rar', '7z', 'tar', 'gz'].includes(extension)) {
    return 'el-icon-folder'
  }

  // 音频文件
  if (['mp3', 'wav', 'ogg', 'flac'].includes(extension)) {
    return 'el-icon-headset'
  }

  // 视频文件
  if (['mp4', 'avi', 'mov', 'wmv', 'flv'].includes(extension)) {
    return 'el-icon-video-camera'
  }

  // 默认图标
  return 'el-icon-document'
}

/**
 * 格式化文件大小
 * @param {Number} size - 文件大小（字节）
 * @returns {String} 格式化后的文件大小
 */
export function formatFileSize(size) {
  if (size === null || size === undefined || isNaN(size)) {
    return '未知大小'
  }

  if (size < 1024) {
    return size + ' B'
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB'
  } else if (size < 1024 * 1024 * 1024) {
    return (size / (1024 * 1024)).toFixed(2) + ' MB'
  } else {
    return (size / (1024 * 1024 * 1024)).toFixed(2) + ' GB'
  }
}

/**
 * 判断文件类型并返回预览方式
 * @param {String} filename - 文件名
 * @returns {String} preview: 可预览; download: 下载查看
 */
export function getFileViewMode(filename) {
  if (!filename) return 'download'

  const extension = filename.split('.').pop().toLowerCase()

  // 可直接预览的文件类型
  const previewable = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'pdf']

  return previewable.includes(extension) ? 'preview' : 'download'
}

/**
 * 通用文件处理Mixin - 提供通用的文件处理方法
 */
export const GeneralFileHandler = {
  mixin: {
    methods: {
      /**
       * 处理通用文件变更
       * @param {Object} event - 文件变更事件
       * @param {Object} form - 数据对象
       * @returns {Boolean} 处理是否成功
       */
      handleGeneralFileChange(event, form) {
        if (!event || !event.fieldName || !form) {
          console.error('文件变更事件参数无效:', event)
          return false
        }

        const fieldName = event.fieldName
        const action = event.action || ''
        const file = event.file || {}

        console.log(`文件操作 ${action} 字段 ${fieldName}:`, event)

        // 处理文件删除
        if (action === 'remove' && file) {
          // 从本地列表中移除文件
          try {
            let files = JSON.parse(form[fieldName] || '[]')
            if (!Array.isArray(files)) files = []

            files = files.filter(item => item.uid !== file.uid)
            form[fieldName] = JSON.stringify(files)

            // 如果文件已上传到服务器，调用删除API
            if (file.url && this.minioDeleteApi) {
              // 直接使用完整URL，后端会处理
              console.log('正在删除服务器文件，完整URL:', file.url)

              // 发送删除请求
              this.$axios.get(`${this.minioDeleteApi}?fileName=${encodeURIComponent(file.url)}`)
                .then(response => {
                  if (response.data && response.data.success) {
                    this.$message.success('文件已成功从服务器删除')
                    console.log('文件删除成功:', file.url)
                  } else {
                    this.$message.error('文件删除失败')
                    console.error('删除文件失败:', response.data)
                  }
                })
                .catch(error => {
                  this.$message.error('删除文件出错')
                  console.error('删除文件出错:', error)
                })
            }

            return true
          } catch (e) {
            console.error('处理文件删除错误', e)
            return false
          }
        }

        // 处理文件上传成功
        if (action === 'success' && file) {
          try {
            let files = JSON.parse(form[fieldName] || '[]')
            if (!Array.isArray(files)) files = []

            // 检查文件是否已存在
            const fileExists = files.some(item => item.uid === file.uid)
            if (!fileExists) {
              files.push(file)
            }

            form[fieldName] = JSON.stringify(files)
            return true
          } catch (e) {
            console.error('处理文件上传错误', e)
            return false
          }
        }

        return false
      },

      /**
       * 初始化通用文件字段
       * @param {Array} fields - 要初始化的字段名数组
       */
      async initGeneralFileFields(fields) {
        if (!Array.isArray(fields) || !fields.length) return

        for (const field of fields) {
          if (!this.form[field]) {
            this.form[field] = '[]'
            continue
          }

          try {
            // 尝试解析为JSON
            if (this.form[field].startsWith('[')) {
              // 如果已经是JSON格式，检查URL格式并转换
              let fileList = JSON.parse(this.form[field])
              if (!Array.isArray(fileList)) fileList = []

              // 转换每个文件的URL
              const processedList = await this.processFileUrls(fileList)
              this.form[field] = JSON.stringify(processedList)
            } else if (typeof this.form[field] === 'string' && this.form[field].includes(',')) {
              const fileUrls = this.form[field].split(',').filter(url => url.trim())
              const fileList = fileUrls.map((url, index) => {
                const fileName = url.split('/').pop() || `文件${index + 1}`
                return {
                  uid: `file-${index}`,
                  name: fileName,
                  url: url,
                  status: 'success'
                }
              })

              // 转换每个文件的URL
              const processedList = await this.processFileUrls(fileList)
              this.form[field] = JSON.stringify(processedList)
            } else if (typeof this.form[field] === 'string' && this.form[field].trim()) {
              const url = this.form[field].trim()
              const fileName = url.split('/').pop() || `文件`
              const fileList = [{
                uid: 'file-0',
                name: fileName,
                url: url,
                status: 'success'
              }]

              // 转换每个文件的URL
              const processedList = await this.processFileUrls(fileList)
              this.form[field] = JSON.stringify(processedList)
            } else {
              this.form[field] = '[]'
            }
          } catch (e) {
            console.error(`初始化字段${field}失败:`, e)
            this.form[field] = '[]'
          }
        }
      },

      /**
       * 处理文件列表中的URL，将相对路径转换为预签名URL
       * @param {Array} fileList - 文件对象数组
       * @returns {Promise<Array>} - 处理后的文件对象数组
       */
      async processFileUrls(fileList) {
        if (!Array.isArray(fileList)) return []

        // 收集所有需要处理的文件路径
        const promises = fileList.map(async file => {
          if (file.url && !file.url.startsWith('http')) {
            try {
              // 保存原始路径用于后续操作（如删除）
              file.originalUrl = file.url

              // 获取预签名URL
              const response = await this.$axios.get('/api/minio/url', {
                params: { fileName: file.url }
              })

              if (response.data && response.data.success) {
                file.url = response.data.url
                console.log(`已转换文件URL: ${file.originalUrl} -> ${file.url.substr(0, 60)}...`)
              }
            } catch (error) {
              console.error(`获取预签名URL失败: ${file.url}`, error)
            }
          }
          return file
        })

        return Promise.all(promises)
      },

      /**
       * 提交前处理通用文件字段
       * @param {Array} fields - 要处理的字段名数组
       */
      prepareGeneralFileFields(fields) {
        if (!Array.isArray(fields) || !fields.length) return

        fields.forEach(field => {
          if (!this.form[field]) {
            this.form[field] = ''
            return
          }

          if (typeof this.form[field] === 'string' && this.form[field].startsWith('[')) {
            try {
              const fileList = JSON.parse(this.form[field])

              if (Array.isArray(fileList) && fileList.length > 0) {
                // 提取所有有效URL并用逗号连接
                const urls = fileList.map(item => item.url || '').filter(url => url).join(',')
                this.form[field] = urls
                console.log(`提交前处理字段${field}成功, 由JSON转为URL列表: ${this.form[field].substring(0, 50)}...`)
              } else {
                // 空数组转为空字符串
                this.form[field] = ''
                console.log(`字段${field}为空数组，已转为空字符串`)
              }
            } catch (e) {
              console.error(`提交前处理字段${field}失败:`, e)
              // 解析失败时，直接设为空字符串以避免错误
              this.form[field] = ''
            }
          } else if (this.form[field] === null || this.form[field] === undefined) {
            // null或undefined值处理为空字符串
            this.form[field] = ''
          }
        })
      }
    }
  }
}

// 导出通用函数
export default {
  uploadGeneralFile,
  getFileIcon,
  formatFileSize,
  getFileViewMode,
  GeneralFileHandler
}
