<template>
  <div class="db-tree" :style="{width: treeWidth + 'px'}">
    <!-- 工具栏固定顶部 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" icon="el-icon-connection" class="modern-btn connect-btn" size="small" @click="handleConnect">
          连接数据库
        </el-button>
        <el-button icon="el-icon-refresh" class="modern-btn refresh-btn" size="small" @click="handleRefresh">
          刷新
        </el-button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div v-if="databases.length > 0" class="search-container">
      <div class="search-wrapper">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索表名..."
          prefix-icon="el-icon-search"
          size="small"
          clearable
          class="search-input"
          @input="handleSearch"
          @clear="handleClearSearch"
        />
      </div>
      <div v-if="searchKeyword" class="search-info">
        <span v-if="searchResults.length > 0" class="search-count">
          {{ searchResults.length }} 个结果
        </span>
        <span v-else class="no-results">
          无匹配结果
        </span>
      </div>
    </div>

    <!-- 可滚动菜单容器 -->
    <div class="menu-container">

      <!-- 现代化树形结构 -->
      <div v-if="databases.length > 0" class="modern-tree">
        <!-- 搜索模式：显示搜索结果 -->
        <div v-if="searchKeyword && searchResults.length > 0" class="search-results">
          <div v-for="result in searchResults" :key="result.fullPath" class="tree-node table-node search-result">
            <el-tooltip
              :content="tableComments[result.tableName] || '暂无注释'"
              placement="right"
              :disabled="!tableComments[result.tableName]"
            >
              <div
                class="node-content table-content"
                @click="$emit('table-selected', result.dbName, result.tableName)"
                @mouseenter="loadTableComment(result.tableName)"
              >
                <div class="node-icon">
                  <i class="el-icon-document" />
                </div>
                <div class="node-label">
                  <span class="db-path">{{ result.dbName }}.{{ result.type }} /</span>
                  <span v-html="highlightSearchTerm(result.tableName)" />
                </div>
              </div>
            </el-tooltip>
          </div>
        </div>

        <!-- 正常模式：显示完整树形结构 -->
        <div v-else class="tree-structure">
          <div v-for="db in filteredDatabases" :key="db.name" class="tree-node db-node">
            <div class="node-content db-content" @click="toggleDb(db.name)">
              <div class="node-icon">
                <i :class="expandedDbs.includes(db.name) ? 'el-icon-folder-opened' : 'el-icon-folder'" />
              </div>
              <div class="node-label">{{ db.name }}</div>
              <div class="node-arrow">
                <i :class="expandedDbs.includes(db.name) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" />
              </div>
            </div>

            <div v-show="expandedDbs.includes(db.name)" class="node-children">
              <div v-for="(items, type) in db.items" :key="type" class="tree-node type-node">
                <div class="node-content type-content" @click="toggleType(db.name + '-' + type)">
                  <div class="node-icon">
                    <i class="el-icon-grid" />
                  </div>
                  <div class="node-label">{{ type }}</div>
                  <div class="node-count">({{ getFilteredItems(items).length }})</div>
                  <div class="node-arrow">
                    <i :class="expandedTypes.includes(db.name + '-' + type) ? 'el-icon-arrow-down' : 'el-icon-arrow-right'" />
                  </div>
                </div>

                <div v-show="expandedTypes.includes(db.name + '-' + type)" class="node-children">
                  <el-tooltip
                    v-for="item in getFilteredItems(items)"
                    :key="item.name"
                    :content="tableComments[item.name] || '暂无注释'"
                    placement="right"
                    :disabled="!tableComments[item.name]"
                  >
                    <div
                      class="tree-node table-node"
                      :class="{ 'highlight': isTableHighlighted(item.name) }"
                      @click="$emit('table-selected', db.name, item.name)"
                      @mouseenter="loadTableComment(item.name)"
                    >
                      <div class="node-content table-content">
                        <div class="node-icon">
                          <i class="el-icon-document" />
                        </div>
                        <div class="node-label" v-html="highlightSearchTerm(item.name)" />
                      </div>
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 空状态提示 -->
      <div v-else class="empty-state">
        <div class="empty-icon">
          <i class="el-icon-database" />
        </div>
        <div class="empty-text">暂无数据库连接</div>
        <div class="empty-desc">请点击上方"连接数据库"按钮</div>
      </div>

      <!-- 原来的Element UI菜单（暂时隐藏） -->
      <el-menu
        v-show="false"
        :default-active="selectedDb"
        :default-openeds="databases.map(db => 'db-' + db.name)"
        class="el-menu-vertical-demo"
        background-color="#1e1e1e"
        text-color="#cccccc"
        active-text-color="#ffffff"
        :unique-opened="false"
        @select="selectDb"
      >
        <!-- 动态数据库菜单 -->
        <el-sub-menu v-for="db in databases" :key="'db-' + db.name" :index="'db-' + db.name">
          <template slot="title">
            <i class="el-icon-folder-opened" />
            <span class="db-name">{{ db.name }}</span>
          </template>

          <!-- 动态表/视图/函数分类 -->
          <el-sub-menu
            v-for="(items, type) in db.items"
            :key="'type-' + db.name + '-' + type"
            :index="'type-' + db.name + '-' + type"
          >
            <template slot="title">
              <i :class="getIcon(type)" />
              <span>{{ type }}</span>
            </template>

            <!-- 具体表名 -->
            <el-tooltip
              v-for="item in items"
              :key="'table-' + db.name + '-' + type + '-' + item.name"
              :content="tableComments[item.name] || '暂无注释'"
              placement="right"
              :disabled="!tableComments[item.name]"
            >
              <el-menu-item
                :index="'table-' + db.name + '-' + type + '-' + item.name"
                @click="$emit('table-selected', db.name, item.name)"
                @mouseenter="loadTableComment(item.name)"
              >
                <i class="el-icon-document" />
                <span>{{ item.name }}</span>
              </el-menu-item>
            </el-tooltip>
          </el-sub-menu>
        </el-sub-menu>
      </el-menu>
    </div>

    <!-- 拖拽调整大小的手柄 -->
    <div class="resize-handle" @mousedown="startResize" />

    <!-- 数据库连接对话框 -->
    <el-dialog :visible.sync="dialogVisible" title="连接数据库" width="500px">
      <el-form ref="formRef" :model="formData" :rules="rules" label-width="80px">
        <!-- 表单项保持原样 -->
        <el-form-item label="主机" prop="host">
          <el-input v-model="formData.host" placeholder="请输入主机地址" @input="updateUrl" />
        </el-form-item>
        <el-form-item label="端口" prop="port">
          <el-input v-model.number="formData.port" type="number" placeholder="请输入端口号" @input="updateUrl" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="formData.username" placeholder="请输入用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="formData.password" type="password" placeholder="请输入密码" />
        </el-form-item>
        <el-form-item label="URL" prop="url">
          <el-input
            v-model="formData.url"
            placeholder="jdbc:mysql://主机:端口号/数据库名"
            @input="urlManuallyEdited = true"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addtosession">保存</el-button>
        <el-button type="primary" @click="handleTestConnection">测试连接</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { testConnection, getDatabases } from '@/api/AnalyseDB/DbConnection'
import { getTableComment } from '@/api/AnalyseDB/TableData'

export default {
  name: 'DatabaseSelector',

  data() {
    return {
      // 拖拽调整大小相关
      treeWidth: 240,
      resizing: false,

      // 数据库列表，从后端获取
      databases: [],

      selectedDb: '',

      // 展开状态管理
      expandedDbs: [],
      expandedTypes: [],

      // 表注释缓存
      tableComments: {},

      // 搜索相关
      searchKeyword: '',
      searchResults: [],
      // 保存搜索前的展开状态
      expandedDbsBeforeSearch: [],
      expandedTypesBeforeSearch: [],

      // 对话框相关数据
      dialogVisible: false,
      urlManuallyEdited: false,
      formData: {
        host: '',
        port: '',
        username: '',
        password: '',
        url: ''
      },

      // 表单验证规则
      rules: {
        host: [{ required: true, message: '请输入主机地址', trigger: 'blur' }],
        port: [{ required: true, message: '请输入端口号', trigger: 'blur' }],
        username: [{ required: true, message: '请输入用户名', trigger: 'blur' }],
        password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
        url: [{ required: true, message: '请输入数据库URL', trigger: 'blur' }]
      }
    }
  },

  computed: {
    // 过滤后的数据库列表（用于树形结构显示）
    filteredDatabases() {
      if (!this.searchKeyword) {
        return this.databases
      }
      // 如果有搜索关键词，返回包含匹配表的数据库
      return this.databases.filter(db => {
        return Object.values(db.items).some(items =>
          items.some(item =>
            item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
          )
        )
      })
    }
  },

  watch: {
    // 监听host和port的变化
    'formData.host': {
      handler() {
        this.updateUrl()
      }
    },
    'formData.port': {
      handler() {
        this.updateUrl()
      }
    },
    'formData.database': {
      handler() {
        this.updateUrl()
      }
    }
  },

  mounted() {
    // 先恢复表单数据
    this.loadFormDataFromStorage()
    this.fetchDatabases()
    // 添加全局事件监听器
    window.addEventListener('mousemove', this.doResize)
    window.addEventListener('mouseup', this.stopResize)
  },

  beforeDestroy() {
    // 清理事件监听器
    window.removeEventListener('mousemove', this.doResize)
    window.removeEventListener('mouseup', this.stopResize)
  },

  methods: {
    getIcon(type) {
      // 在Vue2中，我们不能直接使用Element Plus的图标
      // 这里返回对应的Element UI图标类名
      const icons = {
        tables: 'el-icon-grid',
        views: 'el-icon-view',
        functions: 'el-icon-operation'
      }
      return icons[type] || 'el-icon-document'
    },

    selectDb(name) {
      this.selectedDb = name
    },

    // 展开/折叠数据库
    toggleDb(dbName) {
      const index = this.expandedDbs.indexOf(dbName)
      if (index > -1) {
        this.expandedDbs.splice(index, 1)
      } else {
        this.expandedDbs.push(dbName)
      }
    },

    // 展开/折叠类型
    toggleType(typeKey) {
      const index = this.expandedTypes.indexOf(typeKey)
      if (index > -1) {
        this.expandedTypes.splice(index, 1)
      } else {
        this.expandedTypes.push(typeKey)
      }
    },

    // 搜索处理
    handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = []
        // 恢复搜索前的展开状态
        this.expandedDbs = [...this.expandedDbsBeforeSearch]
        this.expandedTypes = [...this.expandedTypesBeforeSearch]
        return
      }

      // 搜索时保存当前展开状态
      if (this.searchResults.length === 0) {
        this.expandedDbsBeforeSearch = [...this.expandedDbs]
        this.expandedTypesBeforeSearch = [...this.expandedTypes]
      }

      const keyword = this.searchKeyword.toLowerCase()
      const results = []

      this.databases.forEach(db => {
        Object.entries(db.items).forEach(([type, items]) => {
          items.forEach(item => {
            if (item.name.toLowerCase().includes(keyword)) {
              results.push({
                dbName: db.name,
                type: type,
                tableName: item.name,
                fullPath: `${db.name}.${type}.${item.name}`
              })
            }
          })
        })
      })

      this.searchResults = results

      // 如果有搜索结果，自动展开相关的数据库和类型
      if (results.length > 0) {
        this.autoExpandForSearch()
      }
    },

    // 清除搜索
    handleClearSearch() {
      this.searchKeyword = ''
      this.searchResults = []
      // 恢复搜索前的展开状态
      this.expandedDbs = [...this.expandedDbsBeforeSearch]
      this.expandedTypes = [...this.expandedTypesBeforeSearch]
    },

    // 自动展开包含搜索结果的数据库和类型
    autoExpandForSearch() {
      const dbsToExpand = new Set()
      const typesToExpand = new Set()

      this.searchResults.forEach(result => {
        dbsToExpand.add(result.dbName)
        typesToExpand.add(`${result.dbName}-${result.type}`)
      })

      this.expandedDbs = [...dbsToExpand]
      this.expandedTypes = [...typesToExpand]
    },

    // 高亮搜索关键词
    highlightSearchTerm(text) {
      if (!this.searchKeyword) {
        return text
      }

      const keyword = this.searchKeyword
      const regex = new RegExp(`(${keyword})`, 'gi')
      return text.replace(regex, '<span class="search-highlight">$1</span>')
    },

    // 判断表是否应该高亮
    isTableHighlighted(tableName) {
      if (!this.searchKeyword) {
        return false
      }
      return tableName.toLowerCase().includes(this.searchKeyword.toLowerCase())
    },

    // 获取过滤后的表项
    getFilteredItems(items) {
      if (!this.searchKeyword) {
        return items
      }
      return items.filter(item =>
        item.name.toLowerCase().includes(this.searchKeyword.toLowerCase())
      )
    },

    // 加载表注释
    async loadTableComment(tableName) {
      // 如果已经缓存了注释，直接返回
      if (this.tableComments[tableName]) {
        return
      }

      try {
        const response = await getTableComment({ tableName })
        if (response && response.comment) {
          this.$set(this.tableComments, tableName, response.comment)
        }
      } catch (error) {
        console.error('获取表注释失败:', error)
      }
    },

    // 更新URL
    updateUrl() {
      if (!this.urlManuallyEdited) {
        const { host, port, database } = this.formData
        let url = `jdbc:mysql://${host}:${port}`
        if (database) {
          url += `/${database}`
        }
        this.formData.url = url
      }
    },

    // 显示连接对话框
    handleConnect() {
      // 每次打开对话框时都重新加载保存的数据
      this.loadFormDataFromStorage()
      this.dialogVisible = true
      this.urlManuallyEdited = false
    },

    // 测试连接
    async handleTestConnection() {
      if (!this.$refs.formRef) return
      try {
        const response = await testConnection(this.formData)
        console.log('Response:', response)
        if (response.code === 200) {
          this.$message.success('连接测试成功')
        } else {
          const errorMessage = response.message || '连接测试失败'
          this.$message.error(errorMessage)
        }
      } catch (error) {
        const message = (error.response && error.response.data && error.response.data.message) || error.message || '连接测试失败'
        console.error('Error:', error)
        this.$message.error(message)
      }
    },

    // 提交表单
    async handleSubmit() {
      await this.$refs.formRef.validate()
      const response = await getDatabases(this.formData)
      localStorage.setItem('dbConfig', JSON.stringify(this.formData))

      // 将返回的列表转换为前端需要的结构
      this.databases = Object.entries(response).map(([dbName, tables]) => ({
        name: dbName,
        items: {
          表: tables.map((tableName) => ({ name: tableName }))
        }
      }))

      // 连接后不自动展开，让用户手动点击需要的数据库
      this.expandedDbs = []
      this.expandedTypes = []

      // 强制触发Vue的响应式更新
      this.$forceUpdate()

      // 延迟一下再强制更新，确保DOM更新
      this.$nextTick(() => {
        this.$forceUpdate()
      })

      this.dialogVisible = false
      this.$message.success('连接成功')
    },

    // 刷新数据库连接
    async handleRefresh() {
      try {
        const savedConfig = localStorage.getItem('dbConfig')
        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig)
          // 使用保存的配置重新获取数据库列表
          const response = await getDatabases(parsedConfig)

          // 将返回的列表转换为前端需要的结构
          this.databases = Object.entries(response).map(([dbName, tables]) => ({
            name: dbName,
            items: {
              表: tables.map((tableName) => ({ name: tableName }))
            }
          }))

          // 强制触发Vue的响应式更新
          this.$forceUpdate()

          // 延迟一下再强制更新，确保DOM更新
          this.$nextTick(() => {
            this.$forceUpdate()
          })

          this.$message.success('刷新成功')
        } else {
          this.$message.warning('请先连接数据库')
        }
      } catch (error) {
        console.error('刷新失败:', error)
        this.$message.error('刷新失败，请检查数据库连接')
      }
    },

    // 后端接口调用
    async fetchDatabases() {
      try {
        // 检查是否有保存的数据库配置，仅用于恢复表单数据
        const savedConfig = localStorage.getItem('dbConfig')
        if (savedConfig) {
          console.log('已从localStorage恢复数据库配置，等待用户手动连接')
        }
      } catch (error) {
        console.error('恢复数据库配置失败:', error)
      }
    },

    // 拖拽调整大小功能
    startResize(e) {
      this.resizing = true
      document.body.style.cursor = 'ew-resize'
      document.body.style.userSelect = 'none'
      e.preventDefault()
    },

    doResize(e) {
      if (this.resizing) {
        const min = 200
        const max = window.innerWidth * 0.5 // 限制最大宽度为窗口宽度的50%
        const newWidth = e.clientX
        this.treeWidth = Math.min(Math.max(newWidth, min), max)
      }
    },

    stopResize() {
      this.resizing = false
      document.body.style.cursor = ''
      document.body.style.userSelect = ''
    },

    // 保存连接表单
    addtosession() {
      localStorage.setItem('dbConfig', JSON.stringify(this.formData))
      this.dialogVisible = false
      this.$message.success('保存成功')
    },
    // 从localStorage恢复表单数据
    loadFormDataFromStorage() {
      try {
        const savedConfig = localStorage.getItem('dbConfig')
        if (savedConfig) {
          const parsedConfig = JSON.parse(savedConfig)
          // 恢复表单数据
          this.formData = {
            host: parsedConfig.host || '',
            port: parsedConfig.port || '',
            username: parsedConfig.username || '',
            password: parsedConfig.password || '',
            url: parsedConfig.url || ''
          }
          console.log('已从localStorage恢复数据库配置')
        }
      } catch (error) {
        console.error('恢复数据库配置失败:', error)
      }
    }
  }
}
</script>

<style scoped>
.db-tree {
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 260px;
  max-width: 50vw;
  height: 100vh;
  background: #1e1e1e;
  border-right: 1px solid #333;
  transition: width 0.1s ease;
}

.menu-container {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 8px;
}

.toolbar {
  padding: 8px 12px;
  border-bottom: 1px solid #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #1e1e1e;
}

.toolbar-left {
  display: flex;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
}

.width-indicator {
  font-size: 11px;
  color: #666;
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* 简洁按钮样式 */
.modern-btn {
  border-radius: 4px !important;
  font-size: 12px !important;
  height: 28px !important;
  transition: all 0.3s ease !important;
  border: 1px solid #444 !important;
}

.connect-btn {
  background-color: #409eff !important;
  color: white !important;
  border-color: #409eff !important;
}

.connect-btn:hover {
  background-color: #66b1ff !important;
  border-color: #66b1ff !important;
}

.refresh-btn {
  background-color: transparent !important;
  color: #cccccc !important;
  border-color: #444 !important;
}

.refresh-btn:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
  border-color: #666 !important;
}

.el-menu {
  border-right: none !important;
}

.db-tree >>> .el-sub-menu__title,
.db-tree >>> .el-menu-item {
  height: 32px !important;
  line-height: 32px !important;
  font-size: 13px !important;
  transition: all 0.3s;
}

.db-tree >>> .el-sub-menu__title:hover,
.db-tree >>> .el-menu-item:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  color: #ffffff !important;
}

.db-tree >>> .el-menu--inline {
  background-color: #252526 !important;
}

.db-tree >>> .el-menu-item.is-active {
  background-color: #37373d !important;
  color: #ffffff !important;
}

.el-icon {
  width: 16px;
  height: 16px;
  margin-right: 8px;
}

/* 对话框样式 */
.db-tree >>> .el-dialog {
  background-color: #1e1e1e !important;
  border: 1px solid #333;
}

.db-tree >>> .el-dialog__header {
  border-bottom: 1px solid #333;
  margin: 0;
  padding: 15px 20px;
}

.db-tree >>> .el-dialog__title {
  color: #cccccc;
  font-size: 14px;
}

.db-tree >>> .el-dialog__body {
  padding: 20px;
  color: #cccccc;
}

.db-tree >>> .el-form-item__label {
  color: #cccccc;
}

.db-tree >>> .el-input__inner {
  background-color: #2a2a2a;
  border: 1px solid #444;
  color: #cccccc;
}

.db-tree >>> .el-input__inner:hover,
.db-tree >>> .el-input__inner:focus {
  border-color: #409eff;
}

.db-tree >>> .el-select {
  width: 100%;
}

.w-full {
  width: 100%;
}

/* 拖拽调整大小的手柄 */
.resize-handle {
  position: absolute;
  right: -4px;
  top: 0;
  width: 8px;
  height: 100%;
  cursor: ew-resize;
  z-index: 10;
  background: transparent;
  transition: all 0.2s ease;
  border-radius: 0 4px 4px 0;
}

.resize-handle:hover {
  background-color: rgba(64, 158, 255, 0.3);
  width: 12px;
  right: -6px;
}

.resize-handle:active {
  background-color: rgba(64, 158, 255, 0.6);
  width: 12px;
  right: -6px;
}

/* 添加拖拽提示线 */
.resize-handle::before {
  content: '';
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  width: 2px;
  height: 30px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 1px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.resize-handle:hover::before {
  opacity: 1;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  padding-top: 20px;
  border-top: 1px solid #333;
}

/* 简洁清晰的树形结构样式 */
.modern-tree {
  padding: 4px 0;
}

.tree-node {
  margin-bottom: 1px;
}

.node-content {
  display: flex;
  align-items: flex-start;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  user-select: none;
  min-height: 32px;
  line-height: 1.4;
}

.node-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 8px;
  margin-top: 2px;
  font-size: 14px;
  color: #cccccc;
  flex-shrink: 0;
}

.node-label {
  flex: 1;
  font-size: 13px;
  color: #cccccc;
  font-weight: normal;
  word-wrap: break-word;
  word-break: break-all;
}

.node-count {
  font-size: 11px;
  color: #888;
  margin-left: 4px;
}

.node-arrow {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: #888;
  transition: transform 0.2s ease;
}

.node-children {
  margin-left: 20px;
}

/* 数据库节点样式 */
.db-node .db-content:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.db-node .node-icon {
  color: #cccccc;
}

.db-node .node-label {
  font-weight: 500;
}

/* 类型节点样式 */
.type-node .type-content {
  background-color: #252526;
}

.type-node .type-content:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.type-node .node-icon {
  color: #cccccc;
}

/* 表节点样式 */
.table-node .table-content:hover {
  background-color: rgba(255, 255, 255, 0.1);
  color: #ffffff;
}

.table-node .node-icon {
  color: #cccccc;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-icon i {
  font-size: 28px;
  color: #8b5cf6;
}

.empty-text {
  font-size: 16px;
  font-weight: 600;
  color: #e2e8f0;
  margin-bottom: 8px;
}

.empty-desc {
  font-size: 14px;
  color: #94a3b8;
}

/* 搜索栏样式 */
.search-container {
  padding: 8px 12px;
  border-bottom: 1px solid #333;
  background-color: #1e1e1e;
}

.search-wrapper {
  margin-bottom: 6px;
}

.search-input {
  width: 100%;
}

.search-input >>> .el-input__inner {
  background-color: #2a2a2a !important;
  border: 1px solid #444 !important;
  color: #cccccc !important;
  font-size: 12px !important;
  height: 28px !important;
  border-radius: 4px !important;
  transition: all 0.3s ease !important;
}

.search-input >>> .el-input__inner:hover {
  border-color: #666 !important;
}

.search-input >>> .el-input__inner:focus {
  border-color: #409eff !important;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1) !important;
}

.search-input >>> .el-input__prefix {
  color: #888 !important;
}

.search-input >>> .el-input__suffix {
  color: #888 !important;
}

.search-info {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 20px;
}

.search-count {
  font-size: 11px;
  color: #409eff;
  background: rgba(64, 158, 255, 0.1);
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: 500;
}

.no-results {
  font-size: 11px;
  color: #f56c6c;
  background: rgba(245, 108, 108, 0.1);
  padding: 2px 6px;
  border-radius: 2px;
}

/* 搜索结果样式 */
.search-results {
  padding: 4px 0;
}

.search-result .table-content {
  background-color: rgba(64, 158, 255, 0.05);
  border-left: 2px solid #409eff;
}

.search-result .table-content:hover {
  background-color: rgba(64, 158, 255, 0.1);
}

.db-path {
  font-size: 11px;
  color: #888;
  margin-right: 4px;
}

/* 搜索高亮样式 */
.search-highlight {
  background-color: #409eff;
  color: #ffffff;
  padding: 1px 2px;
  border-radius: 2px;
  font-weight: 600;
}

/* 高亮的表节点 */
.table-node.highlight .table-content {
  background-color: rgba(64, 158, 255, 0.05);
}

.table-node.highlight .table-content:hover {
  background-color: rgba(64, 158, 255, 0.1);
}
</style>
