<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">国别（国家）</label>
        <el-input v-model="query.country" clearable placeholder="国别（国家）" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">店铺名称</label>
        <el-input v-model="query.shopName" clearable placeholder="店铺名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">经营类目</label>
        <el-input v-model="query.operateType" clearable placeholder="经营类目" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">公司名称</label>
        <el-input v-model="query.companyName" clearable placeholder="公司名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">商品编号</label>
        <el-input v-model="query.goodsNo" clearable placeholder="商品编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">商品名称</label>
        <el-input v-model="query.goodsName" clearable placeholder="商品名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别（国家）">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="店铺名称">
            <el-input v-model="form.shopName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="经营类目">
            <el-input v-model="form.operateType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻日期">
            <el-date-picker v-model="form.enterDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司名称">
            <el-input v-model="form.companyName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品编号">
            <el-input v-model="form.goodsNo" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="form.goodsName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="图片">
            <el-input v-model="form.picture" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品分类">
            <el-input v-model="form.goodsType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="品牌">
            <el-input v-model="form.brand" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="风格">
            <el-input v-model="form.style" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品规格">
            <el-input v-model="form.goodsSpecificat" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品材质">
            <el-input v-model="form.goodsMaterial" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="颜色">
            <el-input v-model="form.color" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="单位">
            <el-input v-model="form.unit" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="市场价格">
            <el-input v-model="form.marketPrice" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="店铺价格">
            <el-input v-model="form.shopPrice" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品库存数">
            <el-input v-model="form.goodsInventory" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别（国家）" />
        <el-table-column prop="shopName" label="店铺名称" />
        <el-table-column prop="operateType" label="经营类目" />
        <el-table-column prop="enterDate" label="入驻日期" />
        <el-table-column prop="companyName" label="公司名称" />
        <el-table-column prop="goodsNo" label="商品编号" />
        <el-table-column prop="goodsName" label="商品名称" />
        <el-table-column prop="picture" label="图片" />
        <el-table-column prop="goodsType" label="商品分类" />
        <el-table-column prop="brand" label="品牌" />
        <el-table-column prop="style" label="风格" />
        <el-table-column prop="goodsSpecificat" label="商品规格" />
        <el-table-column prop="goodsMaterial" label="商品材质" />
        <el-table-column prop="color" label="颜色" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="marketPrice" label="市场价格" />
        <el-table-column prop="shopPrice" label="店铺价格" />
        <el-table-column prop="goodsInventory" label="商品库存数" />
        <el-table-column v-if="checkPer(['admin','yanfaOutTableGoodsSummaryListing:edit','yanfaOutTableGoodsSummaryListing:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudYanfaOutTableGoodsSummaryListing from '@/api/yanfa/yanfaOutTableGoodsSummaryListing'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, shopName: null, operateType: null, enterDate: null, companyName: null, goodsNo: null, goodsName: null, picture: null, goodsType: null, brand: null, style: null, goodsSpecificat: null, goodsMaterial: null, color: null, unit: null, marketPrice: null, shopPrice: null, goodsInventory: null }
export default {
  name: 'YanfaOutTableGoodsSummaryListing',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '户外家具上架产品汇总表(桌几类)', url: 'api/yanfaOutTableGoodsSummaryListing', idField: 'id', sort: 'id,desc', crudMethod: { ...crudYanfaOutTableGoodsSummaryListing }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'yanfaOutTableGoodsSummaryListing:add'],
        edit: ['admin', 'yanfaOutTableGoodsSummaryListing:edit'],
        del: ['admin', 'yanfaOutTableGoodsSummaryListing:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别（国家）' },
        { key: 'shopName', display_name: '店铺名称' },
        { key: 'operateType', display_name: '经营类目' },
        { key: 'companyName', display_name: '公司名称' },
        { key: 'goodsNo', display_name: '商品编号' },
        { key: 'goodsName', display_name: '商品名称' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
