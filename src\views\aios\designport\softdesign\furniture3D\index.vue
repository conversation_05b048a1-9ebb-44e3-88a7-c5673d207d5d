<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="产品名称" prop="productName">
                  <el-input v-model="form.productName" style="width: 100%;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="产品编号" prop="productId">
                  <el-input v-model="form.productId" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" style="width: 100%;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="3D模型计划完成时间">
                  <el-date-picker v-model="form.planCompletionTime" type="datetime" style="width: 100%;" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="最终稿确认完成时间">
                  <el-date-picker v-model="form.completionTime" type="datetime" style="width: 100%;" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="状态" prop="status">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%;">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-divider content-position="left"><i class="el-icon-picture" /> 家具模型版本</el-divider>
            <div class="table-container">
              <el-table :data="form.furnitureVersions" border style="width: 100%">
                <el-table-column type="index" label="序号" width="50" align="center" />
                <el-table-column label="开始时间" width="160">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.startTime"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%;"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="绘制人" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.draftsman" size="mini" placeholder="请输入绘制人" />
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription" size="mini" type="textarea" :rows="1" placeholder="请输入异常说明" />
                  </template>
                </el-table-column>
                <el-table-column label="附件" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.formatFile3d"
                      :show-url="true"
                      :field-name="'formatFile3d'"
                      v-bind="fileUploadConfig"
                      :use-minio-delete="true"
                      :hide-remove="isViewMode"
                      @change="handleFileChange('formatFile3d', $event, scope.row)"
                      @file-change="handleFileListChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="图纸版本" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.drawingVersion" size="mini" placeholder="请选择" style="width: 100%;">
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="状态" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.commitStatus" size="mini" placeholder="请选择" style="width: 100%;">
                      <el-option
                        v-for="item in dict.commit_status"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="实际完成日期" width="160">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.actualvcompletionDate"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%;"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="removeFurnitureVersion(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="addFurnitureVersion">新增</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>
            <div class="form-section-title">预览图</div>
            <file-upload
              :field-value.sync="form.preview"
              :limit="5"
              :upload-to-server="true"
              :api-url="minioUploadApi"
              :hide-remove="isViewMode"
              @change="handleFileListChange($event, form)"
            />

            <div class="form-section-title">最终稿附件</div>
            <general-file-upload
              v-model="form.dwgFormatFile1"
              :field-name="'dwgFormatFile1'"
              v-bind="fileUploadConfig"
              :use-minio-delete="true"
              :hide-remove="isViewMode"
              @change="handleFileChange('dwgFormatFile1', $event)"
              @file-change="handleFileListChange"
            />
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="productName" label="产品名称" />
        <el-table-column prop="productId" label="产品编号" />
        <el-table-column label="预览图" width="180">
          <template slot-scope="scope">
            <div class="preview-images-container">
              <div v-for="(img, index) in getPreviewImageList(scope.row.preview).slice(0, 2)" :key="index">
                <el-image
                  style="width: 70px; height: 70px; margin-right: 5px;"
                  :src="img"
                  :preview-src-list="getPreviewImageList(scope.row.preview)"
                  fit="cover"
                />
              </div>
              <span v-if="!getPreviewImageList(scope.row.preview).length">无图片</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjJiaju3d:edit','tumaiSjJiaju3d:del','tumaiSjJiaju3d:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiSjJiaju3d from '@/api/aios/designport/softdesign/tumaiSjJiaju3d'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, productName: null, productId: null, oddNumbers: null, projectNature: null, completionTime: null, dwgFormatFile1: null, planCompletionTime: null, uid: null, projectAddress: null, typeOfService: null, projectName: null, projectid: null, projectNumber: null, projectDeliveryPeriod: null, projectOverview: null, tabDate: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, createtime: null, isturn: null, preview: null, furnitureVersions: [], modelData: null }
export default {
  name: 'TumaiSjJiaju3d',
  components: { pagination, crudOperation, rrOperation, FileUpload, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'drawing_version', 'commit_status'],
  cruds() {
    return CRUD({ title: '家具3D', url: 'api/tumaiSjJiaju3d', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjJiaju3d }})
  },
  data() {
    return {
      activeTab: 'basicInfo',
      permission: {
        add: ['admin', 'tumaiSjJiaju3d:add'],
        edit: ['admin', 'tumaiSjJiaju3d:edit'],
        del: ['admin', 'tumaiSjJiaju3d:del'],
        view: ['admin', 'tumaiSjJiaju3d:edit', 'tumaiSjJiaju3d:view']
      },
      rules: {
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        productName: [
          { required: true, message: '产品名称不能为空', trigger: 'blur' }
        ],
        productId: [
          { required: true, message: '产品编号不能为空', trigger: 'blur' }
        ],
        status: [
          { required: true, message: '状态不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'projectNumber', display_name: '项目编号' }
      ],
      // 文件字段列表
      fileFields: ['preview', 'dwgFormatFile1'],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.dwg,.dxf,.skp,.max,.zip,.rar',
        maxFiles: 5,
        tipText: '支持上传图片、模型文件及常用文档',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true,
        showUrl: true,
        nameType: 'url'
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },
    // 钩子：在编辑前执行
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      this.activeTab = 'basicInfo'
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'preview',
        'dwgFormatFile1'
      ])
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
      // 初始化家具模型版本数据
      this.initFurnitureVersions(form)
      // 初始化子表单中的文件字段
      this.initSubformFileFields(form.furnitureVersions, 'formatFile3d')
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      // 删除主表单的文件
      deleteRecordFiles(data, [
        'preview',
        'dwgFormatFile1'
      ])
      // 删除子表单中的文件
      deleteSubformFiles(data, 'furnitureVersions', 'formatFile3d')
      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.activeTab = 'basicInfo'
      this.initAttachmentFields([
        'preview',
        'dwgFormatFile1'
      ])
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
      this.initFurnitureVersions(form)
      this.initSubformFileFields(form.furnitureVersions, 'formatFile3d')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },
    // 钩子：在添加前执行
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      this.activeTab = 'basicInfo'

      // 初始化为空数组
      this.form.preview = '[]'
      this.form.dwgFormatFile1 = '[]'

      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)

      // 清空并添加一个默认的版本记录
      form.furnitureVersions = []
      this.addFurnitureVersion()
    },
    // 钩子：提交前
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始表单数据:', JSON.stringify({
          preview: this.form.preview,
          dwgFormatFile1: this.form.dwgFormatFile1
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'preview',
          'dwgFormatFile1'
        ])

        // 处理通用文件字段
        this.prepareGeneralFileFields(this.fileFields)

        console.log('处理主表单附件后:', JSON.stringify({
          preview: this.form.preview,
          dwgFormatFile1: this.form.dwgFormatFile1
        }))

        // 处理家具模型版本的文件字段
        // 确保每个子表单的formatFile3d字段都已正确处理
        if (crud.form.furnitureVersions && Array.isArray(crud.form.furnitureVersions)) {
          crud.form.furnitureVersions.forEach(version => {
            // 确保formatFile3d字段格式正确
            if (version.formatFile3d) {
              try {
                // 检查是否已经是JSON字符串
                if (typeof version.formatFile3d === 'string') {
                  if (!version.formatFile3d.startsWith('[')) {
                    // 如果是单个URL字符串，转换为JSON数组格式
                    version.formatFile3d = JSON.stringify([{ url: version.formatFile3d }])
                  }
                } else if (Array.isArray(version.formatFile3d)) {
                  // 如果是数组对象，转换为JSON字符串
                  version.formatFile3d = JSON.stringify(version.formatFile3d)
                }
              } catch (e) {
                console.error('处理子表单formatFile3d字段失败:', e)
                // 确保错误时设置默认值
                version.formatFile3d = '[]'
              }
            } else {
              // 确保为空时设置正确的默认值
              version.formatFile3d = '[]'
            }

            // 移除多余的3dFormatFile字段
            delete version['3dFormatFile']
          })
        }

        this.prepareSubformFileFields(crud.form.furnitureVersions, 'formatFile3d')

        // 将furnitureVersions数组转换为JSON字符串格式传递给后端
        crud.form.furnitureVersions = JSON.stringify(crud.form.furnitureVersions)

        // 清除前端内部使用的modelData字段，避免重复发送给后端
        delete crud.form.modelData

        console.log('处理后的版本数据:', crud.form.furnitureVersions)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化家具模型版本数据
    initFurnitureVersions(form) {
      // 如果furnitureVersions是JSON字符串，需要解析为数组
      if (typeof form.furnitureVersions === 'string') {
        try {
          form.furnitureVersions = JSON.parse(form.furnitureVersions)
        } catch (e) {
          console.error('解析家具模型版本数据失败:', e)
          form.furnitureVersions = []
        }
      }

      // 确保furnitureVersions是数组格式
      if (!Array.isArray(form.furnitureVersions)) {
        form.furnitureVersions = []
      }

      // 确保每个版本都有formatFile3d字段
      form.furnitureVersions.forEach(item => {
        // 如果有旧版本的3dFormatFile字段，转换为新字段formatFile3d
        if (item['3dFormatFile'] && !item.formatFile3d) {
          item.formatFile3d = item['3dFormatFile']
          // 如果是URL字符串，转换为JSON格式
          if (typeof item.formatFile3d === 'string' && !item.formatFile3d.startsWith('[')) {
            item.formatFile3d = JSON.stringify([{
              // name: '3D模型附件',
              url: item.formatFile3d
            }])
          }
        }

        if (!item.formatFile3d) {
          item.formatFile3d = '[]'
        }

        // 移除多余的3dFormatFile字段
        delete item['3dFormatFile']
      })
    },
    // 添加家具模型版本
    addFurnitureVersion() {
      if (!this.form.furnitureVersions) {
        this.form.furnitureVersions = []
      }

      this.form.furnitureVersions.push({
        startTime: null,
        draftsman: null,
        exceptionDescription: null,
        formatFile3d: '[]',
        drawingVersion: null,
        commitStatus: null,
        actualvcompletionDate: null
      })
    },

    // 移除家具模型版本
    removeFurnitureVersion(index) {
      // 获取要删除的行数据
      const rowData = this.form.furnitureVersions[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        deleteSubformRowFiles(rowData, 'formatFile3d')
      }
      this.form.furnitureVersions.splice(index, 1)

      // 确保至少有一条记录
      if (this.form.furnitureVersions.length === 0) {
        this.addFurnitureVersion()
      }
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        // 获取目标对象
        const targetObject = row || this.form

        // 确保事件格式正确
        if (!event || !event.action) {
          console.warn('文件变更事件格式不正确')
          return false
        }

        // 如果是删除操作，确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName || (event.target && event.target.fieldName)
          if (!fieldName) {
            console.warn('无法确定字段名称')
            return false
          }

          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)

          // 确保删除操作后更新了表单数据
          if (targetObject && fieldName) {
            try {
              // 获取当前的文件列表
              const currentValue = targetObject[fieldName]
              let fileList = []

              if (typeof currentValue === 'string') {
                try {
                  // 尝试解析JSON字符串
                  fileList = JSON.parse(currentValue)
                } catch (e) {
                  // 如果不是有效的JSON，可能是单个URL
                  if (currentValue && currentValue.trim() !== '') {
                    fileList = [{ url: currentValue }]
                  }
                }
              } else if (Array.isArray(currentValue)) {
                fileList = [...currentValue]
              }

              // 过滤掉已删除的文件
              fileList = fileList.filter(file => file.url !== event.file.url)

              // 更新回表单对象
              targetObject[fieldName] = JSON.stringify(fileList)

              console.log(`删除文件后更新${fieldName}为:`, targetObject[fieldName])
            } catch (e) {
              console.error('更新删除后的文件列表失败:', e)
            }
          }
        }

        // 使用GeneralFileHandler的处理方法
        try {
          const result = this.handleGeneralFileChange(event, targetObject)
          return result
        } catch (error) {
          console.error('调用handleGeneralFileChange失败:', error)
          return false
        }
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files, row) {
      console.log(`处理文件变更: ${fieldName}`, files)

      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        if (row) {
          // 子表单的处理
          row[fieldName] = jsonStr
          console.log(`子表单字段${fieldName}更新为:`, row[fieldName])
        } else {
          // 主表单的处理
          this.form[fieldName] = jsonStr
          console.log(`主表单字段${fieldName}更新为:`, this.form[fieldName])
        }
      } else if (files === null || files === undefined || (typeof files === 'string' && files === '[]')) {
        // 处理文件被完全删除的情况
        if (row) {
          row[fieldName] = '[]'
        } else {
          this.form[fieldName] = '[]'
        }
      }
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    },

    // 获取预览图的第一张图片用于列表显示
    getPreviewImage(previewStr) {
      try {
        if (!previewStr || previewStr === '[]') {
          return ''
        }

        // 预览图字段可能是JSON字符串或者是单个URL字符串
        if (previewStr.startsWith('[')) {
          const previewList = JSON.parse(previewStr)
          if (previewList && previewList.length > 0) {
            return previewList[0].url || ''
          }
        } else {
          // 如果是直接的URL字符串
          return previewStr
        }

        return ''
      } catch (error) {
        console.error('解析预览图时出错:', error)
        return ''
      }
    },

    // 获取预览图列表用于大图预览
    getPreviewImageList(previewStr) {
      try {
        if (!previewStr || previewStr === '[]') {
          return []
        }

        // 预览图字段可能是JSON字符串或者是单个URL字符串，或者是通过逗号分隔的URL字符串
        if (previewStr.startsWith('[')) {
          // JSON数组格式
          const previewList = JSON.parse(previewStr)
          if (previewList && previewList.length > 0) {
            return previewList.map(item => item.url || '').filter(url => url)
          }
        } else if (previewStr.includes(',')) {
          // 通过逗号分隔的多个URL
          return previewStr.split(',').map(url => url.trim()).filter(url => url)
        } else {
          // 如果是直接的单个URL字符串
          return [previewStr]
        }

        return []
      } catch (error) {
        console.error('解析预览图列表时出错:', error)
        return []
      }
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 158px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.preview-images-container {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>
