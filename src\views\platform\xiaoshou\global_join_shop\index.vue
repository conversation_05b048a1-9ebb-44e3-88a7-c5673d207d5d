<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">国别</label>
        <el-input v-model="query.country" clearable placeholder="国别" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">门店类型</label>
        <el-input v-model="query.shopType" clearable placeholder="门店类型" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">联系人姓名</label>
        <el-input v-model="query.contactName" clearable placeholder="联系人姓名" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">联系电话</label>
        <el-input v-model="query.contactPhone" clearable placeholder="联系电话" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">邮箱</label>
        <el-input v-model="query.email" clearable placeholder="邮箱" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">门店地址</label>
        <el-input v-model="query.shopAddr" clearable placeholder="门店地址" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="门店类型">
            <el-input v-model="form.shopType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="联系人姓名">
            <el-input v-model="form.contactName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="联系电话">
            <el-input v-model="form.contactPhone" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="form.email" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="门店地址">
            <el-input v-model="form.shopAddr" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="面积㎡">
            <el-input v-model="form.area" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="经营类目">
            <el-input v-model="form.operateType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="立项日期">
            <el-date-picker v-model="form.projectDate" type="datetime" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别" />
        <el-table-column prop="shopType" label="门店类型" />
        <el-table-column prop="contactName" label="联系人姓名" />
        <el-table-column prop="contactPhone" label="联系电话" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="shopAddr" label="门店地址" />
        <el-table-column prop="area" label="面积㎡" />
        <el-table-column prop="operateType" label="经营类目" />
        <el-table-column prop="projectDate" label="立项日期" />
        <el-table-column v-if="checkPer(['admin','xiaoshouGlobalJoinShopSummary:edit','xiaoshouGlobalJoinShopSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudXiaoshouGlobalJoinShopSummary from '@/api/xiaoshou/xiaoshouGlobalJoinShopSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, shopType: null, contactName: null, contactPhone: null, email: null, shopAddr: null, area: null, operateType: null, projectDate: null }
export default {
  name: 'XiaoshouGlobalJoinShopSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '全球加盟门店汇总', url: 'api/xiaoshouGlobalJoinShopSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudXiaoshouGlobalJoinShopSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'xiaoshouGlobalJoinShopSummary:add'],
        edit: ['admin', 'xiaoshouGlobalJoinShopSummary:edit'],
        del: ['admin', 'xiaoshouGlobalJoinShopSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别' },
        { key: 'shopType', display_name: '门店类型' },
        { key: 'contactName', display_name: '联系人姓名' },
        { key: 'contactPhone', display_name: '联系电话' },
        { key: 'email', display_name: '邮箱' },
        { key: 'shopAddr', display_name: '门店地址' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
