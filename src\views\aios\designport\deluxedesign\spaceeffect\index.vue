<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="模型计划完成时间">
                  <el-date-picker v-model="form.planCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30" />
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="楼盘名称">
                  <el-input v-model="form.buildingName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼盘编号" prop="buildingNumber">
                  <el-input v-model="form.buildingNumber" />
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="项目id">
                  <el-input v-model="form.projectid" />
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" />
            </el-form-item>
            <!-- <el-form-item label="服务类型">
              <el-input v-model="form.typeOfService" :rows="3" type="textarea" />
            </el-form-item> -->

            <el-divider content-position="left"><i class="el-icon-document" /> 设计阶段效果图</el-divider>
            <div class="table-container">
              <el-table :data="designPhaseList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="提交日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.submitDate" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="绘制人" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.draftsman1" placeholder="请输入绘制人" />
                  </template>
                </el-table-column>
                <el-table-column label="空间" width="150">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.space" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.space"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="相关文件" width="150">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.fileContent"
                      size="small"
                      :field-name="'fileContent'"
                      v-bind="tableFileUploadConfig"
                      :use-minio-delete="true"
                      list-type="text"
                      show-file-list
                      show-file-name
                      :hide-remove="isViewMode"
                      @change="handleSubformFileChange($event, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="版本" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.drawingVersion" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="开始制作日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.dateCommenced" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="异常说明及状态" width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription" placeholder="请输入异常说明" :rows="2" type="textarea" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveDesignPhase(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDesignPhase">新增</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 最终效果图</el-divider>
            <div class="table-container">
              <el-table :data="finalEffectList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="空间" width="200">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.space" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.space"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="效果图附件" min-width="450">
                  <template slot-scope="scope">
                    <file-upload
                      :field-value.sync="scope.row.renderingsAttachment"
                      :limit="4"
                      :upload-to-server="true"
                      :api-url="minioUploadApi"
                      :hide-remove="isViewMode"
                      @change="(event) => handleFileListChange(event, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveFinalEffect(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddFinalEffect">新增</el-button>
              </div>
            </div>
          </div>

        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjKongjian:edit','tumaiSjKongjian:del','tumaiSjKongjian:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible.sync="dialogVisible" append-to-body>
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudTumaiSjKongjian from '@/api/aios/designport/deluxedesign/tumaiSjKongjian'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, familyName: null,
  projectDesignNumber: null, projectNumber: null, projectAddr: null, projectNature: null,
  serviceType: null, projectOverview: null, planCompletionTime: null, uid: null,
  buildingAddress: null, typeOfService: null, tabDate: null, buildingNumber: null,
  buildingName: null, masterBedroomAccessory: null, secondaryAccessory: null,
  elderlyRoomAccessories: null, spaceRenderingsAttachment: null, projectName: null,
  projectid: null, projectDeliveryPeriod: null, optdt: null, optid: null, optname: null,
  applydt: null, explain: null, status: null, createtime: null, isturn: null,
  // 设计阶段效果图子表单数据
  designPhaseData: '[]',
  // 最终效果图子表单数据
  finalEffectData: '[]'
}
export default {
  name: 'TumaiSjKongjian',
  components: { pagination, crudOperation, rrOperation, FileUpload, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'space', 'drawing_version'],
  cruds() {
    return CRUD({ title: '空间效果图', url: 'api/tumaiSjKongjian', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjKongjian }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjKongjian:add'],
        edit: ['admin', 'tumaiSjKongjian:edit'],
        del: ['admin', 'tumaiSjKongjian:del'],
        view: ['admin', 'tumaiSjKongjian:edit', 'tumaiSjKongjian:view']
      },
      // 设计阶段效果图列表
      designPhaseList: [],
      // 最终效果图列表
      finalEffectList: [],
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      masterBedroomAttachList: [],
      secondaryBedroomAttachList: [],
      elderlyRoomAttachList: [],
      spaceRenderingsAttachList: [],
      // 表格中文件上传配置
      tableFileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 2,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true,
        showFileList: true,
        showFileName: true
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        buildingNumber: [
          { required: true, message: '楼盘编号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)

      // 初始化主表单附件字段
      this.initAttachmentFields([
        'masterBedroomAccessory',
        'secondaryAccessory',
        'elderlyRoomAccessories',
        'spaceRenderingsAttachment'
      ])
      // 初始化设计阶段效果图列表
      this.initDesignPhaseList()
      // 初始化最终效果图列表
      this.initFinalEffectList()
      // 初始化子表单中的文件字段
      this.initSubformFileFields(this.designPhaseList, 'fileContent')
      this.initSubformFileFields(this.finalEffectList, 'renderingsAttachment')
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'masterBedroomAccessory',
        'secondaryAccessory',
        'elderlyRoomAccessories',
        'spaceRenderingsAttachment'
      ])
      this.initDesignPhaseList()
      this.initFinalEffectList()
      this.initSubformFileFields(this.designPhaseList, 'fileContent')
      this.initSubformFileFields(this.finalEffectList, 'renderingsAttachment')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.masterBedroomAccessory = '[]'
      this.form.secondaryAccessory = '[]'
      this.form.elderlyRoomAccessories = '[]'
      this.form.spaceRenderingsAttachment = '[]'

      // 清空设计阶段效果图列表和最终效果图列表
      this.designPhaseList = []
      this.finalEffectList = []

      // 添加一个空的记录
      this.handleAddDesignPhase()
      this.handleAddFinalEffect()
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      // 删除子表单中的文件
      deleteSubformFiles(data, 'designPhaseData', 'fileContent')
      deleteSubformFiles(data, 'finalEffectData', 'renderingsAttachment')

      return true
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始子表单数据:', JSON.stringify({
          designPhaseList: this.designPhaseList,
          finalEffectList: this.finalEffectList
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'masterBedroomAccessory',
          'secondaryAccessory',
          'elderlyRoomAccessories',
          'spaceRenderingsAttachment'
        ])

        // 处理子表单中的文件字段
        this.prepareSubformFileFields(this.designPhaseList, 'fileContent')
        this.prepareSubformFileFields(this.finalEffectList, 'renderingsAttachment')

        // 将处理后的子表单数据同步回表单
        crud.form.designPhaseData = JSON.stringify(this.designPhaseList)
        crud.form.finalEffectData = JSON.stringify(this.finalEffectList)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化设计阶段效果图列表
    initDesignPhaseList() {
      try {
        this.designPhaseList = this.form.designPhaseData ? JSON.parse(this.form.designPhaseData) : []
        if (!Array.isArray(this.designPhaseList)) {
          this.designPhaseList = []
        }

        // 确保每行都有文件字段
        this.designPhaseList.forEach(item => {
          if (!item.fileContent) {
            item.fileContent = '[]'
          } else if (typeof item.fileContent === 'string' && !item.fileContent.startsWith('[')) {
            // 处理URL格式的情况，转换为标准JSON格式
            item.fileContent = this.convertUrlsToFileArray(item.fileContent)
          }
        })
      } catch (e) {
        console.error('解析设计阶段效果图数据失败:', e)
        this.designPhaseList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.designPhaseList.length === 0) {
        this.handleAddDesignPhase()
      }
    },

    // 将URL字符串转换为文件数组
    convertUrlsToFileArray(urlStr) {
      if (!urlStr) return '[]'

      try {
        // 如果已经是JSON格式，直接返回
        if (urlStr.startsWith('[') && urlStr.endsWith(']')) {
          return urlStr
        }

        // 处理逗号分隔的多个URL
        const urls = urlStr.split(',')
        const files = urls.map(url => {
          // 从URL提取文件名
          const fileName = url.substring(url.lastIndexOf('/') + 1)
          return {
            name: fileName,
            url: url,
            size: 0,
            type: ''
          }
        })

        return JSON.stringify(files)
      } catch (e) {
        console.error('URL转换文件数组出错:', e)
        return '[]'
      }
    },

    // 初始化最终效果图列表
    initFinalEffectList() {
      try {
        this.finalEffectList = this.form.finalEffectData ? JSON.parse(this.form.finalEffectData) : []
        if (!Array.isArray(this.finalEffectList)) {
          this.finalEffectList = []
        }

        // 确保每行都有文件字段
        this.finalEffectList.forEach(item => {
          if (!item.renderingsAttachment) {
            item.renderingsAttachment = '[]'
          } else if (typeof item.renderingsAttachment === 'string' && !item.renderingsAttachment.startsWith('[')) {
            // 处理URL格式的情况，转换为标准JSON格式
            item.renderingsAttachment = this.convertUrlsToFileArray(item.renderingsAttachment)
          }
        })
      } catch (e) {
        console.error('解析最终效果图数据失败:', e)
        this.finalEffectList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.finalEffectList.length === 0) {
        this.handleAddFinalEffect()
      }
    },

    // 添加设计阶段效果图
    handleAddDesignPhase() {
      this.designPhaseList.push({
        submitDate: null,
        draftsman1: null,
        space: null,
        fileContent: '[]',
        drawingVersion: null,
        dateCommenced: null,
        exceptionDescription: null
      })
    },

    // 添加最终效果图
    handleAddFinalEffect() {
      this.finalEffectList.push({
        space: null,
        renderingsAttachment: '[]'
      })
    },

    // 获取指定行的设计阶段效果图文件列表
    getPhaseFileList(index) {
      if (index >= 0 && index < this.designPhaseList.length) {
        const item = this.designPhaseList[index]
        try {
          return JSON.parse(item.fileContent || '[]')
        } catch (e) {
          console.error('解析设计阶段文件内容失败:', e)
          return []
        }
      }
      return []
    },

    // 获取指定行的最终效果图文件列表
    getFinalEffectFileList(index) {
      if (index >= 0 && index < this.finalEffectList.length) {
        const item = this.finalEffectList[index]
        try {
          return JSON.parse(item.renderingsAttachment || '[]')
        } catch (e) {
          console.error('解析最终效果图文件内容失败:', e)
          return []
        }
      }
      return []
    },

    // 移除设计阶段效果图
    handleRemoveDesignPhase(index) {
      // 获取要删除的行数据
      const rowData = this.designPhaseList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        console.log('删除子表单行文件，行数据:', rowData)
        deleteSubformRowFiles(rowData, 'fileContent')
      }
      this.designPhaseList.splice(index, 1)
      // 如果删除后列表为空，添加一个新的空行
      if (this.designPhaseList.length === 0) {
        this.handleAddDesignPhase()
      }
    },

    // 移除最终效果图
    handleRemoveFinalEffect(index) {
      // 获取要删除的行数据
      const rowData = this.finalEffectList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        console.log('删除子表单行文件，行数据:', rowData)
        deleteSubformRowFiles(rowData, 'renderingsAttachment')
      }
      this.finalEffectList.splice(index, 1)
      // 如果删除后列表为空，添加一个新的空行
      if (this.finalEffectList.length === 0) {
        this.handleAddFinalEffect()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      // 确保事件对象格式正确
      if (event && event.action === 'success' && event.file && event.file.response) {
        // 根据上下文自动判断字段名
        if (!event.fieldName) {
          if (row === this.form) {
            // 在主表单中
            if (event.target && this.masterBedroomAttachList === event.target) {
              event.fieldName = 'masterBedroomAccessory'
            } else if (event.target && this.secondaryBedroomAttachList === event.target) {
              event.fieldName = 'secondaryAccessory'
            } else if (event.target && this.elderlyRoomAttachList === event.target) {
              event.fieldName = 'elderlyRoomAccessories'
            } else if (event.target && this.spaceRenderingsAttachList === event.target) {
              event.fieldName = 'spaceRenderingsAttachment'
            }
          } else {
            // 在子表单中
            if (this.designPhaseList.includes(row)) {
              event.fieldName = 'fileContent'
            } else if (this.finalEffectList.includes(row)) {
              event.fieldName = 'renderingsAttachment'
            }
          }
        }
      }

      let result = false

      try {
        if (row === this.form) {
          // 主表单文件处理
          result = this.handleAttachmentChange(event, row)
        } else {
          // 子表单文件处理
          result = this.handleSubformFileChange(event, row)
        }

        // if (!result) {
        //   console.warn('文件处理返回失败结果')
        //   this.$message.warning('文件处理失败，请重试')
        // }

        return result
      } catch (error) {
        // console.error('文件处理过程中发生错误:', error)
        // this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理行的设计阶段效果图文件移除
    handleRemovePhaseFile(rowIndex, file, fileList) {
      if (rowIndex >= 0 && rowIndex < this.designPhaseList.length) {
        const event = {
          action: 'remove',
          fieldName: 'fileContent',
          file: file,
          fileList: fileList
        }
        this.handleFileListChange(event, this.designPhaseList[rowIndex])
      }
    },

    // 处理行的设计阶段效果图文件改变
    handleChangePhaseFile(rowIndex, file, fileList) {
      if (rowIndex >= 0 && rowIndex < this.designPhaseList.length) {
        const event = {
          action: file.status || 'change',
          fieldName: 'fileContent',
          file: file,
          fileList: fileList
        }
        this.handleFileListChange(event, this.designPhaseList[rowIndex])
      }
    },

    // 处理行的最终效果图文件移除
    handleRemoveFinalEffectFile(rowIndex, file, fileList) {
      if (rowIndex >= 0 && rowIndex < this.finalEffectList.length) {
        const event = {
          action: 'remove',
          fieldName: 'renderingsAttachment',
          file: file,
          fileList: fileList
        }
        this.handleFileListChange(event, this.finalEffectList[rowIndex])
      }
    },

    // 处理行的最终效果图文件改变
    handleChangeFinalEffectFile(rowIndex, file, fileList) {
      if (rowIndex >= 0 && rowIndex < this.finalEffectList.length) {
        const event = {
          action: file.status || 'change',
          fieldName: 'renderingsAttachment',
          file: file,
          fileList: fileList
        }
        this.handleFileListChange(event, this.finalEffectList[rowIndex])
      }
    },

    // 主卧附件
    handleRemoveMasterBedroomAttach(file, fileList) {
      const event = {
        action: 'remove',
        fieldName: 'masterBedroomAccessory',
        file: file,
        fileList: fileList,
        target: this.masterBedroomAttachList
      }
      this.handleFileListChange(event, this.form)
    },
    handleChangeMasterBedroomAttach(file, fileList) {
      const event = {
        action: file.status || 'change',
        fieldName: 'masterBedroomAccessory',
        file: file,
        fileList: fileList,
        target: this.masterBedroomAttachList
      }
      this.handleFileListChange(event, this.form)
    },

    // 次卧附件
    handleRemoveSecondaryBedroomAttach(file, fileList) {
      const event = {
        action: 'remove',
        fieldName: 'secondaryAccessory',
        file: file,
        fileList: fileList,
        target: this.secondaryBedroomAttachList
      }
      this.handleFileListChange(event, this.form)
    },
    handleChangeSecondaryBedroomAttach(file, fileList) {
      const event = {
        action: file.status || 'change',
        fieldName: 'secondaryAccessory',
        file: file,
        fileList: fileList,
        target: this.secondaryBedroomAttachList
      }
      this.handleFileListChange(event, this.form)
    },

    // 长辈房附件
    handleRemoveElderlyRoomAttach(file, fileList) {
      const event = {
        action: 'remove',
        fieldName: 'elderlyRoomAccessories',
        file: file,
        fileList: fileList,
        target: this.elderlyRoomAttachList
      }
      this.handleFileListChange(event, this.form)
    },
    handleChangeElderlyRoomAttach(file, fileList) {
      const event = {
        action: file.status || 'change',
        fieldName: 'elderlyRoomAccessories',
        file: file,
        fileList: fileList,
        target: this.elderlyRoomAttachList
      }
      this.handleFileListChange(event, this.form)
    },

    // 空间效果图附件
    handleRemoveSpaceRenderingsAttach(file, fileList) {
      const event = {
        action: 'remove',
        fieldName: 'spaceRenderingsAttachment',
        file: file,
        fileList: fileList,
        target: this.spaceRenderingsAttachList
      }
      this.handleFileListChange(event, this.form)
    },
    handleChangeSpaceRenderingsAttach(file, fileList) {
      const event = {
        action: file.status || 'change',
        fieldName: 'spaceRenderingsAttachment',
        file: file,
        fileList: fileList,
        target: this.spaceRenderingsAttachList
      }
      this.handleFileListChange(event, this.form)
    },

    // 处理子表单的文件变更
    handleSubformFileChange(event, row) {
      if (!event || !event.fieldName || !row) {
        return false
      }

      const fieldName = event.fieldName
      const files = event.files || []

      console.log(`子表单文件 ${fieldName} 更新:`, files)

      // 将文件数组转换为JSON字符串存储
      row[fieldName] = JSON.stringify(files)

      return true
    },
    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 158px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1)
}
</style>

