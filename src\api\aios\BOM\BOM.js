import request from '@/utils/request'

// 硬装
export function getHardSetupBOMDTO(params) {
  // 确保参数安全
  return request({
    url: 'api/BOM/getHardSetupBOMDTO',
    method: 'get',
    params
  })
}
export function deleteHardSetupBOMDTO(id) {
  // 确保参数安全
  return request({
    url: `api/BOM/deleteHardBOM/${id}`,
    method: 'delete'
  })
}

// 软装BOM
export function getSoftSetupBOMDTO(params) {
  return request({
    url: 'api/BOM/getSoftSetupBOMDTO',
    method: 'get',
    params
  })
}
export function deleteSoftSetupBOMDTO(id) {
  return request({
    url: `api/BOM/deleteSoftBOM/${id}`,
    method: 'delete'
  })
}

// 固装
export function getSolidSetupBOMDTO(params) {
  return request({
    url: 'api/BOM/getSolidSetupBOMDTO',
    method: 'get',
    params
  })
}
export function deleteSolidSetupBOMDTO(id) {
  return request({
    url: `api/BOM/deleteSolidBOM/${id}`,
    method: 'delete'
  })
}

export function importExcel(file, type, generalSetupBOM) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)

  // 将form对象转换为JSON字符串再添加
  formData.append('generalSetupBOM', JSON.stringify(generalSetupBOM))

  formData.forEach((value, key) => {
    console.log(key, value)
  })

  return request({
    url: 'api/BOM/excel/import',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 添加以下配置，阻止axios自动转换数据格式
    transformRequest: [function(data) {
      return data
    }]
  })
}

export function downloadExcel(data, type) {
  return request({
    url: 'api/BOM/excel/export',
    method: 'post',
    data: {
      ...data[0], // 展开选中的项目数据
      type: type // 添加类型标识
    },
    responseType: 'blob'
  })
}

export function changeImage(file, id, mid, type) {
  const formData = new FormData()
  formData.append('file', file)
  formData.append('type', type)
  formData.append('id', id)
  formData.append('mid', mid)

  // formData.forEach((value, key) => {
  //   console.log(key, value)
  // })

  return request({
    url: 'api/BOM/updateImg',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 添加以下配置，阻止axios自动转换数据格式
    transformRequest: [function(data) {
      return data
    }]
  })
}
