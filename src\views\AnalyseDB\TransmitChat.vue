<template>
  <el-dialog
    :visible.sync="visible"
    width="520px"
    :before-close="handleClose"
    :modal="true"
    :close-on-click-modal="true"
    :close-on-press-escape="true"
    :append-to-body="true"
    custom-class="transmit-dialog-wrapper"
  >
    <div slot="title" class="dialog-title">
      <div class="title-icon">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
          <path
            d="M4 12v8a2 2 0 002 2h12a2 2 0 002-2v-8M16 6l-4-4-4 4M12 2v13"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
        </svg>
      </div>
      <span>转发聊天记录</span>
    </div>
    <div class="transmit-chat-container">
      <!-- 转发说明 -->
      <div class="info-section">
        <div class="info-card">
          <div class="info-icon">
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
              <circle cx="12" cy="12" r="10" stroke="currentColor" stroke-width="2" />
              <path d="M12 16v-4M12 8h.01" stroke="currentColor" stroke-width="2" stroke-linecap="round" />
            </svg>
          </div>
          <div class="info-content">
            <div class="info-title">准备转发聊天记录</div>
            <div class="info-desc">将转发当前所有聊天记录（共 {{ messages.length }} 条）</div>
          </div>
        </div>
      </div>

      <!-- 转发方式选择 -->
      <div class="method-section">
        <div class="method-title">选择转发方式</div>
        <div class="method-buttons">
          <button
            class="method-btn copy-btn"
            :disabled="copying"
            @click="copyToClipboard"
          >
            <div class="btn-icon">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <rect x="9" y="9" width="13" height="13" rx="2" ry="2" stroke="currentColor" stroke-width="2" />
                <path d="M5 15H4a2 2 0 01-2-2V4a2 2 0 012-2h9a2 2 0 012 2v1" stroke="currentColor" stroke-width="2" />
              </svg>
            </div>
            <div class="btn-content">
              <div class="btn-title">复制到剪贴板</div>
              <div class="btn-desc">快速复制文本内容</div>
            </div>
            <div v-if="copying" class="btn-loading">
              <i class="el-icon-loading" />
            </div>
          </button>

          <button
            class="method-btn download-btn"
            :disabled="downloading"
            @click="downloadAsFile"
          >
            <div class="btn-icon">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4M7 10l5 5 5-5M12 15V3" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div class="btn-content">
              <div class="btn-title">下载为文件</div>
              <div class="btn-desc">保存为 .txt 文件</div>
            </div>
            <div v-if="downloading" class="btn-loading">
              <i class="el-icon-loading" />
            </div>
          </button>

          <button
            class="method-btn pdf-btn"
            :disabled="exportingPdf"
            @click="exportToPdf"
          >
            <div class="btn-icon">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none">
                <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <polyline points="14,2 14,8 20,8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <line x1="16" y1="13" x2="8" y2="13" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <line x1="16" y1="17" x2="8" y2="17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                <polyline points="10,9 9,9 8,9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
              </svg>
            </div>
            <div class="btn-content">
              <div class="btn-title">导出为PDF</div>
              <div class="btn-desc">保存为 .pdf 文件</div>
            </div>
            <div v-if="exportingPdf" class="btn-loading">
              <i class="el-icon-loading" />
            </div>
          </button>
        </div>
      </div>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button size="medium" @click="handleClose">取消</el-button>
      <el-button type="primary" size="medium" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import JsPDF from 'jspdf'
import html2canvas from 'html2canvas'

export default {
  name: 'TransmitChat',

  props: {
    visible: {
      type: Boolean,
      default: false
    },
    messages: {
      type: Array,
      default: () => []
    }
  },

  data() {
    return {
      copying: false,
      downloading: false,
      exportingPdf: false
    }
  },

  computed: {
    // 格式化后的内容
    formattedContent() {
      return this.formatMessages(this.messages)
    }
  },

  methods: {
    // 格式化消息为聊天格式
    formatMessages(messages) {
      if (!messages || messages.length === 0) {
        return '暂无聊天记录'
      }

      const lines = []
      lines.push('=== AI聊天记录 ===')
      lines.push(`导出时间: ${new Date().toLocaleString()}`)
      lines.push(`共 ${messages.length} 条对话`)
      lines.push('')

      messages.forEach((message) => {
        const sender = message.type === 'user' ? '用户' : 'AI助手'
        const time = message.timestamp ? new Date(message.timestamp).toLocaleString() : ''

        lines.push(`${sender} ${time}`)

        if (message.image) {
          lines.push('[图片内容]')
        } else if (message.chartUrl || (message.chartUrls && message.chartUrls.length > 0)) {
          lines.push('[图表内容]')
        } else if (message.text) {
          // 移除HTML标签，保留纯文本
          const plainText = message.text.replace(/<[^>]*>/g, '').trim()
          lines.push(plainText)
        }
        lines.push('')
      })

      return lines.join('\n')
    },

    // 复制到剪贴板
    async copyToClipboard() {
      this.copying = true
      try {
        await navigator.clipboard.writeText(this.formattedContent)
        this.$message.success('已复制到剪贴板')
        this.handleClose()
      } catch (error) {
        console.error('复制失败:', error)
        this.$message.error('复制失败，请手动复制')
      } finally {
        this.copying = false
      }
    },

    // 下载为文件
    downloadAsFile() {
      this.downloading = true
      try {
        const content = this.formattedContent
        const filename = `AI聊天记录_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`

        const blob = new Blob([content], { type: 'text/plain' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = filename
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        URL.revokeObjectURL(url)

        this.$message.success('文件下载成功')
        this.handleClose()
      } catch (error) {
        console.error('下载失败:', error)
        this.$message.error('下载失败')
      } finally {
        this.downloading = false
      }
    },

    // 导出为PDF（优化版本，支持中文）
    exportToPdf() {
      this.exportingPdf = true
      try {
        this.exportChinesePdf()
      } catch (error) {
        console.error('PDF导出失败:', error)
        this.$message.error('PDF导出失败，请重试')
      } finally {
        this.exportingPdf = false
      }
    },

    // 导出中文PDF（优化版本，直接下载PDF文件）
    async exportChinesePdf() {
      try {
        // 创建一个隐藏的div来渲染聊天内容
        const chatContainer = document.createElement('div')
        chatContainer.style.cssText = `
          position: absolute;
          left: -9999px;
          top: -9999px;
          width: 700px;
          background: #ffffff;
          padding: 25px;
          font-family: 'Microsoft YaHei', Arial, sans-serif;
          font-size: 14px;
          line-height: 1.6;
          color: #000000;
          box-sizing: border-box;
        `

        // 添加CSS样式到容器
        const style = document.createElement('style')
        style.textContent = `
          .chat-export-container p { margin: 8px 0; }
          .chat-export-container ul, .chat-export-container ol { margin: 8px 0; padding-left: 20px; }
          .chat-export-container li { margin: 4px 0; }
          .chat-export-container h1, .chat-export-container h2, .chat-export-container h3,
          .chat-export-container h4, .chat-export-container h5, .chat-export-container h6 {
            margin: 12px 0 8px 0; font-weight: bold;
          }
          .chat-export-container strong, .chat-export-container b { font-weight: bold; }
          .chat-export-container br { line-height: 1.6; }
          .chat-export-container div { margin: 4px 0; }
        `
        chatContainer.appendChild(style)
        chatContainer.className = 'chat-export-container'

        // 构建简化的HTML内容
        let htmlContent = `
          <div style="text-align: center; margin-bottom: 25px; border-bottom: 2px solid #ddd; padding-bottom: 20px;">
            <h2 style="margin: 0 0 10px 0; color: #333; font-size: 20px; font-weight: bold;">AI聊天记录</h2>
            <p style="margin: 0; color: #666; font-size: 12px;">导出时间: ${new Date().toLocaleString()}</p>
            <p style="margin: 0; color: #666; font-size: 12px;">共 ${this.messages.length} 条对话</p>
          </div>
        `

        // 添加消息内容（简化版本）
        this.messages.forEach((message, index) => {
          const sender = message.type === 'user' ? '用户' : 'AI助手'
          const time = message.timestamp ? new Date(message.timestamp).toLocaleString() : ''
          const senderColor = message.type === 'user' ? '#2563eb' : '#059669'
          let content = ''
          if (message.image) {
            content = '[图片内容]'
          } else if (message.chartUrl || (message.chartUrls && message.chartUrls.length > 0)) {
            content = '[图表内容]'
          } else if (message.text) {
            // 直接使用HTML内容，保留所有格式
            content = message.text
              .replace(/&nbsp;/g, ' ')
              .replace(/&lt;/g, '<')
              .replace(/&gt;/g, '>')
              .replace(/&amp;/g, '&')
          }

          // 构建消息内容，包含图片
          let messageContent = content

          // 如果有图片，添加图片标签
          if (message.image) {
            messageContent += `<br><img src="${message.image}" style="max-width: 100%; height: auto; margin-top: 8px; border: 1px solid #ddd;">`
          }

          // 如果有图表，添加图表
          if (message.chartUrl) {
            messageContent += `<br><img src="${message.chartUrl}" style="max-width: 100%; height: auto; margin-top: 8px; border: 1px solid #ddd;">`
          } else if (message.chartUrls && message.chartUrls.length > 0) {
            message.chartUrls.forEach(chartUrl => {
              messageContent += `<br><img src="${chartUrl}" style="max-width: 100%; height: auto; margin-top: 8px; border: 1px solid #ddd;">`
            })
          }

          htmlContent += `
            <div style="margin-bottom: 18px; padding: 10px; border-left: 4px solid ${senderColor}; background: #f9f9f9;">
              <div style="margin-bottom: 6px; font-weight: bold; color: ${senderColor}; font-size: 13px;">
                ${index + 1}. ${sender}
                ${time ? `<span style="float: right; font-weight: normal; color: #999; font-size: 10px;">${time}</span>` : ''}
              </div>
              <div style="color: #333; font-size: 12px; word-wrap: break-word; clear: both; line-height: 1.6;">
                ${messageContent}
              </div>
            </div>
          `
        })

        chatContainer.innerHTML = htmlContent
        document.body.appendChild(chatContainer)

        // 等待字体和图片加载完成
        await new Promise(resolve => setTimeout(resolve, 500))

        // 使用html2canvas生成图片，支持图片和完整内容
        const canvas = await html2canvas(chatContainer, {
          scale: 1.2,
          useCORS: true,
          allowTaint: true, // 允许跨域图片
          backgroundColor: '#ffffff',
          logging: false,
          letterRendering: true,
          imageTimeout: 15000, // 增加图片加载超时时间
          onclone: (clonedDoc) => {
            // 确保克隆的文档中字体和样式正确
            const clonedContainer = clonedDoc.querySelector('div')
            if (clonedContainer) {
              clonedContainer.style.fontFamily = 'Arial, sans-serif'
              clonedContainer.style.color = '#000000'

              // 确保图片正确显示
              const images = clonedContainer.querySelectorAll('img')
              images.forEach(img => {
                img.style.maxWidth = '100%'
                img.style.height = 'auto'
              })
            }
          }
        })

        // 移除临时元素
        document.body.removeChild(chatContainer)

        // 检查canvas是否正确生成
        if (canvas.width === 0 || canvas.height === 0) {
          throw new Error('Canvas生成失败')
        }

        // 创建PDF
        const pdf = new JsPDF('p', 'mm', 'a4')
        const imgData = canvas.toDataURL('image/png')

        // 检查图片数据是否有效
        if (!imgData || imgData === 'data:,') {
          throw new Error('图片数据生成失败')
        }

        const pdfWidth = pdf.internal.pageSize.getWidth()
        const pdfHeight = pdf.internal.pageSize.getHeight()
        const imgWidth = pdfWidth - 20
        const imgHeight = (canvas.height * imgWidth) / canvas.width

        let heightLeft = imgHeight
        let position = 10

        // 添加第一页
        pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight)
        heightLeft -= pdfHeight - 20

        // 如果内容超过一页，添加更多页面
        while (heightLeft >= 0) {
          position = heightLeft - imgHeight + 10
          pdf.addPage()
          pdf.addImage(imgData, 'PNG', 10, position, imgWidth, imgHeight)
          heightLeft -= pdfHeight - 20
        }

        // 生成文件名并保存
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const hour = String(now.getHours()).padStart(2, '0')
        const minute = String(now.getMinutes()).padStart(2, '0')
        const second = String(now.getSeconds()).padStart(2, '0')
        const filename = `AI聊天记录_${year}-${month}-${day}_${hour}-${minute}-${second}.pdf`

        pdf.save(filename)

        this.$message.success('PDF文件已下载成功')
        this.handleClose()
      } catch (error) {
        console.error('PDF导出失败:', error)
        // 如果html2canvas失败，使用备选的纯文本方案
        this.exportSimpleTextPdf()
      }
    },

    // 备选方案：纯文本PDF导出
    exportSimpleTextPdf() {
      try {
        const pdf = new JsPDF('p', 'mm', 'a4')
        const margin = 15
        const pageWidth = pdf.internal.pageSize.getWidth()
        const pageHeight = pdf.internal.pageSize.getHeight()
        const maxWidth = pageWidth - 2 * margin
        let currentY = margin

        // 设置字体
        pdf.setFont('helvetica')

        // 添加标题
        pdf.setFontSize(18)
        pdf.setTextColor(51, 51, 51)
        const title = 'AI Chat Records'
        const titleWidth = pdf.getTextWidth(title)
        pdf.text(title, (pageWidth - titleWidth) / 2, currentY)
        currentY += 12

        // 添加导出信息
        pdf.setFontSize(10)
        pdf.setTextColor(102, 102, 102)
        const exportTime = `Export Time: ${new Date().toLocaleString()}`
        const messageCount = `Total Messages: ${this.messages.length}`

        pdf.text(exportTime, margin, currentY)
        currentY += 6
        pdf.text(messageCount, margin, currentY)
        currentY += 15

        // 添加分隔线
        pdf.setDrawColor(200, 200, 200)
        pdf.line(margin, currentY, pageWidth - margin, currentY)
        currentY += 10

        // 处理每条消息
        this.messages.forEach((message, index) => {
          const sender = message.type === 'user' ? 'User' : 'AI Assistant'
          const time = message.timestamp ? new Date(message.timestamp).toLocaleString() : ''

          // 检查是否需要新页面
          if (currentY > pageHeight - 40) {
            pdf.addPage()
            currentY = margin
          }

          // 消息序号和发送者
          pdf.setFontSize(12)
          pdf.setTextColor(message.type === 'user' ? 37 : 5, message.type === 'user' ? 99 : 150, message.type === 'user' ? 235 : 105)
          const senderText = `${index + 1}. ${sender}`
          pdf.text(senderText, margin, currentY)

          // 时间戳
          if (time) {
            pdf.setFontSize(8)
            pdf.setTextColor(128, 128, 128)
            const timeWidth = pdf.getTextWidth(time)
            pdf.text(time, pageWidth - margin - timeWidth, currentY)
          }

          currentY += 8

          // 消息内容
          pdf.setFontSize(10)
          pdf.setTextColor(51, 51, 51)

          let content = this.extractMessageContent(message)
          if (content) {
            // 添加图片标识
            if (message.image) {
              content += '\n[Image attached]'
            }
            if (message.chartUrl || (message.chartUrls && message.chartUrls.length > 0)) {
              content += '\n[Chart attached]'
            }

            // 不再限制内容长度，保留完整内容
            const lines = pdf.splitTextToSize(content, maxWidth - 5)
            lines.forEach(line => {
              if (currentY > pageHeight - 20) {
                pdf.addPage()
                currentY = margin
              }
              pdf.text(line, margin + 3, currentY)
              currentY += 5
            })
          }

          currentY += 8
        })

        // 生成文件名并保存
        const now = new Date()
        const year = now.getFullYear()
        const month = String(now.getMonth() + 1).padStart(2, '0')
        const day = String(now.getDate()).padStart(2, '0')
        const hour = String(now.getHours()).padStart(2, '0')
        const minute = String(now.getMinutes()).padStart(2, '0')
        const second = String(now.getSeconds()).padStart(2, '0')
        const filename = `AI聊天记录_${year}-${month}-${day}_${hour}-${minute}-${second}.pdf`

        pdf.save(filename)
        this.$message.success('PDF文件已下载成功（文本版本）')
        this.handleClose()
      } catch (error) {
        console.error('备选PDF导出也失败:', error)
        this.$message.error('PDF导出失败，请重试')
      }
    },

    // 提取消息内容
    extractMessageContent(message) {
      if (message.image) {
        return '[图片内容]'
      } else if (message.chartUrl || (message.chartUrls && message.chartUrls.length > 0)) {
        return '[图表内容]'
      } else if (message.text) {
        return message.text
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
          .replace(/<br\s*\/?>/gi, '\n')
          .replace(/<\/p>/gi, '\n\n')
          .replace(/<[^>]*>/g, '')
          .replace(/&nbsp;/g, ' ')
          .replace(/&lt;/g, '<')
          .replace(/&gt;/g, '>')
          .replace(/&amp;/g, '&')
          .trim()
      }
      return ''
    },

    // 生成文件名
    generateFilename() {
      const now = new Date()
      const year = now.getFullYear()
      const month = String(now.getMonth() + 1).padStart(2, '0')
      const day = String(now.getDate()).padStart(2, '0')
      const hour = String(now.getHours()).padStart(2, '0')
      const minute = String(now.getMinutes()).padStart(2, '0')
      const second = String(now.getSeconds()).padStart(2, '0')
      return `聊天记录_${year}-${month}-${day}_${hour}-${minute}-${second}.pdf`
    },

    // 确认转发
    handleConfirm() {
      if (this.messages.length === 0) {
        this.$message.warning('暂无聊天记录')
        return
      }
      // 默认复制到剪贴板
      this.copyToClipboard()
    },

    // 关闭对话框
    handleClose() {
      this.$emit('update:visible', false)
    }
  }
}
</script>

<style scoped>
/* 对话框标题样式 */
.dialog-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
}

.title-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  color: white;
}

/* 容器样式 */
.transmit-chat-container {
  padding: 4px 0;
}

/* 信息卡片 */
.info-section {
  margin-bottom: 24px;
}

.info-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
  border-radius: 12px;
  border: 1px solid #e1f5fe;
}

.info-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: #3b82f6;
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 15px;
  font-weight: 600;
  color: #1e40af;
  margin-bottom: 4px;
}

.info-desc {
  font-size: 13px;
  color: #3730a3;
  opacity: 0.8;
}

/* 方法选择区域 */
.method-section {
  margin-bottom: 8px;
}

.method-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 16px;
  text-align: center;
}

.method-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* 方法按钮 */
.method-btn {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px 20px;
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.method-btn:hover {
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
  transform: translateY(-1px);
}

.method-btn:active {
  transform: translateY(0);
}

.method-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.copy-btn:hover {
  border-color: #3b82f6;
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
}

.download-btn:hover {
  border-color: #10b981;
  background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
}

.pdf-btn:hover {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
}

.btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: #f8fafc;
  border-radius: 10px;
  color: #64748b;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.copy-btn:hover .btn-icon {
  background: #3b82f6;
  color: white;
}

.download-btn:hover .btn-icon {
  background: #10b981;
  color: white;
}

.pdf-btn:hover .btn-icon {
  background: #f59e0b;
  color: white;
}

.btn-content {
  flex: 1;
  text-align: left;
}

.btn-title {
  font-size: 15px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.btn-desc {
  font-size: 13px;
  color: #6b7280;
}

.btn-loading {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #3b82f6;
  font-size: 16px;
}

/* 底部按钮 */
.dialog-footer {
  text-align: right;
  padding-top: 8px;
}

.transmit-dialog-wrapper .el-dialog__header {
  padding: 20px 24px 16px;
  border-bottom: 1px solid #f1f5f9;
}

.transmit-dialog-wrapper .el-dialog__body {
  padding: 20px 24px;
}

.transmit-dialog-wrapper .el-dialog__footer {
  padding: 16px 24px 20px;
  border-top: 1px solid #f1f5f9;
}

.transmit-dialog-wrapper .el-dialog {
  border-radius: 16px;
  overflow: hidden;
}
</style>
