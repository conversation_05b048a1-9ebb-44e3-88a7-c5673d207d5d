import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/tumaiSj<PERSON>an',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/tumaiSj<PERSON>/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/tumaiSjK<PERSON>an',
    method: 'put',
    data
  })
}

export default { add, edit, del }
