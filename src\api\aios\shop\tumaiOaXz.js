import request from '@/utils/request'

// 根据字段名获取选项列表
export function getOptionsByFieldName(fieldName) {
  return request({
    url: `/api/tumaiOaXz/options/${fieldName}`,
    method: 'get'
  })
}

// 批量添加选项
export function batchAddOptions(data) {
  return request({
    url: '/api/tumaiOaXz/batch',
    method: 'post',
    data
  })
}

// 删除选项 - 正确处理特殊字符
export function deleteOption(fieldName, optionValue) {
  // 确保参数都被正确编码
  const encodedFieldName = encodeURIComponent(fieldName)
  const encodedOptionValue = encodeURIComponent(optionValue)

  // 记录调试信息
  console.log(`删除选项: fieldName=${fieldName}, optionValue=${optionValue}`)
  console.log(`编码后: encodedFieldName=${encodedFieldName}, encodedOptionValue=${encodedOptionValue}`)

  // 使用编码后的参数构建URL
  return request({
    url: `/api/tumaiOaXz/${encodedFieldName}/${encodedOptionValue}`,
    method: 'delete'
  })
}
