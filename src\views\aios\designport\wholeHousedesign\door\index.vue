<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.makeDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-form-item label="项目概况">
              <el-input v-model="form.projectOverview" :rows="3" type="textarea" />
            </el-form-item>

            <!-- 房门测量子表单 -->
            <el-divider content-position="left"><i class="el-icon-document" /> 房门测量信息</el-divider>
            <div class="table-container">
              <el-table :data="doorMeasurementList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="类别" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.category" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.category_fangmen"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="所属空间名称" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.spatialName" size="mini" placeholder="请输入空间名称" />
                  </template>
                </el-table-column>
                <el-table-column label="长" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.length" size="mini" placeholder="长度" />
                  </template>
                </el-table-column>
                <el-table-column label="宽" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.width" size="mini" placeholder="宽度" />
                  </template>
                </el-table-column>
                <el-table-column label="高" width="80">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.height" size="mini" placeholder="高度" />
                  </template>
                </el-table-column>
                <el-table-column label="开启方向" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.openOrientation" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.open_orientation"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="总层高" width="120">
                  <template slot-scope="scope">
                    <el-input
                      v-model.number="scope.row.totalFloorHeight"
                      size="mini"
                      type="number"
                      required
                      placeholder="请输入总层高"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="复尺人" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.thecompoundFootPeople" size="mini" placeholder="请输入复尺人" />
                  </template>
                </el-table-column>
                <el-table-column label="复尺次数" width="120">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.multipldTimes" size="mini" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.multipld_times"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="现场复尺日期" width="180">
                  <template slot-scope="scope">
                    <el-date-picker
                      v-model="scope.row.sitecompoundSizeDate"
                      type="datetime"
                      size="mini"
                      placeholder="选择日期"
                      style="width: 100%"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="墙垛数据" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.wallData" size="mini" type="textarea" :rows="2" placeholder="请输入墙垛数据" />
                  </template>
                </el-table-column>
                <el-table-column label="现场施工需配合内容" width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.contentOfAssistance" size="mini" type="textarea" :rows="2" placeholder="请输入内容" />
                  </template>
                </el-table-column>
                <el-table-column label="基层情况" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.baseCircumstances" size="mini" type="textarea" :rows="2" placeholder="请输入基层情况" />
                  </template>
                </el-table-column>
                <el-table-column label="附件" width="180">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.attachment"
                      :field-name="'attachment'"
                      v-bind="fileUploadConfig"
                      :use-minio-delete="true"
                      :hide-remove="isViewMode"
                      @change="handleFileChange('attachment', $event, scope.row)"
                      @file-change="handleFileListChange"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveDoorMeasurement(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDoorMeasurement">新增</el-button>
              </div>
            </div>

            <!-- 测量人员信息子表单 -->
            <el-divider content-position="left"><i class="el-icon-user" /> 测量人员信息</el-divider>
            <div class="table-container">
              <el-table :data="measurementPersonnelList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="姓名" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.name" size="mini" placeholder="请输入姓名" />
                  </template>
                </el-table-column>
                <el-table-column label="职位" width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.position" size="mini" placeholder="请输入职位" />
                  </template>
                </el-table-column>
                <el-table-column label="职责" min-width="180">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.responsibility" size="mini" type="textarea" :rows="2" placeholder="请输入职责" />
                  </template>
                </el-table-column>
                <el-table-column label="联系方式" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.contactInformation" size="mini" placeholder="请输入联系方式" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveMeasurementPersonnel(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddMeasurementPersonnel">新增</el-button>
              </div>
            </div>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectNumber" label="项目编号" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiSjFangmen:edit','tumaiSjFangmen:del','tumaiSjFangmen:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudTumaiSjFangmen from '@/api/aios/designport/wholeHousedesign/tumaiSjFangmen'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, projectNumber: null, projectName: null, projectAddr: null, projectNature: null, projectOverview: null, category: null, spatialName: null, length: null, width: null, height: null, contentOfAssistance: null, notes: null, name: null, position: null, responsibility: null, contactInformation: null, makeDate: null, uid: null, typeOfService: null, projectAddress: null, projectid: null, projectDeliveryPeriod: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, createtime: null, isturn: null, doorMeasurementData: '[]', measurementPersonnelData: '[]' }
export default {
  name: 'TumaiSjFangmen',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'category_fangmen', 'multipldTimes', 'open_orientation', 'multipld_times'],
  cruds() {
    return CRUD({ title: '房门测量', url: 'api/tumaiSjFangmen', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjFangmen }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjFangmen:add'],
        edit: ['admin', 'tumaiSjFangmen:edit'],
        del: ['admin', 'tumaiSjFangmen:del'],
        view: ['admin', 'tumaiSjFangmen:edit', 'tumaiSjFangmen:view']
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 房门测量子表单数据
      doorMeasurementList: [],
      // 测量人员子表单数据
      measurementPersonnelList: [],
      // 图片预览
      dialogImageUrl: '',
      dialogVisible: false,
      // 文件字段列表
      fileFields: ['attachment'],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 5,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      // 初始化房门测量列表
      this.initDoorMeasurementList(form)
      // 初始化测量人员列表
      this.initMeasurementPersonnelList(form)
      // 初始化子表单中的文件字段 (使用新工具)
      this.initSubformFileFields(this.doorMeasurementList, 'attachment')
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteSubformFiles(data, 'doorMeasurementData', 'attachment')
      return true
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initDoorMeasurementList(form)
      this.initMeasurementPersonnelList(form)
      this.initSubformFileFields(this.doorMeasurementList, 'attachment')
      this.initGeneralFileFields(this.fileFields)
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      // 清空列表
      this.doorMeasurementList = []
      this.measurementPersonnelList = []

      // 添加空记录
      this.handleAddDoorMeasurement()
      this.handleAddMeasurementPersonnel()

      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      try {
        const errors = this.validateMeasurementList()
        if (errors.length > 0) {
          errors.forEach(msg => this.$message.error(msg))
          return false
        }

        console.log('提交前原始子表单数据:', JSON.stringify(this.doorMeasurementList))

        // 验证总层高字段
        for (let i = 0; i < this.doorMeasurementList.length; i++) {
          if (!this.doorMeasurementList[i].totalFloorHeight) {
            this.$message.error(`请输入第${i + 1}行的总层高`)
            return false
          }
        }

        // 处理子表单中的文件字段
        this.prepareSubformFileFields(this.doorMeasurementList, 'attachment')

        // 处理通用文件字段
        this.prepareGeneralFileFields(this.fileFields)

        // 处理子表单中的日期字段 - 转换为时间戳
        this.doorMeasurementList.forEach(item => {
          // 处理现场复尺日期
          if (item.sitecompoundSizeDate) {
            if (typeof item.sitecompoundSizeDate === 'string') {
              // 如果是ISO字符串
              item.sitecompoundSizeDate = new Date(item.sitecompoundSizeDate).getTime()
            } else if (item.sitecompoundSizeDate instanceof Date) {
              // 如果是Date对象
              item.sitecompoundSizeDate = item.sitecompoundSizeDate.getTime()
            }
          }
        })

        // 处理房门测量列表数据
        crud.form.doorMeasurementData = JSON.stringify(this.doorMeasurementList)

        // 处理测量人员列表数据
        crud.form.measurementPersonnelData = JSON.stringify(this.measurementPersonnelList)

        console.log('处理后的表单数据:', crud.form.doorMeasurementData)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化房门测量列表
    initDoorMeasurementList(form) {
      try {
        this.doorMeasurementList = form.doorMeasurementData ? JSON.parse(form.doorMeasurementData) : []
        if (!Array.isArray(this.doorMeasurementList)) {
          this.doorMeasurementList = []
        }

        // 确保每行都有附件字段
        this.doorMeasurementList.forEach(item => {
          if (!item.attachment) {
            item.attachment = '[]'
          }
        })
      } catch (e) {
        console.error('解析房门测量数据失败:', e)
        this.doorMeasurementList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.doorMeasurementList.length === 0) {
        this.handleAddDoorMeasurement()
      }
    },

    // 初始化测量人员列表
    initMeasurementPersonnelList(form) {
      try {
        this.measurementPersonnelList = form.measurementPersonnelData ? JSON.parse(form.measurementPersonnelData) : []
        if (!Array.isArray(this.measurementPersonnelList)) {
          this.measurementPersonnelList = []
        }
      } catch (e) {
        console.error('解析测量人员数据失败:', e)
        this.measurementPersonnelList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.measurementPersonnelList.length === 0) {
        this.handleAddMeasurementPersonnel()
      }
    },

    // 添加房门测量记录
    handleAddDoorMeasurement() {
      this.doorMeasurementList.push({
        category: '',
        spatialName: '',
        length: '',
        width: '',
        height: '',
        contentOfAssistance: '',
        multipldTimes: '',
        thecompoundFootPeople: '',
        sitecompoundSizeDate: '',
        baseCircumstances: '',
        attachment: '[]', // 初始化为空数组JSON字符串
        openOrientation: '',
        wallData: '',
        totalFloorHeight: ''
      })
    },

    // 移除房门测量记录
    handleRemoveDoorMeasurement(index) {
      // 获取要删除的行数据
      const rowData = this.doorMeasurementList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        deleteSubformRowFiles(rowData, 'attachment')
      }
      this.doorMeasurementList.splice(index, 1)
      if (this.doorMeasurementList.length === 0) {
        this.handleAddDoorMeasurement()
      }
    },

    // 添加测量人员记录
    handleAddMeasurementPersonnel() {
      this.measurementPersonnelList.push({
        name: '',
        position: '',
        contactInformation: '',
        responsibility: ''
      })
    },

    // 移除测量人员记录
    handleRemoveMeasurementPersonnel(index) {
      this.measurementPersonnelList.splice(index, 1)
      if (this.measurementPersonnelList.length === 0) {
        this.handleAddMeasurementPersonnel()
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, row || this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files, row) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        if (row) {
          // 子表单的处理
          row[fieldName] = jsonStr
          console.log(`子表单字段${fieldName}更新为:`, row[fieldName])
        } else {
          // 主表单的处理
          this.form[fieldName] = jsonStr
          console.log(`主表单字段${fieldName}更新为:`, this.form[fieldName])
        }
      }
    },
    validateMeasurementList() {
      const errors = []
      this.doorMeasurementList.forEach((item, index) => {
        if (!item.multipldTimes) {
          errors.push(`第 ${index + 1} 行缺少复尺次数`)
        }
      })

      return errors
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.small-upload-container {
  width: 100%;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}
</style>
