<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :title="crud.status.title" width="1000px" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-document" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目名称" prop="projectName">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼盘名称">
                  <el-input v-model="form.buildingName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <!-- <el-col :span="12">
                <el-form-item label="户型名称">
                  <el-input v-model="form.huxingName" />
                </el-form-item>
              </el-col> -->
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="归属户型">
                  <el-select v-model="form.huxingStructure" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.huxing_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="归属户型空间数量">
                  <el-input v-model="form.spaceQuantity" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 空间信息子表单 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 空间信息</el-divider>
            <div class="table-container">
              <el-table :data="spaceInfoList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="50" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="空间面积" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.spaceArea" size="mini">
                      <template slot="append">㎡</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="空间编号" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.spaceNumber" size="mini" />
                  </template>
                </el-table-column>
                <el-table-column label="长（米）" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.longValue" size="mini">
                      <template slot="append">m</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="宽（米）" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.wide" size="mini">
                      <template slot="append">m</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="高（米）" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.tall" size="mini">
                      <template slot="append">m</template>
                    </el-input>
                  </template>
                </el-table-column>
                <el-table-column label="归属空间" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.ownershipSpace" size="mini" />
                  </template>
                </el-table-column>
                <el-table-column label="归属空间数量" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.ownershipSpaceNumber" size="mini" />
                  </template>
                </el-table-column>
                <el-table-column label="风格" width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.style" size="mini" />
                  </template>
                </el-table-column>
                <el-table-column label="备注" min-width="150">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.remarks" size="mini" type="textarea" :rows="2" />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center" fixed="right">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveSpaceInfo(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddSpaceInfo">新增</el-button>
              </div>
            </div>
          </div>

          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 附件上传</el-divider>
            <el-form-item label="户型图CAD附件（量过尺的）">
              <div class="image-upload-container">
                <general-file-upload
                  v-model="form.huxingCad"
                  :field-name="'huxingCad'"
                  v-bind="fileUploadConfig"
                  :use-minio-delete="true"
                  :hide-remove="isViewMode"
                  @change="handleFileChange('huxingCad', $event)"
                  @file-change="handleFileListChange"
                />
              </div>
            </el-form-item>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>

      <!-- 图片预览对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body title="图片预览" @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>

      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="huxingStructure" label="归属户型">
          <template slot-scope="scope">
            {{ dict.label.huxing_structure[scope.row.huxingStructure] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="buildingName" label="楼盘名称" />
        <el-table-column v-if="checkPer(['admin','tumaiOaKongjian:edit','tumaiOaKongjian:del','tumaiOaKongjian:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaKongjian from '@/api/aios/shop/tumaiOaKongjian'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, nickName: null, shopid: null, familyName: null, oddNumbers: null, comid: null, entryName: null, itemNumber: null, uid: null, projectName: null, projectNumber: null, projectNature: null, projectid: null, spaceArea: null, huxingStructure: null, ownershipSpaceNumber: null, belongstoTheBuilding: null, tabDate: null, optdt: null, optid: null, optname: null, applydt: null, remarks: null, status: null, isturn: null, createtime: null, buildingName: null, longValue: null, wide: null, tall: null, style: null, ownershipSpace: null, spaceNumber: null, spaceInfoData: '[]', spaceQuantity: null, huxingName: null, huxingCad: '[]' }
export default {
  name: 'TumaiOaKongjian',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['huxing_structure', 'status'],
  cruds() {
    return CRUD({ title: '空间量尺', url: 'api/tumaiOaKongjian', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaKongjian }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaKongjian:add'],
        edit: ['admin', 'tumaiOaKongjian:edit'],
        del: ['admin', 'tumaiOaKongjian:del'],
        view: ['admin', 'tumaiOaKongjian:edit', 'tumaiOaKongjian:view']
      },
      rules: {
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        projectName: [
          { required: true, message: '项目名称不能为空', trigger: 'blur' }
        ],
        longValue: [
          { required: true, message: '长（米）不能为空', trigger: 'blur' }
        ],
        wide: [
          { required: true, message: '宽（米）不能为空', trigger: 'blur' }
        ],
        tall: [
          { required: true, message: '高（米）不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'projectNumber', display_name: '项目编号' }
      ],
      // 空间信息子表单数据
      spaceInfoList: [],
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 进度条
      uploadProgress: 0,
      // 文件列表
      huxingCadList: [],
      // 上传文件映射字段名
      fileFieldMap: {
        'huxingCad': '户型图CAD附件'
      },
      // 文件字段列表，用于初始化和提交前处理
      fileFields: [
        'huxingCad'
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar,.dwg,.dxf,.dwt,.dgn,.dws,.dwf',
        maxFiles: 10,
        tipText: '支持上传图片、CAD文件、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      this.initSpaceInfoList(form)
      // 初始化主表单附件字段
      this.initAttachmentFields(['huxingCad'])
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initSpaceInfoList(form)
      // 初始化主表单附件字段
      this.initAttachmentFields(['huxingCad'])
      // 初始化通用文件字段
      this.initGeneralFileFields(this.fileFields)
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      this.spaceInfoList = []
      this.handleAddSpaceInfo()
      // 初始化为空数组
      this.form.huxingCad = '[]'
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['huxingCad'])
      return true
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      if (this.spaceInfoList.length > 0) {
        // 如果有空间信息行，则取第一行的数据同步回表单
        const firstRow = this.spaceInfoList[0]
        crud.form.spaceArea = firstRow.spaceArea
        crud.form.spaceNumber = firstRow.spaceNumber
        crud.form.longValue = firstRow.longValue
        crud.form.wide = firstRow.wide
        crud.form.tall = firstRow.tall
        crud.form.ownershipSpace = firstRow.ownershipSpace
        crud.form.ownershipSpaceNumber = firstRow.ownershipSpaceNumber
        crud.form.style = firstRow.style
        crud.form.remarks = firstRow.remarks
      }

      // 将空间信息列表数据序列化存储
      crud.form.spaceInfoData = JSON.stringify(this.spaceInfoList)

      try {
        // 处理主表单附件字段
        this.prepareAttachmentFields(['huxingCad'])

        // 使用通用方法处理文件字段
        this.prepareGeneralFileFields(this.fileFields)

        console.log('处理附件后:', JSON.stringify({
          huxingCad: this.form.huxingCad
        }))

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化空间信息列表
    initSpaceInfoList(form) {
      // 首先检查是否有序列化存储的数据
      try {
        if (form.spaceInfoData && form.spaceInfoData !== '[]') {
          this.spaceInfoList = JSON.parse(form.spaceInfoData)
          if (!Array.isArray(this.spaceInfoList)) {
            this.createDefaultSpaceInfo(form)
          }
        } else {
          this.createDefaultSpaceInfo(form)
        }
      } catch (e) {
        console.error('解析空间信息数据失败:', e)
        this.createDefaultSpaceInfo(form)
      }
    },

    // 创建默认的空间信息行
    createDefaultSpaceInfo(form) {
      this.spaceInfoList = [{
        spaceArea: form.spaceArea,
        spaceNumber: form.spaceNumber,
        longValue: form.longValue,
        wide: form.wide,
        tall: form.tall,
        ownershipSpace: form.ownershipSpace,
        ownershipSpaceNumber: form.ownershipSpaceNumber,
        style: form.style,
        remarks: form.remarks
      }]
    },

    // 添加空间信息
    handleAddSpaceInfo() {
      this.spaceInfoList.push({
        spaceArea: null,
        spaceNumber: null,
        longValue: null,
        wide: null,
        tall: null,
        ownershipSpace: null,
        ownershipSpaceNumber: null,
        style: null,
        remarks: null
      })
    },

    // 移除空间信息
    handleRemoveSpaceInfo(index) {
      if (this.spaceInfoList.length <= 1) {
        this.$message.warning('至少保留一条记录')
        return
      }
      this.spaceInfoList.splice(index, 1)
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    },

    // 添加created钩子初始化文件字段
    created() {
      // 初始化文件字段
      this.initGeneralFileFields(this.fileFields)

      // 检查minioDeleteApi是否可用
      if (!this.minioDeleteApi) {
        console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
      }
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01);
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 5px;
  width: 100%;
  overflow-x: auto;
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input, .el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 调整新增按钮边距 */
.table-container .el-button {
  margin-top: 5px;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

/* 通用文件上传样式调整 */
.image-upload-container .general-file-upload {
  width: 100%;
}

.general-file-upload .upload-button {
  margin-bottom: 10px;
}

.general-file-upload .el-upload-list--text {
  max-height: 300px;
  overflow-y: auto;
}

.general-file-upload .el-upload-list__item {
  margin-top: 5px;
}
</style>
