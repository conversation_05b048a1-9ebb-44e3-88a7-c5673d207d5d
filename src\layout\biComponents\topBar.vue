<template>
  <div class="vis-nav">
    <div class="nav-topic">
      <div class="topic-tit">{{ top.titleName }}云数据可视化</div>
      <div class="topic-img"><img src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_decorate.png" alt=""></div>
    </div>

    <ul class="nav-route">

      <li
        v-for="(item, index) in top.navList"
        :key="index"
        :class="{ 'route-sel': item.active }"
        @click="handleNavClick(index)"
      >
        <a href="javascript:;">{{ item.name }}</a>
      </li>

    </ul>

    <div class="nav-time">{{ dynamicTime }}</div>
    <div class="triangle" @click="navigateToDashboard">&nbsp;</div>
  </div>
</template>

<script>
export default {
  props: {
    // 接收导航数据
    top: {
      type: Object,
      default: () => {}
    },
    // 接收动态时间
    dynamicTime: {
      type: String,
      default: '2024年11月14日 18:15:25'
    }
  },
  methods: {
    // 点击事件处理
    handleNavClick(index) {
      this.$emit('nav-change', index)
    },
    navigateToDashboard() {
      window.location.href = '/dashboard'
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
@import "@/assets/styles/bi.scss";

.triangle {
  width: 0;
  height: 0;
  border-left: 40px solid transparent;
  border-right: 40px solid transparent;
  border-bottom: 40px solid #2D4B68;
  position: absolute;
  top: -6px;
  right: -24px;
  cursor: pointer;
  transform: rotate(45deg);

  &::after {
    content: "点击返回后台主页";
    position: absolute;
    top: 70px; /* 调整提示框的位置 */
    right: -60px;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 5px;
    border-radius: 3px;
    white-space: nowrap;
    opacity: 0;
    transition: opacity 0.3s;
    transform: rotate(-45deg);
    pointer-events: none; /* 防止提示框阻止鼠标事件 */
  }

  &:hover::after {
    opacity: 1;
  }
}
</style>
