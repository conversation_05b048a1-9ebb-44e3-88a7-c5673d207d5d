<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">国别（国家）</label>
        <el-input v-model="query.country" clearable placeholder="国别（国家）" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">店铺名称</label>
        <el-input v-model="query.shopName" clearable placeholder="店铺名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">公司名称</label>
        <el-input v-model="query.companyName" clearable placeholder="公司名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">公司性质</label>
        <el-input v-model="query.companyNature" clearable placeholder="公司性质" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">公司地址</label>
        <el-input v-model="query.companyAddr" clearable placeholder="公司地址" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别（国家）">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="店铺名称">
            <el-input v-model="form.shopName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻日期">
            <el-date-picker v-model="form.enterDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司名称">
            <el-input v-model="form.companyName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司性质">
            <el-input v-model="form.companyNature" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司地址">
            <el-input v-model="form.companyAddr" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="邮箱">
            <el-input v-model="form.email" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品编号">
            <el-input v-model="form.goodsNo" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="form.goodsName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="图片">
            <el-input v-model="form.picture" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="型号">
            <el-input v-model="form.modelNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="品牌名称">
            <el-input v-model="form.brandName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="商品规格">
            <el-input v-model="form.specification" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="材质及参数">
            <el-input v-model="form.materParam" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="颜色">
            <el-input v-model="form.color" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="等级">
            <el-input v-model="form.level" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="产地">
            <el-input v-model="form.producing" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="单位">
            <el-input v-model="form.unit" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="报价数量">
            <el-input v-model="form.quotedNum" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="单价（未税）">
            <el-input v-model="form.unitNoTax" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="最小起订量">
            <el-input v-model="form.minOrder" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="最小库存数">
            <el-input v-model="form.minInventory" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="库存单位">
            <el-input v-model="form.inventoryUnit" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="供货周期（天）">
            <el-input v-model="form.supplyCycle" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别（国家）" />
        <el-table-column prop="shopName" label="店铺名称" />
        <el-table-column prop="enterDate" label="入驻日期" />
        <el-table-column prop="companyName" label="公司名称" />
        <el-table-column prop="companyNature" label="公司性质" />
        <el-table-column prop="companyAddr" label="公司地址" />
        <el-table-column prop="email" label="邮箱" />
        <el-table-column prop="goodsNo" label="商品编号" />
        <el-table-column prop="goodsName" label="商品名称" />
        <el-table-column prop="picture" label="图片" />
        <el-table-column prop="modelNumber" label="型号" />
        <el-table-column prop="brandName" label="品牌名称" />
        <el-table-column prop="specification" label="商品规格" />
        <el-table-column prop="materParam" label="材质及参数" />
        <el-table-column prop="color" label="颜色" />
        <el-table-column prop="level" label="等级" />
        <el-table-column prop="producing" label="产地" />
        <el-table-column prop="unit" label="单位" />
        <el-table-column prop="quotedNum" label="报价数量" />
        <el-table-column prop="unitNoTax" label="单价（未税）" />
        <el-table-column prop="minOrder" label="最小起订量" />
        <el-table-column prop="minInventory" label="最小库存数" />
        <el-table-column prop="inventoryUnit" label="库存单位" />
        <el-table-column prop="supplyCycle" label="供货周期（天）" />
        <el-table-column v-if="checkPer(['admin','cailiaoQuotationsSoildWoodSummary:edit','cailiaoQuotationsSoildWoodSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudCailiaoQuotationsSoildWoodSummary from '@/api/cailiao/cailiaoQuotationsSoildWoodSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, shopName: null, enterDate: null, companyName: null, companyNature: null, companyAddr: null, email: null, goodsNo: null, goodsName: null, picture: null, modelNumber: null, brandName: null, specification: null, materParam: null, color: null, level: null, producing: null, unit: null, quotedNum: null, unitNoTax: null, minOrder: null, minInventory: null, inventoryUnit: null, supplyCycle: null }
export default {
  name: 'CailiaoQuotationsSoildWoodSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '报价汇总(实木材料商)', url: 'api/cailiaoQuotationsSoildWoodSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudCailiaoQuotationsSoildWoodSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'cailiaoQuotationsSoildWoodSummary:add'],
        edit: ['admin', 'cailiaoQuotationsSoildWoodSummary:edit'],
        del: ['admin', 'cailiaoQuotationsSoildWoodSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别（国家）' },
        { key: 'shopName', display_name: '店铺名称' },
        { key: 'companyName', display_name: '公司名称' },
        { key: 'companyNature', display_name: '公司性质' },
        { key: 'companyAddr', display_name: '公司地址' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
