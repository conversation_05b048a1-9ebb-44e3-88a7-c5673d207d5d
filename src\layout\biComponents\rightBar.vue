<template>
  <div class="content-right">
    <div class="total">
      <div class="tit-bar">
        <span>{{ right.rightTableOne.title }}</span>
        <img src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png" alt="">
      </div>

      <div class="total-pot">
        <div
          v-for="(item, index) in rightTopData.slice(0 ,6)"
          :key="item.id"
          :class="[
            `bar-pos${index + 1}`,
          ]"
        >
          <p class="bar-unit">
            <span
              :class="[
                `dot${ index< 3 ? 1 : 2 }`
              ]"
            >
              &nbsp;
            </span>
            <span
              :class="[
                `font${index< 3 ? 1 : 2}`,
              ]"
            >{{ item.name }}</span>
          </p>
          <p
            :class="[
              `total-quantity`,
              `font${index< 3 ? 1 : 2}`
            ]"
          >{{ item.value }}</p>
        </div>
        <div class="total-item7">
          <p class="total-unit">{{ rightTopData[rightTopData.length-1].name }}</p>
          <p class="total-quantity">{{ rightTopData[rightTopData.length-1].value }}</p>
        </div>
      </div>
    </div>

    <div class="total">
      <div class="tit-bar">
        <span>{{ right.rightTableTwo.title }}</span>
        <img id="tit_spot" src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png" alt="">
      </div>
      <div ref="distribution" style="width: 340px;height:208px" class="total-pot">&nbsp;</div>
    </div>

    <div class="total">
      <div class="tit-bar">
        <span>{{ right.rightTableThree.title }}</span>
        <img id="tit_spot" src="https://supplier.talmdcloud.com/wstmart/admin/view//images/tit_spot.png" alt="">
      </div>
      <div class="ranking-category">
        <p>排名类别</p>
        <div v-click-outside="closeDropdown" class="custom-select">
          <div class="selected" @click="toggleDropdown">
            <span>{{ selectedLabel }}</span>
            <div class="arrow">▼</div>
          </div>
          <ul v-show="isOpen" class="options">
            <li
              v-for="(option, index) in right.rightTableThree.options"
              :key="index"
              :class="{ 'hover-option': hoverIndex === index }"
              @click="selectOption(option)"
              @mouseenter="hoverIndex = index"
              @mouseleave="hoverIndex = -1"
            >
              {{ option.label }}
            </li>
          </ul>
        </div>
      </div>
      <div ref="proRanking" style="width: 340px;height:403px" class="total-pot">&nbsp;</div>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts/core'
export default {
  directives: {
    // 指令名称：v-click-outside
    'click-outside': {
      bind(el, binding, vnode) { // 指令绑定到元素时触发
        el.clickOutsideEvent = function(event) {
          // 判断点击事件是否发生在当前元素外部
          if (!(el === event.target || el.contains(event.target))) {
            // 执行绑定的回调方法（例如关闭下拉）
            vnode.context[binding.expression](event)
          }
        }
        // 给 body 添加点击事件监听
        document.body.addEventListener('click', el.clickOutsideEvent)
      },
      // 指令解绑时触发（清理工作）
      unbind(el) {
        // 移除事件监听
        document.body.removeEventListener('click', el.clickOutsideEvent)
      }
    }
  },
  props: {
    right: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      isOpen: false,
      hoverIndex: -1,
      selectedLabel: '',
      rightTopData: [
        {
          id: 0,
          name: '',
          value: 0
        }
      ]
    }
  },
  watch: {
    right: {
      handler(newVal) {
        if (
          newVal.rightTableOne.rightTopData &&
          newVal.rightTableTwo.rightMiddleTitle &&
          newVal.rightTableThree.options
        ) {
          this.initChart() // 所有数据就绪后渲染图表
        }
      },
      immediate: true, // 立即触发一次（处理父组件数据已提前到达的情况）
      deep: true // 深度监听（如果数据结构嵌套较深）
    }
  },
  mounted() {
    window.addEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      this.rightTopData = this.right.rightTableOne.rightTopData
      this.selectedLabel = this.right.rightTableThree.options[0].label
      this.distribution = echarts.init(this.$refs.distribution)
      const option3 = {
        tooltip: {
          trigger: 'axis',
          show: true,
          backgroundColor: '#182430',
          borderWidth: 1,
          borderColor: 'rgba(43, 106, 143, 1)',
          // 提示框位置
          position: function(point, params, dom, rect, size) {
            var x = 0
            var y = 0 // y坐标位置
            // 当前鼠标位置
            var pointX = point[0]
            var pointY = point[1]
            // 提示框大小
            var boxWidth = size.contentSize[0]
            var boxHeight = size.contentSize[1]

            // boxWidth > pointX 说明鼠标左边放不下提示框
            if (boxWidth > pointX) {
              x = pointX + 10
            } else { // 左边放的下
              x = pointX - boxWidth - 15
            }

            // boxHeight > pointY 说明鼠标上边放不下提示框
            if (boxHeight > pointY) {
              y = 5
            } else { // 上边放得下
              y = pointY - boxHeight
            }
            return [x, y]
          },
          axisPointer: {
            type: 'shadow'
          },
          formatter(params) {
            var result = ''
            for (var i = 0; i < params.length; i++) {
              result += "<div style='display:flex;justify-content: space-around;width:110px;text-align:left;'>"
              result += "<div style='display:flex;width:100%'>"
              result += "<div style='background-color:" + params[i].color + ";width:10px;height:10px; display: inline-block;margin-right:5px;margin-top:5px'></div>"
              result += "<div style='width:70px;color:rgba(221, 232, 235, 1);font-size:12px'>" + params[i].seriesName + ' :</div>'
              result += '</div>'
              result += "<div style='color:#fff;font-weight:700;font-size:14px'>" + params[i].data + '</div>'
              result += '</div>'
            }
            return result
          }
        },
        grid: {
          top: '20%',
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        legend: {
          top: '2%',
          left: 'left',
          icon: 'rect',
          itemWidth: 8,
          itemHeight: 8,
          itemGap: 7,
          textStyle: {
            fontSize: '12px',
            rich: {
              a: {
                verticalAlign: 'middle'
              }
            }
          },

          data: this.right.rightTableTwo.rightMiddleTitle
        },

        xAxis: {
          type: 'category',
          data: this.right.rightTableTwo.rightMiddleCountry,
          axisLabel: {
            color: '#DDE8EB'
          }
        },
        yAxis: {
          type: 'value',
          min: 0,
          max: 200,
          splitLine: {
            lineStyle: {
              type: 'dotted',
              color: 'rgba(94,109,110,0.4)'
            }
          },
          axisLabel: {
            color: '#DDE8EB'
          }
        },
        series: [
          {
            stack: '分布',
            name: this.right.rightTableTwo.rightMiddleData[0].name,
            type: 'bar',
            barWidth: '30%',
            data: this.right.rightTableTwo.rightMiddleData[0].data,
            showBackground: false,
            itemStyle: {
              color: '#589AC7'
            }
          },
          {
            stack: '分布',
            name: this.right.rightTableTwo.rightMiddleData[1].name,
            type: 'bar',
            barWidth: '33%',
            data: this.right.rightTableTwo.rightMiddleData[1].data,
            showBackground: false,
            itemStyle: {
              color: '#74BF95'
            }
          },
          {
            stack: '分布',
            name: this.right.rightTableTwo.rightMiddleData[2].name,
            type: 'bar',
            barWidth: '33%',
            data: this.right.rightTableTwo.rightMiddleData[2].data,
            showBackground: false,
            itemStyle: {
              color: '#F0884D'
            }
          },
          {
            stack: '分布',
            name: this.right.rightTableTwo.rightMiddleData[3].name,
            type: 'bar',
            barWidth: '33%',
            data: this.right.rightTableTwo.rightMiddleData[3].data,
            showBackground: false,
            itemStyle: {
              color: '#D580FF'
            }
          },
          {
            stack: '分布',
            name: this.right.rightTableTwo.rightMiddleData[4].name,
            type: 'bar',
            barWidth: '33%',
            data: this.right.rightTableTwo.rightMiddleData[4].data,
            showBackground: false,
            itemStyle: {
              color: '#7863FF'
            }
          },
          {
            stack: '分布',
            name: this.right.rightTableTwo.rightMiddleData[5].name,
            type: 'bar',
            barWidth: '33%',
            data: this.right.rightTableTwo.rightMiddleData[5].data,
            showBackground: false,
            itemStyle: {
              fontSize: '10px',
              color: '#60C42D'
            }
          }
        ]
      }
      this.distribution.setOption(option3)

      this.proRanking = echarts.init(this.$refs.proRanking)
      const option4 = {
        grid: {
          top: '3%',
          left: '2%',
          right: '2%',
          bottom: '2%',
          containLabel: true
        },
        dataZoom: [
          {
            show: false,
            type: 'slider',
            yAxisIndex: [0, 1],
            showDetail: false,
            width: 8,
            height: '100%',
            endValue: 8,
            zoomLoxk: false
          },
          {
            type: 'inside',
            yAxisIndex: [0, 1],
            zoomOnMouseWheel: false,
            moveOnMouseMove: true,
            moveOnMouseWheel: true
          }
        ],

        xAxis: {
          type: 'value',
          min: 0,
          max: 30000,
          axisLine: {
            show: false
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          },
          axisLabel: {
            show: false
          }
        },
        yAxis: [
          {
            inverse: true,
            data: this.right.rightTableThree.YAxisDataLeft,
            axisLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            splitLine: {
              show: false
            },
            axisLabel: {
              show: true,
              inside: true,
              interval: 0,
              splitNumber: 50,
              textStyle: {
                color: '#BED0DB',
                verticalAlign: 'bottom',
                fontSize: 13,
                align: 'left',
                padding: [0, 0, 10, 45]

              }
            }
          },
          {
            inverse: true,
            data: this.right.rightTableThree.YAxisDataRight,
            axisLabel: {
              inside: true,
              textStyle: {
                color: '#6BE4FF',
                verticalAlign: 'bottom',
                fontSize: 13,
                align: 'left',
                padding: [0, 0, 10, -310]
              }
            },
            offset: 0,
            splitLine: {
              show: false
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: false
            }
          }
        ],
        series: [
          {
            // 辅助系列
            type: 'bar',
            barGap: '-100%',
            silent: true,
            itemStyle: {
              color: 'rgba(255, 255, 254, 0.2)'
            },
            barWidth: 13,
            data: [30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000, 30000]
          },
          {
            name: '',
            type: 'bar',
            barWidth: '33%',
            data: this.right.rightTableThree.YAxisData,
            label: {
              position: [10, 10],
              normal: {
                position: [280, -18],
                show: true,
                textStyle: {
                  color: '#DAECF6',
                  fontSize: 16
                }
              }
            },
            itemStyle: {
              normal: {
                color() {
                  return new echarts.graphic.LinearGradient(
                    1,
                    0,
                    0,
                    0,
                    [
                      { offset: 0, color: '#9ECCF5' },
                      { offset: 1, color: '#122434' }
                    ],
                    false
                  )
                }
              }
            }
          }
        ]
      }
      this.proRanking.setOption(option4)
    },
    handleResize() {
      if (this.increase) {
        this.increase.resize()
      }
    },
    toggleDropdown() {
      this.isOpen = !this.isOpen
    },
    closeDropdown() {
      this.isOpen = false
    },
    selectOption(option) {
      this.$emit('input', option.value)
      this.isOpen = false
      this.selectedLabel = this.right.rightTableThree.option.label
    }
  }
}
</script>

<style rel='stylesheet/scss' lang='scss' scoped>
@import '@/assets/styles/bi.scss';
  .content-right {
    margin-top: 10px;
    float: right;
  }
  .custom-select {
    width: 120px;
    position: relative;
    right: 10px;
    user-select: none;
  }

  .selected {
    height: 40px;
    padding: 10px 15px;
    // border: 2px solid #315877;
    // border-radius: 8px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #030B15;
  }

  .arrow {
    color: #315877;
    transition: transform 0.2s;
    transform: rotate(0deg);
  }

  .options {
    position: absolute;
    width: 100%;
    border: 2px solid #294A64;
    border-radius: 8px;
    margin-top: 4px;
    background: linear-gradient(
      135deg,
      #335B7A,
      #15202A
    );
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
    max-height: 300px;
    overflow-y: auto;
    z-index: 100;
  }

  .options li {
    height: 45px;
    padding: 0px 8px;
    // font-size: medium;
    color: #BED0DB;
    cursor: pointer;
    transition: all 0.2s;
  }

  .options li:last-child {
    border-bottom: none;
  }

  .options li:hover {
    background: linear-gradient(to right, rgba(63,122,157, 0.5), rgba(49,83,94, 0));
    color: #BED0DB;
  }

  .hover-option {
    background-color: rgba(49, 88, 119, 0.1);
  }

  .bar-pos1 {
    position: absolute;
    right: 10px;
    top: 25px;
  }

  .bar-pos2 {
    position: absolute;
    right: -3px;
    top: 85px;
  }

  .bar-pos3 {
    position: absolute;
    right: 10px;
    top: 145px;
  }

  .bar-pos4 {
    position: absolute;
    left: 5px;
    top: 25px;
  }

  .bar-pos5 {
    position: absolute;
    left: -5px;
    top: 85px;
  }

  .bar-pos6 {
    position: absolute;
    left: 5px;
    top: 145px;
  }

  .bar-unit {
    width: 120px;
    height: 25px;
    font-weight: 800;
    color: #BED0DB;
    background: linear-gradient(to right, rgba(30, 46, 65, 0) 0%, rgba(30, 46, 65, 1) 50%, rgba(30, 46, 65, 0) 100%);
  }

  .dot1{
    position: absolute;
    left: 5px;
    top: 9px;
    display: block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #0DF6FF;
    box-shadow:
    0 0 3px 5px rgba(13,246,255,.4)
    ,0 0 6px 1px rgba(13,246,255,.2);  /* 外层光晕 */
  }

  .dot2{
    position: absolute;
    right: 5px;
    top: 9px;
    display: block;
    width: 5px;
    height: 5px;
    border-radius: 50%;
    background-color: #0DF6FF;
    box-shadow:
    0 0 3px 5px rgba(13,246,255,.4)
    ,0 0 6px 1px rgba(13,246,255,.2);  /* 外层光晕 */
  }

  .font1 {
    position: absolute;
    left: 19px;
  }

  .font2 {
    position: absolute;
    right: 19px;
  }
</style>
