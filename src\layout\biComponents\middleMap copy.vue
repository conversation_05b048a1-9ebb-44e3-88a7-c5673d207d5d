<template>
  <div class="map">
    <div>
      <div ref="chinaMap" style="width:1230px;height:666px" class="chinaMap total-pot">&nbsp;</div>
    </div>

    <div class="statistics-pot">
      <div
        class="switcher"
        :style="{ transform: `translateX(-${offset}px)` }"
      >
        <div class="statistics">
          <MiddleButtonMenu
            v-for="(stat, index) in statsData"
            :key="index"
            :title="stat.title"
            :items="stat.items"
          />
        </div>
      </div>
      <div class="content_2">&nbsp;</div>

      <ul class="control-box">
        <li
          class="prev"
          :disabled="currentPage === 0"
          @click="prevPage"
        >
          <img src="https://supplier.talmdcloud.com/wstmart/admin/view//images/prev.png" alt="">
        </li>
        <li
          class="next"
          :disabled="currentPage >= maxPage"
          @click="nextPage"
        >
          <img src="https://supplier.talmdcloud.com/wstmart/admin/view//images/next.png" alt="">
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import MiddleButtonMenu from '@/layout/biComponents/middleButtonMenu.vue'
import * as echarts from 'echarts/core'

export default {
  components: {
    MiddleButtonMenu
  },
  props: {
    statsData: {
      type: Array,
      default: () => ([])
    }
  },
  data() {
    return {
      currentPage: 0,
      itemWidth: 310
    }
  },
  computed: {
    // 计算可翻页码
    maxPage() {
      return this.statsData.length - 4
    },
    // 计算滑动偏移量
    offset() {
      return this.currentPage * this.itemWidth
    }
  },
  mounted() {
    this.initChartMap()
    window.addEventListener('resize', this.handleResize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize)
    if (this.chinaMap) {
      this.chinaMap.dispose()
      this.chinaMap = null
    }
  },
  methods: {
    prevPage() {
      if (this.currentPage > 0) this.currentPage--
    },
    nextPage() {
      if (this.currentPage < this.maxPage) this.currentPage++
    },
    initChartMap() {
      this.chinaMap = echarts.init(this.$refs.chinaMap)
      const option5 = {
        tooltip: {
          trigger: 'item',
          backgroundColor: '#0F1D26',
          textStyle: {
            color: '#DAECF6' // 设置文字颜色
          },
          formatter(params) {
            if (params.data.id !== undefined) {
              var result = ''
              result +=
                '<div style="display: flex;margin:-10px -10px 0 -10px;min-width:240px;height:39px;padding-right:12px;background: linear-gradient(90deg, #248CB9 0%, #213045 100%);">'
              result +=
                '<img style="vertical-align: top;margin-right:16px" src="../images/region.png" alt="">'
              result +=
                '<span style="font-size:20px;display:inline-block;margin-top:10px">' +
                params.data.name +
                '</span>'
              result += '</div>'
              result +=
                '<div style="display:flex;justify-content: space-between;padding:12px;background: rgba(85, 147, 192, 0.10);font-size:16px;margin:12px 0 8px 0">'
              result += '<div>楼盘数 :</div>'
              result += '<div>' + params.data.value + '</div>'
              result += '</div>'
              result +=
                '<div style="display:flex;justify-content: space-between;padding:12px;background: rgba(85, 147, 192, 0.10);font-size:16px;margin:0 0 8px 0">'
              result += '<div>户型数 :</div>'
              result += '<div>' + params.data.value + '</div>'
              result += '</div>'
              result +=
                '<div style="display:flex;justify-content: space-between;padding:12px;background: rgba(85, 147, 192, 0.10);font-size:16px;">'
              result += '<div>设计立项面积数 :</div>'
              result += '<div>' + params.data.value + '</div>'
              result += '</div>'

              return result
            } else {
              // 控制值为空时不报错，且没有悬停展示
              return ''
            }
          }
        },

        series: [
          {
            name: '各省份的总成交量对比',
            type: 'map',
            mapType: 'china',
            roam: false,
            silent: false,
            emphasis: {
              // disabled: true,
              // focus: 'none',
              itemStyle: {
                areaColor: {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: 'transparent'
                    },
                    {
                      offset: 1,
                      color: 'transparent'
                    }
                  ],
                  global: true
                }
              }
            },
            selectedMode: false,
            zoom: 1.0,
            itemStyle: {
              borderWidth: 0,
              areaColor: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'transparent'
                  },
                  {
                    offset: 1,
                    color: 'transparent'
                  }
                ],
                global: true
              },
              borderColor: '#fff',
              shadowColor: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'transparent'
                  },
                  {
                    offset: 1,
                    color: 'transparent'
                  }
                ],
                global: true
              }
            },
            label: {
              normal: {
                formatter(params) {
                  if (params.data.id !== undefined) {
                    var result1 = ''
                    result1 += params.data.name
                    return result1
                  } else {
                    return ''
                  }
                },
                show: true,
                textStyle: {
                  color: '#fff',
                  fontSize: '16px',
                  fontWeight: '1000'
                }
              },
              emphasis: {
                formatter(params) {
                  if (params.data.id !== undefined) {
                    var result2 = ''
                    result2 += params.data.name
                    return result2
                  } else {
                    return ''
                  }
                },
                show: true,
                textStyle: {
                  color: '#BFCAD4'
                }
              }
            },
            data: this.mydata
          }
        ]
      }
      this.chinaMap.setOption(option5)
    },
    randomData() {
      return Math.round(Math.random() * 500)
    },
    handleResize() {
      if (this.chinaMap) {
        this.chinaMap.resize()
      }
    }
  }
}
</script>

<style rel='stylesheet/scss' lang='scss' scoped>
@import '@/assets/styles/bi.scss';
  .map {
    float: left;
  }
  .statistics-pot {
    width: 1230px;
    position: relative;
    overflow: hidden;
    margin: 0 auto;
  }
  /* 滑动容器 */
  .switcher {
    display: flex;
    transition: transform 0.5s ease;
    width: max-content;
  }

  /* 单个统计项 */
  .statistics-item {
    flex: 0 0 292px; /* 固定宽度 */
    height: 100%;
    padding: 15px;
    box-sizing: border-box;
  }

  .nav-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
  }
</style>
