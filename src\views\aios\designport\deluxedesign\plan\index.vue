<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="单号">
                  <el-input v-model="form.oddNumbers" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="流程状态">
                  <el-select v-model="form.status" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.status"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="平面图计划完成时间">
                  <el-date-picker v-model="form.planCompletionTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目交期">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="30">
              <el-col :span="12">
                <el-form-item label="客户确认时间">
                  <el-date-picker v-model="form.customerConfirmTime" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>

            </el-row>

            <!-- 子表单 -->
            <el-divider content-position="left"><i class="el-icon-document" /> 平面图版本</el-divider>
            <div class="table-container">
              <el-table :data="drawingVersionsList" size="small" border style="width: 100%">
                <el-table-column label="序号" width="60" align="center">
                  <template slot-scope="scope">
                    {{ scope.$index + 1 }}
                  </template>
                </el-table-column>
                <el-table-column label="提交时间" width="180">
                  <template slot-scope="scope">
                    <el-date-picker v-model="scope.row.submitTime" type="datetime" style="width: 100%" />
                  </template>
                </el-table-column>
                <el-table-column label="绘制人" min-width="120">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.draftsman" placeholder="请输入绘制人" />
                  </template>
                </el-table-column>
                <el-table-column label="图纸版本" min-width="130">
                  <template slot-scope="scope">
                    <el-select v-model="scope.row.drawingVersion" filterable placeholder="请选择" style="width: 100%">
                      <el-option
                        v-for="item in dict.drawing_version"
                        :key="item.id"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </template>
                </el-table-column>
                <el-table-column label="异常说明" min-width="160">
                  <template slot-scope="scope">
                    <el-input v-model="scope.row.exceptionDescription" placeholder="请输入异常说明" />
                  </template>
                </el-table-column>
                <el-table-column label="PDF格式附件" min-width="200">
                  <template slot-scope="scope">
                    <general-file-upload
                      v-model="scope.row.pdfFormatFile"
                      size="small"
                      :field-name="'pdfFormatFile'"
                      v-bind="tableFileUploadConfig"
                      :use-minio-delete="true"
                      list-type="text"
                      show-file-list
                      show-file-name
                      :hide-remove="isViewMode"
                      @change="handleSubformFileChange($event, scope.row)"
                    />
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="80" align="center">
                  <template slot-scope="scope">
                    <el-button type="text" icon="el-icon-delete" @click="handleRemoveDrawingVersion(scope.$index)" />
                  </template>
                </el-table-column>
              </el-table>
              <div style="margin-top: 10px;">
                <el-button size="mini" type="primary" icon="el-icon-plus" @click="handleAddDrawingVersion">新增</el-button>
              </div>
            </div>

            <el-divider content-position="left"><i class="el-icon-picture" /> 附件信息</el-divider>
            <el-form-item label="最终平面图CAD附件">
              <file-upload
                :field-value.sync="form.cadFormatFile2"
                :limit="5"
                :upload-to-server="true"
                :api-url="minioUploadApi"
                :hide-remove="isViewMode"
                @change="handleFileListChange($event, form)"
              />
            </el-form-item>
            <el-form-item label="最终平面布置图JPG">
              <file-upload
                :field-value.sync="form.finalLayoutPlan"
                :limit="5"
                :upload-to-server="true"
                :api-url="minioUploadApi"
                accept=".jpg,image/jpeg"
                :hide-remove="isViewMode"
                @change="handleFileListChange($event, form)"
              />
            </el-form-item>
            <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>
          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="id" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质">
          <template slot-scope="scope">
            {{ dict.label.project_nature[scope.row.projectNature] }}
          </template>
        </el-table-column>
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column prop="projectDeliveryPeriod" label="项目交期" />
        <el-table-column v-if="checkPer(['admin','tumaiSjPingmian:edit','tumaiSjPingmian:del','tumaiSjPingmian:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
      <!-- 图片查看对话框 -->
      <el-dialog :visible="dialogVisible" append-to-body @update:visible="val => dialogVisible = val">
        <img width="100%" :src="dialogImageUrl" alt="">
      </el-dialog>
    </div>
  </div>
</template>

<script>
import crudTumaiSjPingmian from '@/api/aios/designport/deluxedesign/tumaiSjPingmian'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import FileUpload from '@/components/FileUpload'
import { mapGetters } from 'vuex'
import { SimpleFileHandler } from '@/utils/fileHandler'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { deleteRecordFiles, deleteSubformFiles, deleteSubformRowFiles } from '@/utils/minioFileDeleter'

const defaultForm = {
  id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, tabDate: null,
  projectNumber: null, projectName: null, projectOverview: null, planCompletionTime: null,
  projectNature: null, uid: null, optdt: null, optid: null, optname: null, applydt: null,
  explain: null, status: null, isturn: null, approval: null, cadFormatFile2: null,
  finalLayoutPlan: null, projectid: null, projectDeliveryPeriod: null, projectAddress: null,
  typeOfService: null, drawingVersion: null, customerConfirmTime: null, createtime: null,
  customerSignFile: null,
  // 子表表单字段
  submitTime: null, draftsman: null, pdfFormatFile: null, exceptionDescription: null,
  drawingVersions: '[]',
  planSketch: '[]',
  originalMeasurementDrawings: '[]',
  customerSignatureAttachment: '[]',
  planEffectDrawing: '[]'
}
export default {
  name: 'TumaiSjPingmian',
  components: { pagination, crudOperation, rrOperation, FileUpload, GeneralFileUpload, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), SimpleFileHandler.mixin, subformFileHandlerMixin, GeneralFileHandler.mixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['project_nature', 'status', 'drawing_version'],
  cruds() {
    return CRUD({ title: '平面图', url: 'api/tumaiSjPingmian', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiSjPingmian }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiSjPingmian:add'],
        edit: ['admin', 'tumaiSjPingmian:edit'],
        del: ['admin', 'tumaiSjPingmian:del'],
        view: ['admin', 'tumaiSjPingmian:edit', 'tumaiSjPingmian:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      cadFormatFileList: [],
      finalLayoutPlanList: [],
      customerSignFileList: [],
      pdfFormatFileList: [],
      // 平面图版本列表
      drawingVersionsList: [],
      // 表格中文件上传配置
      tableFileUploadConfig: {
        accept: '.pdf,application/pdf',
        maxFiles: 2,
        tipText: '仅支持上传PDF文件',
        buttonText: '上传PDF',
        listType: 'text',
        useMinioDelete: true,
        showFileList: true,
        showFileName: true
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ],
      // 文件字段映射
      fileFields: {
        pdfFormatFile: 'PDF格式文件'
      }
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields && this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    } else {
      console.log('文件删除API已配置:', this.minioDeleteApi)
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前操作，表单数据:', form)
      // 初始化主表单附件字段
      this.initAttachmentFields([
        'cadFormatFile2',
        'finalLayoutPlan',
        'customerSignFile'
      ])
      // 初始化平面图版本列表
      this.initDrawingVersionsList()
      // 初始化子表单中的文件字段 (使用新工具)
      this.initSubformFileFields(this.drawingVersionsList, 'pdfFormatFile')
    },

    // 钩子：查看前的操作
    [CRUD.HOOK.beforeToView](crud, form) {
      this.initAttachmentFields([
        'cadFormatFile2',
        'finalLayoutPlan',
        'customerSignFile'
      ])
      this.initDrawingVersionsList()
      this.initSubformFileFields(this.drawingVersionsList, 'pdfFormatFile')
      // 设置表单为只读模式
      this.setFormReadonly(true)
      return true
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      // 删除主表单的文件
      deleteRecordFiles(data, [
        'cadFormatFile2',
        'finalLayoutPlan',
        'customerSignFile'
      ])

      // 删除子表单中的文件
      deleteSubformFiles(data, 'drawingVersions', 'pdfFormatFile')

      return true
    },

    // 钩子：查看取消前的操作
    [CRUD.HOOK.beforeViewCancel](crud, form) {
      // 恢复表单可编辑状态
      this.setFormReadonly(false)
      return true
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd](crud, form) {
      console.log('添加前操作')

      // 初始化为空数组
      this.form.cadFormatFile2 = '[]'
      this.form.finalLayoutPlan = '[]'
      this.form.customerSignFile = '[]'

      this.drawingVersionsList = []

      // 添加一个空的版本记录
      this.handleAddDrawingVersion()
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit](crud) {
      console.log('提交前操作')

      try {
        console.log('提交前原始子表单数据:', JSON.stringify(this.drawingVersionsList))
        console.log('提交前原始表单数据:', JSON.stringify({
          cadFormatFile2: this.form.cadFormatFile2,
          finalLayoutPlan: this.form.finalLayoutPlan,
          customerSignFile: this.form.customerSignFile
        }))

        // 处理主表单附件字段
        this.prepareAttachmentFields([
          'cadFormatFile2',
          'finalLayoutPlan',
          'customerSignFile'
        ])

        console.log('处理主表单附件后:', JSON.stringify({
          cadFormatFile2: this.form.cadFormatFile2,
          finalLayoutPlan: this.form.finalLayoutPlan,
          customerSignFile: this.form.customerSignFile
        }))

        // 处理子表单中的文件字段 (使用新工具)
        this.prepareSubformFileFields(this.drawingVersionsList, 'pdfFormatFile')

        // 将处理后的子表单数据同步回表单
        crud.form.drawingVersions = JSON.stringify(this.drawingVersionsList)

        console.log('处理后的表单数据:', crud.form.drawingVersions)

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 初始化平面图版本列表
    initDrawingVersionsList() {
      try {
        this.drawingVersionsList = this.form.drawingVersions ? JSON.parse(this.form.drawingVersions) : []
        if (!Array.isArray(this.drawingVersionsList)) {
          this.drawingVersionsList = []
        }

        // 确保每行都有PDF附件字段
        this.drawingVersionsList.forEach(item => {
          if (!item.pdfFormatFile) {
            item.pdfFormatFile = '[]'
          } else if (typeof item.pdfFormatFile === 'string' && !item.pdfFormatFile.startsWith('[')) {
            // 处理URL格式的情况，转换为标准JSON格式
            item.pdfFormatFile = this.convertUrlsToFileArray(item.pdfFormatFile)
          }
        })
      } catch (e) {
        console.error('解析图纸版本数据失败:', e)
        this.drawingVersionsList = []
      }

      // 如果没有数据，默认添加一条空记录
      if (this.drawingVersionsList.length === 0) {
        this.handleAddDrawingVersion()
      }
    },

    // 将URL字符串转换为文件数组
    convertUrlsToFileArray(urlStr) {
      if (!urlStr) return '[]'

      try {
        // 如果已经是JSON格式，直接返回
        if (urlStr.startsWith('[') && urlStr.endsWith(']')) {
          return urlStr
        }

        // 处理逗号分隔的多个URL
        const urls = urlStr.split(',')
        const files = urls.map(url => {
          // 从URL提取文件名
          const fileName = url.substring(url.lastIndexOf('/') + 1)
          return {
            name: fileName,
            url: url,
            size: 0,
            type: ''
          }
        })

        return JSON.stringify(files)
      } catch (e) {
        console.error('URL转换文件数组出错:', e)
        return '[]'
      }
    },

    // 添加图纸版本
    handleAddDrawingVersion() {
      this.drawingVersionsList.push({
        submitTime: null,
        draftsman: null,
        drawingVersion: null,
        exceptionDescription: null,
        pdfFormatFile: '[]'
      })
    },

    // 移除图纸版本
    handleRemoveDrawingVersion(index) {
      // 获取要删除的行数据
      const rowData = this.drawingVersionsList[index]

      // 删除该行关联的MinIO文件
      if (rowData) {
        console.log('删除子表单行文件，行数据:', rowData)
        deleteSubformRowFiles(rowData, 'pdfFormatFile')
      }
      this.drawingVersionsList.splice(index, 1)
      if (this.drawingVersionsList.length === 0) {
        this.handleAddDrawingVersion()
      }
    },

    // 初始化图片列表
    initImageLists() {
      // 清空原有的列表
      this.clearImageLists()

      // 初始化最终平面图CAD附件
      if (this.form.cadFormatFile2) {
        const urls = this.form.cadFormatFile2.split(',')
        this.cadFormatFileList = urls.map((url, index) => {
          return {
            name: `CAD附件${index + 1}`,
            url: url
          }
        })
      }

      // 初始化最终平面布置图JPG
      if (this.form.finalLayoutPlan) {
        const urls = this.form.finalLayoutPlan.split(',')
        this.finalLayoutPlanList = urls.map((url, index) => {
          return {
            name: `平面布置图${index + 1}`,
            url: url
          }
        })
      }

      // 初始化客户签名附件
      if (this.form.customerSignFile) {
        const urls = this.form.customerSignFile.split(',')
        this.customerSignFileList = urls.map((url, index) => {
          return {
            name: `客户签名${index + 1}`,
            url: url
          }
        })
      }

      // 初始化PDF格式附件 (这部分现在已经移到表格行内)
      if (this.form.pdfFormatFile) {
        const urls = this.form.pdfFormatFile.split(',')
        this.pdfFormatFileList = urls.map((url, index) => {
          return {
            name: `PDF附件${index + 1}`,
            url: url
          }
        })
      }
    },

    // 清空图片列表
    clearImageLists() {
      this.cadFormatFileList = []
      this.finalLayoutPlanList = []
      this.customerSignFileList = []
      this.pdfFormatFileList = []
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 转换图片为Base64
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => resolve(reader.result)
        reader.onerror = error => reject(error)
      })
    },

    // 统一处理图片上传、移除和更新
    handleImageOperation(operation, type, file, fileList) {
      const listMap = {
        'cadFormatFile2': 'cadFormatFileList',
        'finalLayoutPlan': 'finalLayoutPlanList',
        'customerSignFile': 'customerSignFileList',
        'pdfFormatFile': 'pdfFormatFileList'
      }

      this[listMap[type]] = fileList
      this.updateFormImage(type, listMap[type])
    },

    // 统一处理更新表单中的图片字段
    async updateFormImage(fieldName, listName) {
      const imageUrls = []
      for (const file of this[listName]) {
        if (file.url) {
          imageUrls.push(file.url)
        } else if (file.raw) {
          // 如果是新上传的文件，处理raw文件
          if (file.raw.url) {
            // 如果已有URL，直接使用
            imageUrls.push(file.raw.url)
          } else {
            // 否则将文件转为Base64
            const base64 = await this.getBase64(file.raw)
            imageUrls.push(base64)
          }
        }
      }
      this.form[fieldName] = imageUrls.join(',')
    },

    // 处理子表单的文件变更
    handleSubformFileChange(event, row) {
      if (!event || !event.fieldName || !row) {
        return false
      }

      const fieldName = event.fieldName
      const files = event.files || []

      console.log(`子表单文件 ${fieldName} 更新:`, files)

      // 将文件数组转换为JSON字符串存储
      row[fieldName] = JSON.stringify(files)

      return true
    },

    // 文件列表变更处理
    handleFileListChange(event, row) {
      console.log('文件变更事件:', JSON.stringify(event))

      // 确保事件对象格式正确
      if (event && event.action === 'success' && event.file && event.file.response) {
        // 手动补充事件信息
        if (!event.fieldName && row.pdfFormatFile !== undefined) {
          // 针对子表单的平面图版本，指定正确的字段名
          event.fieldName = 'pdfFormatFile'
        }
      }

      let result = false

      try {
        if (row === this.form) {
          // 主表单文件处理
          result = this.handleAttachmentChange(event, row)
          console.log('主表单处理后数据:', JSON.stringify({
            cadFormatFile2: this.form.cadFormatFile2,
            finalLayoutPlan: this.form.finalLayoutPlan,
            customerSignFile: this.form.customerSignFile
          }))
        } else {
          // 子表单文件处理 (使用新工具)
          result = this.handleSubformFileChange(event, row)
          console.log('子表单处理后行数据:', JSON.stringify(row))
        }

        // if (!result) {
        //   console.warn('文件处理返回失败结果')
        //   this.$message.warning('文件处理失败，请重试')
        // }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },
    // 添加表单验证方法
    validateForm() {
      // 检查表单是否为空
      if (this.isFormEmpty()) {
        this.$message.error('请至少填写项目编号、项目名称或项目概况中的一项')
        return false
      }

      // 验证通过，提交表单
      this.crud.submitCU()
    },

    // 检查表单是否为空（未填写任何有效数据）
    isFormEmpty() {
      // 只检查主表单关键字段
      return !['projectNumber', 'projectName', 'projectOverview'].some(field =>
        this.form[field] && this.form[field].trim() !== ''
      )
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.image-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.image-upload-container .el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container {
  display: flex;
  flex-wrap: wrap;
}

.small-upload-container .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-container .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.small-upload-icon {
  font-size: 20px;
}

.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 138px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.table-container {
  margin-bottom: 20px;
  width: 100%;
  overflow-x: auto;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #f9f9f9;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input,
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}
</style>
