<template>
  <div v-if="visible && shouldShow" class="question-templates">
    <div class="templates-header">
      <div class="header-left">
        <i class="el-icon-magic-stick header-icon" />
        <span class="templates-title">快速开始分析</span>
        <span class="templates-subtitle">选择模板快速开始数据库分析</span>
      </div>
      <el-button
        type="text"
        size="mini"
        class="hide-btn"
        title="隐藏模板"
        @click="hideTemplates"
      >
        <i class="el-icon-close" />
      </el-button>
    </div>

    <div class="templates-grid">
      <div
        v-for="template in templates"
        :key="template.id"
        class="template-card"
        :class="`category-${template.category}`"
        @click="selectTemplate(template)"
      >
        <div class="card-header">
          <div class="template-icon">{{ template.icon }}</div>
          <div class="template-title">{{ template.title }}</div>
        </div>
        <div class="template-description">{{ template.description }}</div>
        <div class="template-preview">{{ template.content }}</div>
      </div>
    </div>

    <div class="templates-footer">
      <span class="footer-tip">💡 点击模板卡片自动填充到输入框，点击输入框旁的 <i class="el-icon-magic-stick" /> 按钮可重新打开</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuestionTemplate',

  props: {
    // 控制模板是否可见
    visible: {
      type: Boolean,
      default: true
    },
    // 消息数量，用于判断是否显示模板
    messageCount: {
      type: Number,
      default: 0
    },
    // AI是否正在响应
    isAiResponding: {
      type: Boolean,
      default: false
    }
  },

  data() {
    return {
      templates: [
        {
          id: 1,
          title: '表结构分析',
          content: '分析表结构并生成数据报表',
          icon: '📊',
          category: 'analysis',
          description: '深入分析数据表的结构、字段类型和关系'
        },
        {
          id: 2,
          title: '销售情况分析',
          content: '分析这个表的商品销售情况',
          icon: '💰',
          category: 'business',
          description: '分析商品销售数据、趋势和业绩表现'
        },
        {
          id: 3,
          title: '发展建议',
          content: '根据这些表的数据提出发展建议',
          icon: '🚀',
          category: 'strategy',
          description: '基于数据分析提供业务发展和优化建议'
        },
        {
          id: 4,
          title: '异常值检测',
          content: '检测数据中的异常值和潜在问题',
          icon: '🔍',
          category: 'analysis',
          description: '识别数据质量问题、异常值和数据完整性'
        },
        {
          id: 5,
          title: '用户行为分析',
          content: '分析用户行为模式和偏好',
          icon: '👥',
          category: 'user',
          description: '深入了解用户使用习惯、行为特征和偏好'
        },
        {
          id: 6,
          title: '数据可视化',
          content: '为这些数据创建直观的图表和可视化',
          icon: '📈',
          category: 'visualization',
          description: '生成各种类型的数据图表和可视化报表'
        },
        {
          id: 7,
          title: '趋势预测',
          content: '基于历史数据预测未来趋势',
          icon: '🔮',
          category: 'prediction',
          description: '利用历史数据进行趋势分析和未来预测'
        },
        {
          id: 8,
          title: '性能优化建议',
          content: '分析数据库性能并提出优化建议',
          icon: '⚡',
          category: 'optimization',
          description: '评估数据库性能瓶颈并提供优化方案'
        }
      ]
    }
  },

  computed: {
    // 判断是否应该显示模板
    shouldShow() {
      // AI不在响应时就可以显示模板，不限制消息数量
      return !this.isAiResponding
    }
  },

  methods: {
    // 选择模板
    selectTemplate(template) {
      this.$emit('template-selected', {
        content: template.content,
        template: template
      })
    },

    // 隐藏模板
    hideTemplates() {
      this.$emit('hide-templates')
    }
  }
}
</script>

<style scoped>
.question-templates {
  background: linear-gradient(135deg, #f8faff 0%, #f0f9ff 100%);
  border-radius: 16px;
  padding: 20px;
  margin: 16px 0;
  border: 1px solid #e1e8f0;
  box-shadow: 0 2px 12px rgba(99, 102, 241, 0.08);
  position: relative;
  overflow: hidden;
}

.question-templates::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #6366f1 0%, #06b6d4 50%, #10b981 100%);
}

.templates-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.header-icon {
  color: #6366f1;
  font-size: 20px;
  margin-bottom: 4px;
}

.templates-title {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.templates-subtitle {
  font-size: 14px;
  color: #64748b;
  margin-top: 2px;
}

.hide-btn {
  color: #94a3b8;
  padding: 4px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.hide-btn:hover {
  color: #64748b;
  background: rgba(148, 163, 184, 0.1);
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
  max-height: 50vh;
  overflow-y: auto;
}

.template-card {
  background: #ffffff;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid #e2e8f0;
  position: relative;
  overflow: hidden;
}

.template-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 4px;
  height: 100%;
  background: #e2e8f0;
  transition: all 0.3s ease;
}

.template-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(99, 102, 241, 0.15);
  border-color: #c7d2fe;
}

.template-card:hover::before {
  background: linear-gradient(180deg, #6366f1 0%, #06b6d4 100%);
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.template-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f1f5f9 0%, #e2e8f0 100%);
  border-radius: 10px;
  flex-shrink: 0;
}

.template-title {
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  line-height: 1.3;
}

.template-description {
  font-size: 13px;
  color: #64748b;
  line-height: 1.4;
  margin-bottom: 12px;
}

.template-preview {
  font-size: 14px;
  color: #475569;
  background: #f8fafc;
  padding: 10px 12px;
  border-radius: 8px;
  border-left: 3px solid #e2e8f0;
  font-style: italic;
  line-height: 1.4;
}

.templates-footer {
  text-align: center;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
}

.footer-tip {
  font-size: 12px;
  color: #94a3b8;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

/* 分类特定样式 */
.category-analysis:hover::before {
  background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
}

.category-business:hover::before {
  background: linear-gradient(180deg, #10b981 0%, #047857 100%);
}

.category-strategy:hover::before {
  background: linear-gradient(180deg, #8b5cf6 0%, #7c3aed 100%);
}

.category-user:hover::before {
  background: linear-gradient(180deg, #f59e0b 0%, #d97706 100%);
}

.category-visualization:hover::before {
  background: linear-gradient(180deg, #ef4444 0%, #dc2626 100%);
}

.category-prediction:hover::before {
  background: linear-gradient(180deg, #06b6d4 0%, #0891b2 100%);
}

.category-optimization:hover::before {
  background: linear-gradient(180deg, #84cc16 0%, #65a30d 100%);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .templates-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .template-card {
    padding: 14px;
  }

  .templates-title {
    font-size: 16px;
  }

  .template-title {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .question-templates {
    padding: 16px;
    margin: 12px 0;
  }

  .templates-header {
    margin-bottom: 16px;
  }

  .card-header {
    gap: 10px;
  }

  .template-icon {
    width: 36px;
    height: 36px;
    font-size: 20px;
  }
}
</style>
