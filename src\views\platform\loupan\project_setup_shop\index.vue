<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">国别</label>
        <el-input v-model="query.country" clearable placeholder="国别" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">归属门店/公司</label>
        <el-input v-model="query.ownerStore" clearable placeholder="归属门店/公司" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">入驻角色</label>
        <el-input v-model="query.enterRole" clearable placeholder="入驻角色" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectCode" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目性质</label>
        <el-input v-model="query.projectNature" clearable placeholder="项目性质" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目地址</label>
        <el-input v-model="query.projectAddr" clearable placeholder="项目地址" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="国别">
            <el-input v-model="form.country" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表人">
            <el-input v-model="form.lister" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="制表日期">
            <el-date-picker v-model="form.listerDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="归属门店/公司">
            <el-input v-model="form.ownerStore" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="入驻角色">
            <el-input v-model="form.enterRole" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目类型">
            <el-input v-model="form.projectType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目编号">
            <el-input v-model="form.projectCode" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目名称">
            <el-input v-model="form.projectName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目性质">
            <el-input v-model="form.projectNature" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目地址">
            <el-input v-model="form.projectAddr" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="服务类型">
            <el-input v-model="form.serviceType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目数量">
            <el-input v-model="form.projectNum" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘名称">
            <el-input v-model="form.buildName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="建筑结构">
            <el-input v-model="form.structure" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="层高">
            <el-input v-model="form.levelHigh" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="设计楼栋">
            <el-input v-model="form.designStorey" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="户型结构">
            <el-input v-model="form.houseType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="建筑面积㎡">
            <el-input v-model="form.buildArea" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目概况">
            <el-input v-model="form.projectSituation" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="开盘时间">
            <el-date-picker v-model="form.openingDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="交房时间">
            <el-date-picker v-model="form.payHouseDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="计划入住时间">
            <el-date-picker v-model="form.planCheckInDate" type="datetime" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="硬装预算">
            <el-input v-model="form.hardBudget" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="软装预算">
            <el-input v-model="form.softBudget" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="固装预算">
            <el-input v-model="form.fixedBudget" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司名称">
            <el-input v-model="form.companyName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="公司性质">
            <el-input v-model="form.companyNature" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（姓名）">
            <el-input v-model="form.clientName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（联系电话）">
            <el-input v-model="form.clientPhone" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（职务）">
            <el-input v-model="form.clientJob" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（微信）">
            <el-input v-model="form.clientWechat" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（邮箱）">
            <el-input v-model="form.clientEmail" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="客户信息（性别）">
            <el-input v-model="form.clientGender" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="风格需求">
            <el-input v-model="form.styleRequire" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="主色调">
            <el-input v-model="form.mainTone" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="辅色调">
            <el-input v-model="form.secondTone" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="讨厌的颜色">
            <el-input v-model="form.nastyColor" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="项目状态">
            <el-input v-model="form.projectStatus" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="country" label="国别" />
        <el-table-column prop="lister" label="制表人" />
        <el-table-column prop="listerDate" label="制表日期" />
        <el-table-column prop="ownerStore" label="归属门店/公司" />
        <el-table-column prop="enterRole" label="入驻角色" />
        <el-table-column prop="projectType" label="项目类型" />
        <el-table-column prop="projectCode" label="项目编号" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectNature" label="项目性质" />
        <el-table-column prop="projectAddr" label="项目地址" />
        <el-table-column prop="serviceType" label="服务类型" />
        <el-table-column prop="projectNum" label="项目数量" />
        <el-table-column prop="buildName" label="楼盘名称" />
        <el-table-column prop="structure" label="建筑结构" />
        <el-table-column prop="levelHigh" label="层高" />
        <el-table-column prop="designStorey" label="设计楼栋" />
        <el-table-column prop="houseType" label="户型结构" />
        <el-table-column prop="buildArea" label="建筑面积㎡" />
        <el-table-column prop="projectSituation" label="项目概况" />
        <el-table-column prop="openingDate" label="开盘时间" />
        <el-table-column prop="payHouseDate" label="交房时间" />
        <el-table-column prop="planCheckInDate" label="计划入住时间" />
        <el-table-column prop="hardBudget" label="硬装预算" />
        <el-table-column prop="softBudget" label="软装预算" />
        <el-table-column prop="fixedBudget" label="固装预算" />
        <el-table-column prop="companyName" label="公司名称" />
        <el-table-column prop="companyNature" label="公司性质" />
        <el-table-column prop="clientName" label="客户信息（姓名）" />
        <el-table-column prop="clientPhone" label="客户信息（联系电话）" />
        <el-table-column prop="clientJob" label="客户信息（职务）" />
        <el-table-column prop="clientWechat" label="客户信息（微信）" />
        <el-table-column prop="clientEmail" label="客户信息（邮箱）" />
        <el-table-column prop="clientGender" label="客户信息（性别）" />
        <el-table-column prop="styleRequire" label="风格需求" />
        <el-table-column prop="mainTone" label="主色调" />
        <el-table-column prop="secondTone" label="辅色调" />
        <el-table-column prop="nastyColor" label="讨厌的颜色" />
        <el-table-column prop="projectStatus" label="项目状态" />
        <el-table-column v-if="checkPer(['admin','loupanProjectSetupShopSummary:edit','loupanProjectSetupShopSummary:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudloupanProjectSetupShopSummary from '@/api/loupan/loupanProjectSetupShopSummary'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, country: null, lister: null, listerDate: null, ownerStore: null, enterRole: null, projectType: null, projectCode: null, projectName: null, projectNature: null, projectAddr: null, serviceType: null, projectNum: null, buildName: null, structure: null, levelHigh: null, designStorey: null, houseType: null, buildArea: null, projectSituation: null, openingDate: null, payHouseDate: null, planCheckInDate: null, hardBudget: null, softBudget: null, fixedBudget: null, companyName: null, companyNature: null, clientName: null, clientPhone: null, clientJob: null, clientWechat: null, clientEmail: null, clientGender: null, styleRequire: null, mainTone: null, secondTone: null, nastyColor: null, projectStatus: null }
export default {
  name: 'LoupanProjectSetupShopSummary',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '项目立项汇总(门店) ', url: 'api/loupanProjectSetupShopSummary', idField: 'id', sort: 'id,desc', crudMethod: { ...crudloupanProjectSetupShopSummary }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'loupanProjectSetupShopSummary:add'],
        edit: ['admin', 'loupanProjectSetupShopSummary:edit'],
        del: ['admin', 'loupanProjectSetupShopSummary:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'country', display_name: '国别' },
        { key: 'ownerStore', display_name: '归属门店/公司' },
        { key: 'enterRole', display_name: '入驻角色' },
        { key: 'projectCode', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' },
        { key: 'projectNature', display_name: '项目性质' },
        { key: 'projectAddr', display_name: '项目地址' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
