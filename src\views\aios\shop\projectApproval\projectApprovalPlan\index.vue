<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">申请人</label>
        <el-input v-model="query.nickName" clearable placeholder="申请人" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目编号</label>
        <el-input v-model="query.projectNumber" clearable placeholder="项目编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">项目名称</label>
        <el-input v-model="query.projectName" clearable placeholder="项目名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :title="crud.status.title" width="1000px" :before-close="crud.cancelCU" :visible="crud.status.cuv > 0" @update:visible="val => crud.status.cuv = val ? 2 : 0">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="130px">
          <!-- 项目基本信息 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-info" /> 项目基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目名称">
                  <el-input v-model="form.projectName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目编号">
                  <el-input v-model="form.projectNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目地址">
                  <el-input v-model="form.projectAddress" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目性质">
                  <el-select v-model="form.projectNature" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.project_nature"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="项目交期" prop="projectDeliveryPeriod">
                  <el-date-picker v-model="form.projectDeliveryPeriod" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目状态">
                  <el-select v-model="form.projectstate" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.projectstate"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <checkbox-field
              v-model="form.typeOfService"
              label="需求内容"
              field-name="typeOfService"
            />
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘名称">
                  <el-input v-model="form.buildingName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="楼盘编号" prop="buildingNumber">
                  <el-input v-model="form.buildingNumber" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="楼盘ID" prop="loupid">
                  <el-input v-model="form.loupid" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="建筑结构">
                  <el-select v-model="form.buildingStructure" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.building_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 面积与价格 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-office-building" /> 面积与价格</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="建筑面积㎡" prop="areaOfStructure">
                  <el-input v-model="form.areaOfStructure">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="套内面积㎡">
                  <el-input v-model="form.setOfArea">
                    <template slot="append">㎡</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="参考均价">
                  <el-input v-model="form.referenceAveragePrice">
                    <template slot="append">元/平</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="层高">
                  <el-input v-model="form.floorHeight" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 客户基本信息 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-user" /> 客户基本信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户姓名">
                  <el-input v-model="form.name" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="客户编号">
                  <el-input v-model="form.customerId" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="联系电话">
                  <el-input v-model="form.contactNumber" prop="contactNumber" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="手机号">
                  <el-input v-model="form.number" prop="number" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="性别">
                  <el-select v-model="form.gender" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.gender"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="微信">
                  <el-input v-model="form.wechat" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="邮箱">
                  <el-input v-model="form.email" prop="email" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="职务">
                  <el-input v-model="form.post" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="公司名称">
                  <el-input v-model="form.corporateName" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="公司性质">
                  <el-select v-model="form.corporateNatureX" filterable placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in dict.corporate_nature_x"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="客户属性">
                  <el-input v-model="form.clientProperty" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 家庭概况 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-house" /> 家庭概况</el-divider>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="户型结构">
                  <el-select v-model="form.huxingStructure" filterable placeholder="请选择">
                    <el-option
                      v-for="item in dict.huxing_structure"
                      :key="item.id"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <!-- 使用通用复选框组件 -->
            <checkbox-field
              v-model="form.memberOfFamily"
              label="家庭成员"
              field-name="memberOfFamily"
            />
            <checkbox-field
              v-model="form.spaceRequirement"
              label="空间需求"
              field-name="spaceRequirement"
            />
            <checkbox-field
              v-model="form.abstruse"
              label="玄关"
              field-name="abstruse"
            />
            <checkbox-field
              v-model="form.livingroom"
              label="客厅"
              field-name="livingroom"
            />
            <checkbox-field
              v-model="form.diningroom"
              label="餐厅"
              field-name="diningroom"
            />
            <checkbox-field
              v-model="form.kitchen"
              label="厨房"
              field-name="kitchen"
            />
            <checkbox-field
              v-model="form.puiblicBath"
              label="公共卫生间"
              field-name="puiblicBath"
            />
            <checkbox-field
              v-model="form.bedroom"
              label="主卧"
              field-name="bedroom"
            />
            <checkbox-field
              v-model="form.bathroomZw"
              label="主卧卫生间"
              field-name="bathroomZw"
            />
            <checkbox-field
              v-model="form.oldManRoom"
              label="老人房"
              field-name="oldManRoom"
            />
            <checkbox-field
              v-model="form.boyRoom"
              label="男孩房"
              field-name="boyRoom"
            />
            <checkbox-field
              v-model="form.girlRoom"
              label="女孩房"
              field-name="girlRoom"
            />
          </div>

          <!-- 材料要求 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-house" /> 材料要求</el-divider>
            <checkbox-field
              v-model="form.livingRoomMetope"
              label="客厅墙面"
              field-name="livingRoomMetope"
            />
            <checkbox-field
              v-model="form.livingRoomGround"
              label="客厅地面"
              field-name="livingRoomGround"
            />
            <checkbox-field
              v-model="form.bedroomwall"
              label="卧室墙面"
              field-name="bedroomwall"
            />
            <checkbox-field
              v-model="form.bedroomground"
              label="卧室地面"
              field-name="bedroomground"
            />
            <checkbox-field
              v-model="form.lightingRequirements"
              label="灯饰要求"
              field-name="lightingRequirements"
            />
            <checkbox-field
              v-model="form.sofaReq"
              label="沙发要求"
              field-name="sofaReq"
            />
          </div>

          <!-- 生活方式 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-s-order" /> 生活方式</el-divider>
            <checkbox-field
              v-model="form.intelligentSystem"
              label="智能系统"
              field-name="intelligentSystem"
            />
            <checkbox-field
              v-model="form.hotWater"
              label="热水方式"
              field-name="hotWater"
            />
            <checkbox-field
              v-model="form.refrigerationMethod"
              label="制冷方式"
              field-name="refrigerationMethod"
            />
            <checkbox-field
              v-model="form.heatingMethod"
              label="制热方式"
              field-name="heatingMethod"
            />
            <checkbox-field
              v-model="form.hostessHobby"
              label="女主人爱好"
              field-name="hostessHobby"
            />
            <checkbox-field
              v-model="form.maleMasterHobby"
              label="男主人爱好"
              field-name="maleMasterHobby"
            />
            <checkbox-field
              v-model="form.otherHobbies"
              label="其他成员爱好"
              field-name="otherHobbies"
            />
          </div>

          <!-- 设计要求 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-s-order" /> 设计要求</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="是否指定设计师">
                  <el-radio v-for="item in dict.if_designer" :key="item.id" v-model="form.designerDesignated" :label="item.value">{{ item.label }}</el-radio>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="指定设计师姓名">
                  <el-input v-model="form.designerName" />
                </el-form-item>
              </el-col>
            </el-row>
            <checkbox-field
              v-model="form.fengge"
              label="风格"
              field-name="fengge"
            />
            <checkbox-field
              v-model="form.renderingsSpace"
              label="做效果图空间"
              field-name="renderingsSpace"
            />
            <checkbox-field
              v-model="form.dominantHue"
              label="主色调"
              field-name="dominantHue"
            />
            <checkbox-field
              v-model="form.auxiliaryTone"
              label="辅助色调"
              field-name="auxiliaryTone"
            />
            <checkbox-field
              v-model="form.hateColor"
              label="讨厌的颜色"
              field-name="hateColor"
            />
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="设计要求及要点">
                  <el-input v-model="form.designReq" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="其他要求">
                  <el-input v-model="form.otherNeeds" type="textarea" :rows="2" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 预算信息 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-money" /> 预算信息</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="设计费预算" prop="designFeeBudget">
                  <el-input v-model="form.designFeeBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="项目总预算" prop="generalBudget">
                  <el-input v-model="form.generalBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="硬装预算" prop="hardPackBudget">
                  <el-input v-model="form.hardPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="固装预算" prop="fixedPackBudget">
                  <el-input v-model="form.fixedPackBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="软装预算" prop="softOutfitBudget">
                  <el-input v-model="form.softOutfitBudget">
                    <template slot="append">元</template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 日期安排 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-date" /> 日期安排</el-divider>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="开盘日期">
                  <el-date-picker v-model="form.openingDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="交房日期">
                  <el-date-picker v-model="form.deliveryDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="计划入住日期">
                  <el-date-picker v-model="form.jihua" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="概念方案需求日期">
                  <el-date-picker v-model="form.conceptplanrequirementdate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="深化方案需求日期">
                  <el-date-picker v-model="form.shrequirementdate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="预计装修开始时间">
                  <el-date-picker v-model="form.starttimeofrenovation" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="预计装修完成时间">
                  <el-date-picker v-model="form.timetofinish" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="制表日期">
                  <el-date-picker v-model="form.tabDate" type="datetime" style="width: 100%" />
                </el-form-item>
              </el-col>
            </el-row>
          </div>

          <!-- 附件信息 -->
          <div class="form-section">
            <el-divider content-position="left"><i class="el-icon-picture" /> 意向图片</el-divider>
            <div class="image-upload-container">
              <el-form-item label="意向图片">
                <general-file-upload
                  v-model="form.intentionPictures"
                  :field-name="'intentionPictures'"
                  v-bind="fileUploadConfig"
                  :use-minio-delete="true"
                  :hide-remove="isViewMode"
                  @change="handleFileChange('intentionPictures', $event)"
                  @file-change="handleFileListChange"
                />
              </el-form-item>
            </div>

            <el-dialog
              :visible="dialogVisible"
              append-to-body
              @update:visible="val => dialogVisible = val"
            >
              <img width="100%" :src="dialogImageUrl" alt="">
            </el-dialog>

          </div>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="validateForm">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="nickName" label="申请人" />
        <el-table-column prop="projectName" label="项目名称" />
        <el-table-column prop="projectAddress" label="项目地址" />
        <el-table-column prop="tabDate" label="制表日期" />
        <el-table-column prop="status" label="流程状态">
          <template slot-scope="scope">
            {{ dict.label.status[scope.row.status] }}
          </template>
        </el-table-column>
        <el-table-column v-if="checkPer(['admin','tumaiOaSiren:edit','tumaiOaSiren:del','tumaiOaSiren:view'])" label="操作" width="180px" align="center">
          <template slot-scope="scope">
            <aiosUDVOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudTumaiOaSiren from '@/api/aios/shop/tumaiOaSiren'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import aiosUDVOperation from '@/components/Aios/UDVOperation'
import { AiosViewButtonHelper } from '@/utils/aiosViewButtonHelper'
import crudOperation from '@crud/CRUD.operation'
import pagination from '@crud/Pagination'
import { subformFileHandlerMixin } from '@/utils/subformFileHandler'
import GeneralFileUpload from '@/components/GeneralFileUpload'
import { GeneralFileHandler } from '@/utils/generalFileUpload'
import { mapGetters } from 'vuex'
import CheckboxField from '@/components/CheckBox/CheckboxField'
import { deleteRecordFiles } from '@/utils/minioFileDeleter'

const defaultForm = { id: null, shopid: null, nickName: null, comid: null, oddNumbers: null, typeOfService: null, floorhEight: null, buildingStructure: null, corporateName: null, intentionalImages: null, name: null, post: null, wechat: null, email: null, gender: null, memberOfFamily: null, spaceRequirement: null, abstruse: null, ivingroom: null, diningroom: null, kitchen: null, puiblicBath: null, bedroom: null, lifestyle: null, intentionPictures: '[]', renderingsSpace: '[]', dominantHue: null, livingroom: null, projectNumber: null, projectName: null, contactNumber: null, floorHeight: null, mid: null, sort: null, content2: null, category2: null, highEnd2: null, responsibleperson: null, inTheEnd2: null, responsibleperson2: null, remarks3: null, projectstate: null, number: null, uid: null, optdt: null, optid: null, optname: null, applydt: null, explain: null, status: null, isturn: null, projectNature: null, projectAddress: null, buildingName: null, referenceAveragePrice: null, projectOverview: null, huxingStructure: null, projectDeliveryPeriod: null, projectTypeLx: null, buildingNumber: null, thenumberOfFamily: null, loupid: null, customerId: null, totalNumberHx: null, designFloor: null, designBuilding: null, bathroomZw: null, oldManRoom: null, boyRoom: null, girlRoom: null, livingRoomGround: null, livingRoomMetope: null, bedroomground: null, bedroomwall: null, lightingRequirements: null, sofaReq: null, refrigerationMethod: null, heatingMethod: null, maleMasterHobby: null, otherHobbies: null, designerDesignated: null, designFeeBudget: null, fengge: null, auxiliaryTone: null, hateColor: null, designReq: null, tabDate: null, areaOfStructure: null, setOfArea: null, openingDate: null, deliveryDate: null, jihua: null, hardPackBudget: null, fixedPackBudget: null, demandDate1: null, demandDate2: null, generalBudget: null, hotWater: null, hostessHobby: null, softOutfitBudget: null, fullCaseDesign: null, construction: null, ceramicTile: null, woodFloor: null, bathroom: null, stainlessSteel: null, slate: null, marble: null, doorsAndWindows: null, wholeHouseIntelligence: null, engineeringLightingFixtures: null, wholeHouseCustomization: null, electricalEquipment: null, softDecorationDesign: null, furniture: null, softLightingFixtures: null, carpet: null, hangingPaintings: null, curtain: null, mattress: null, jewelry: null, estimatedDecorationTime: null, washing: null, cleaningMethod: null, washUp: null, bathingMethod: null, cookingFrequency: null, cooker: null, habitOfWakingUpAtNight: null, intelligentSystem: null, shufang: null, zheGuangYaoQiu: null, spiritualNeeds: null, permanentPopulation: null, residencyYears: null, ageOfElders: null, husbandsAge: null, wifesAge: null, boysAge: null, girlsAge: null, bedroomFunctions: null, toiletFunctions: null, studyFunction: null, audioAndVideoRoomFunctions: null, spaSpaceFunction: null, sweatSteamRoomFunction: null, swimmingPoolFunctions: null, storageRoomFunctions: null, storageRequirementsAndFunctions: null, internalCall: null, clientProperty: null, conceptplanrequirementdate: null, shrequirementdate: null, starttimeofrenovation: null, timetofinish: null, designerName: null, otherNeeds: null, createtime: null, corporateNatureX: null }
export default {
  name: 'TumaiOaSiren',
  components: { pagination, crudOperation, rrOperation, GeneralFileUpload, CheckboxField, aiosUDVOperation },
  mixins: [presenter(), header(), form(defaultForm), crud(), GeneralFileHandler.mixin, subformFileHandlerMixin, AiosViewButtonHelper.createViewMixin({
    hasFileFields: true,
    hasSubFormData: true
  })],
  dicts: ['gender', 'building_structure', 'projectstate', 'status', 'project_nature', 'if_designer', 'corporate_nature_x', 'huxing_structure'],
  cruds() {
    return CRUD({ title: '项目立项（适用楼盘预案，私人业主全案）', url: 'api/shop/tumaiOaSiren', idField: 'id', sort: 'id,desc', crudMethod: { ...crudTumaiOaSiren }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'tumaiOaSiren:add'],
        edit: ['admin', 'tumaiOaSiren:edit'],
        del: ['admin', 'tumaiOaSiren:del'],
        view: ['admin', 'tumaiOaSiren:edit', 'tumaiOaSiren:view']
      },
      // 图片上传相关
      dialogImageUrl: '',
      dialogVisible: false,
      // 添加文件字段列表和上传配置
      fileFields: [
        'intentionPictures',
        'renderingsSpace'
      ],
      // 文件上传组件通用配置
      fileUploadConfig: {
        accept: 'image/*,.pdf,.doc,.docx,.xls,.xlsx,.zip,.rar',
        maxFiles: 10,
        tipText: '支持上传图片、PDF、Word、Excel等文件',
        buttonText: '上传文件',
        listType: 'text',
        useMinioDelete: true // 启用组件内部删除功能
      },
      rules: {
        shopid: [
          { required: true, message: '不能为空', trigger: 'blur' }
        ],
        nickName: [
          { required: true, message: '申请人不能为空', trigger: 'blur' }
        ],
        typeOfService: [
          { required: true, message: '需求内容不能为空', trigger: 'blur' }
        ],
        projectDeliveryPeriod: [
          { required: true, message: '项目交期不能为空', trigger: 'blur' }
        ],
        buildingNumber: [
          { required: true, message: '楼盘编号不能为空', trigger: 'blur' }
        ],
        designFeeBudget: [
          { required: true, message: '设计费预算(元)不能为空', trigger: 'blur' }
        ],
        areaOfStructure: [
          { required: true, message: '建筑面积㎡不能为空', trigger: 'blur' }
        ],
        hardPackBudget: [
          { required: true, message: '硬装预算(元)不能为空', trigger: 'blur' }
        ],
        fixedPackBudget: [
          { required: true, message: '固装预算(元)不能为空', trigger: 'blur' }
        ],
        generalBudget: [
          { required: true, message: '项目总预算(元)不能为空', trigger: 'blur' }
        ],
        softOutfitBudget: [
          { required: true, message: '软装预算(元)不能为空', trigger: 'blur' }
        ],
        contactNumber: [
          { required: true, message: '联系电话不能为空', trigger: 'blur' },
          {
            pattern: /^($\d{3,4}$|\d{3,4}-|\s)?\d{7,14}$/,
            message: '格式示例：021-12345678 或 057712345678',
            trigger: 'blur'
          }
        ],
        number: [
          { required: true, message: '手机号不能为空', trigger: 'blur' },
          {
            pattern: /^1[3-9]\d{9}$/,
            message: '请输入11位有效手机号码',
            trigger: 'blur'
          }
        ],
        email: [
          { required: true, message: '邮箱不能为空', trigger: 'blur' },
          {
            type: 'email',
            message: '请输入有效的邮箱地址',
            trigger: 'blur'
          }
        ],
        loupid: [
          {
            pattern: /^[1-9]\d*$/,
            message: '请输入正整数',
            trigger: 'blur'
          }
        ]
      },

      queryTypeOptions: [
        { key: 'nickName', display_name: '申请人' },
        { key: 'projectNumber', display_name: '项目编号' },
        { key: 'projectName', display_name: '项目名称' }
      ]
    }
  },
  computed: {
    ...mapGetters(['minioUploadApi', 'minioDeleteApi']),
    // 判断是否处于查看模式
    isViewMode() {
      return this.crud.status.view === 1 // CRUD.STATUS.PREPARED
    }
  },
  created() {
    // 初始化文件字段
    this.initGeneralFileFields(this.fileFields)

    // 检查minioDeleteApi是否可用
    if (!this.minioDeleteApi) {
      console.warn('警告: minioDeleteApi未定义，文件删除功能可能无法正常工作')
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    },

    // 钩子：编辑前的操作
    [CRUD.HOOK.beforeToEdit](crud, form) {
      console.log('编辑前原始数据:', form)

      // 获取并初始化文件字段
      this.initGeneralFileFields(this.fileFields)

      console.log('初始化后表单数据:', form)
    },

    // 钩子：添加前的操作
    [CRUD.HOOK.beforeToAdd]() {
      // 初始化文件字段为空数组
      this.form.intentionPictures = '[]'
      this.form.renderingsSpace = '[]'
    },

    // 钩子：删除前的操作 - 删除关联的MinIO文件
    [CRUD.HOOK.beforeDelete](crud, data) {
      deleteRecordFiles(data, ['intentionPictures'])
      return true
    },

    // 钩子：提交前的操作
    [CRUD.HOOK.beforeSubmit]() {
      try {
        // 记录处理前的值，用于调试
        console.log('提交前原始URL字段值:', {
          intentionPictures: this.form.intentionPictures,
          renderingsSpace: this.form.renderingsSpace
        })

        // 使用通用方法处理文件字段
        this.prepareGeneralFileFields(this.fileFields)

        // 记录最终提交的值，用于调试
        console.log('最终提交的URL字段值:', {
          intentionPictures: this.form.intentionPictures,
          renderingsSpace: this.form.renderingsSpace
        })

        return true
      } catch (error) {
        console.error('提交前处理数据出错:', error)
        this.$message.error('提交数据处理失败，请检查表单数据')
        return false
      }
    },

    // 文件列表变更处理
    handleFileListChange(event) {
      try {
        // 使用GeneralFileHandler的处理方法
        const result = this.handleGeneralFileChange(event, this.form)

        // 如果是删除操作，记录日志并确保表单值已更新
        if (event.action === 'remove' && event.file && event.file.url) {
          const fieldName = event.fieldName
          console.log(`文件已删除: ${event.file.url}, 字段: ${fieldName}`)
        }

        return result
      } catch (error) {
        console.error('文件处理过程中发生错误:', error)
        this.$message.error('文件处理出错，请重试')
        return false
      }
    },

    // 处理文件变更，直接更新表单值
    handleFileChange(fieldName, files) {
      if (Array.isArray(files)) {
        // 将数组转换为JSON字符串存储
        const jsonStr = JSON.stringify(files)
        this.form[fieldName] = jsonStr
        console.log(`字段${fieldName}更新为:`, this.form[fieldName])
      }
    },

    // 图片预览
    handlePreview(file) {
      this.dialogImageUrl = file.url || URL.createObjectURL(file.raw)
      this.dialogVisible = true
    },

    // 使用封装的表单验证工具
    validateForm() {
      this.$validateFormAndLocate(this.$refs.form, () => {
        this.crud.submitCU()
      })
    }
  }
}
</script>

<style scoped>
.form-section-title {
  font-size: 13px;
  font-weight: bold;
  margin-bottom: 3px;
}

.form-section {
  margin-bottom: 5px;
  padding: 5px 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.01);
}

.image-upload-container {
  display: flex;
  margin-bottom: 5px;
}

.image-upload-container .el-form-item {
  width: 100%;
}

.image-upload-container .el-upload--picture-card,
.el-upload--picture-card {
  width: 130px;
  height: 130px;
  line-height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.image-upload-container .el-upload-list--picture-card .el-upload-list__item,
.el-upload-list--picture-card .el-upload-list__item {
  width: 130px;
  height: 130px;
  margin-right: 5px;
  margin-bottom: 5px;
}

.filter-item {
  margin: 0 3px;
}

.pagination-container {
  background: #fff;
  padding: 4px;
  margin-top: 5px;
  text-align: right;
}

.el-divider {
  margin: 2px 0 5px;
}

.el-divider__text {
  font-size: 13px;
  font-weight: bold;
  color: #409EFF;
  padding: 0 5px;
  background-color: #fff;
}

/* 调整表单元素的间距 */
.el-form-item {
  margin-bottom: 5px;
}

/* 调整行间距 */
.el-row {
  margin-bottom: 0 !important;
  margin-top: 0 !important;
}

.el-row + .el-row {
  margin-top: 2px !important;
}

/* 减少表单项标签和输入框之间的间距 */
.el-form-item__label {
  padding-right: 2px;
  line-height: 28px;
  font-size: 12px;
}

/* 表单输入控件高度调整 */
.el-input__inner, .el-select .el-input__inner {
  height: 28px;
  line-height: 28px;
}

.el-input__icon, .el-select .el-input__icon {
  line-height: 28px;
}

/* 减少弹窗内部padding */
.el-dialog__body {
  padding: 8px 12px !important;
}

/* 减少dialog footer的padding */
.el-dialog__footer {
  padding: 5px 12px;
}

/* 减少el-dialog的标题栏padding */
.el-dialog__header {
  padding: 8px 12px;
}

/* 减少表单项内容区域的内边距 */
.el-form-item__content {
  line-height: 28px;
}

/* 调整日期选择器高度 */
.el-date-editor.el-input
.el-date-editor.el-input__inner {
  width: 100%;
  height: 28px;
  line-height: 28px;
}

/* 调整表格行高 */
.el-table td, .el-table th {
  padding: 4px 0;
}

/* 增强错误提示显示效果 */
::v-deep .el-form-item__error {
  position: absolute !important;
  top: calc(100% + 2px) !important;
  left: 0 !important;
  margin: 0 !important;
  line-height: 1.2;
  transform: translateY(-2px);
  z-index: 2;
  background: rgba(255,255,255,0.9);
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
</style>
