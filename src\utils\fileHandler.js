// import axios from 'axios'
// import { getToken } from '@/utils/auth'
// import { Message } from 'element-ui'

/**
 * 统一的文件处理工具
 */

/**
 * 从URL中提取文件名
 * @param {string} url - 文件URL
 * @returns {string} - 文件名
 */
function getFileNameFromUrl(url) {
  if (!url) return '未知文件'
  try {
    const parts = url.split('/')
    const fileName = parts[parts.length - 1]
    // 如果文件名包含扩展名，直接返回
    if (fileName && fileName.includes('.')) {
      return fileName
    }
    // 如果没有扩展名，尝试从URL中推断
    return fileName || '未知文件'
  } catch (e) {
    console.error('提取文件名失败:', e)
    return '未知文件'
  }
}

/**
 * 初始化附件数据数组，确保字段是JSON格式
 * @param {Array} dataArray - 数据数组
 * @param {string} attachmentField - 附件字段名称
 */
export function initAttachments(dataArray, attachmentField = 'attachment') {
  if (!Array.isArray(dataArray)) return

  dataArray.forEach(item => {
    if (!item[attachmentField]) {
      item[attachmentField] = '[]'
    } else if (typeof item[attachmentField] === 'string' && !item[attachmentField].startsWith('[')) {
      // 如果是URL字符串，转换为JSON格式
      try {
        const urls = item[attachmentField].split(',').filter(url => url.trim())
        const fileList = urls.map((url) => {
          return {
            name: getFileNameFromUrl(url),
            url: url
          }
        })
        item[attachmentField] = JSON.stringify(fileList)
      } catch (e) {
        console.error('转换附件格式失败:', e)
        item[attachmentField] = '[]'
      }
    }
  })
}

/**
 * 处理文件变更事件
 * @param {Object} event - 文件变更事件
 * @param {Object} row - 要更新的数据行
 * @param {Function} deleteApi - 删除文件的API
 * @param {Object} vueInstance - Vue实例
 * @returns {Promise} - 处理完成的Promise
 */
export function handleFileChange(event, row, deleteApi, vueInstance) {
  return new Promise((resolve, reject) => {
    try {
      if (!event || !row) {
        return resolve(false)
      }

      const { action, fieldName, file } = event

      // 上传成功
      if (action === 'success' && file && file.response) {
        const url = file.response.data

        if (!url) {
          vueInstance.$message.error('上传失败：未获取到文件URL')
          return resolve(false)
        }

        // 更新字段值
        if (fieldName && row[fieldName]) {
          try {
            let fileList = []

            // 解析现有数据
            try {
              fileList = JSON.parse(row[fieldName])
              if (!Array.isArray(fileList)) fileList = []
            } catch (e) {
              fileList = []
              console.error('解析文件列表失败:', e)
            }

            // 添加新文件
            fileList.push({
              name: file.name || `附件${fileList.length + 1}`,
              url: url
            })

            // 更新数据
            row[fieldName] = JSON.stringify(fileList)
            console.log(`已更新字段 ${fieldName} 值:`, row[fieldName])

            return resolve(true)
          } catch (e) {
            console.error('更新文件列表失败:', e)
            vueInstance.$message.error('更新文件列表失败')
            return resolve(false)
          }
        }
      }

      // 删除文件
      if (action === 'remove' && fieldName && file && file.url) {
        try {
          // 从URL中提取文件路径
          const fileUrl = file.url
          const pathMatch = fileUrl.match(/\/([^/]+)$/)

          if (pathMatch && pathMatch[1] && deleteApi) {
            const fileName = pathMatch[1]

            // 调用删除API
            deleteApi({ fileName: fileName }).then(() => {
              vueInstance.$message.success('文件删除成功')

              // 更新字段值
              if (row[fieldName]) {
                try {
                  let fileList = JSON.parse(row[fieldName])
                  if (!Array.isArray(fileList)) fileList = []

                  // 过滤已删除的文件
                  fileList = fileList.filter(item => item.url !== fileUrl)
                  row[fieldName] = JSON.stringify(fileList)

                  console.log(`已更新字段 ${fieldName} 值:`, row[fieldName])
                  return resolve(true)
                } catch (e) {
                  console.error('更新文件列表失败:', e)
                  return resolve(false)
                }
              }
            }).catch(error => {
              console.error('删除文件失败:', error)
              vueInstance.$message.error('删除文件失败')
              return resolve(false)
            })
          } else {
            console.warn('无法从URL提取文件名或未提供删除API')
            return resolve(false)
          }
        } catch (e) {
          console.error('删除文件处理失败:', e)
          return resolve(false)
        }
      }

      return resolve(true)
    } catch (error) {
      console.error('文件变更处理失败:', error)
      return reject(error)
    }
  })
}

/**
 * 表单数据附件处理 - 统一的处理方法
 * @param {Object} form - 表单数据对象
 * @param {Array} fieldNames - 要处理的字段名称数组
 * @param {string} mode - 处理模式: 'init'(初始化) 或 'submit'(提交前)
 * @returns {boolean} - 处理是否成功
 */
export function processFormAttachments(form, fieldNames, mode = 'init') {
  if (!form || !Array.isArray(fieldNames)) return false

  try {
    fieldNames.forEach(field => {
      if (!form[field]) {
        // 设置默认值
        form[field] = mode === 'init' ? '[]' : ''
        return
      }

      if (mode === 'init') {
        // 初始化模式: URL字符串 -> JSON格式
        if (typeof form[field] === 'string' && !form[field].startsWith('[')) {
          try {
            // 检查是否是空字符串
            if (!form[field].trim()) {
              form[field] = '[]'
              return
            }

            // 将URL字符串转换为JSON格式
            const urls = form[field].split(',').filter(url => url.trim())
            const fileList = urls.map((url) => {
              return {
                name: getFileNameFromUrl(url),
                url: url
              }
            })

            form[field] = JSON.stringify(fileList)
            console.log(`初始化转换字段 ${field} 从URL为JSON:`, form[field])
          } catch (e) {
            console.error(`初始化转换字段 ${field} 失败:`, e)
            form[field] = '[]'
          }
        }
      } else if (mode === 'submit') {
        // 提交模式: JSON格式 -> URL字符串
        if (typeof form[field] === 'string' && form[field].startsWith('[')) {
          try {
            const fileList = JSON.parse(form[field])
            if (Array.isArray(fileList) && fileList.length > 0) {
              const urls = fileList.map(item => item.url).filter(url => url).join(',')
              console.log(`提交前转换字段 ${field} 从JSON为URL:`, urls)
              form[field] = urls
            } else {
              form[field] = ''
            }
          } catch (e) {
            console.error(`提交前转换字段 ${field} 失败:`, e)
            if (!form[field].includes('http')) {
              form[field] = ''
            }
            // 否则保持原值
          }
        }
      }
    })

    return true
  } catch (error) {
    console.error('处理表单附件失败:', error)
    return false
  }
}

/**
 * 处理子表单(数组)附件数据
 * @param {Object} form - 主表单数据
 * @param {string} arrayField - 子表单数组字段名
 * @param {string} attachmentField - 附件字段名称
 * @param {string} targetField - 目标字段名称(可选，默认与attachmentField相同)
 * @param {string} mode - 处理模式: 'init'(初始化) 或 'submit'(提交前)
 * @returns {boolean} - 处理是否成功
 */
export function processSubFormAttachments(form, arrayField, attachmentField, targetField = attachmentField, mode = 'init') {
  if (!form || !arrayField || !attachmentField) return false

  try {
    // 获取子表单数组
    let dataArray = []

    if (form[arrayField]) {
      try {
        if (typeof form[arrayField] === 'string') {
          dataArray = JSON.parse(form[arrayField])
        } else if (Array.isArray(form[arrayField])) {
          dataArray = form[arrayField]
        }

        if (!Array.isArray(dataArray)) {
          dataArray = []
        }
      } catch (e) {
        console.error(`解析子表单数组 ${arrayField} 失败:`, e)
        dataArray = []
      }
    }

    // 处理每个子表单项
    dataArray.forEach(item => {
      if (mode === 'init') {
        // 初始化模式
        if (!item[attachmentField]) {
          item[attachmentField] = '[]'
        } else if (typeof item[attachmentField] === 'string' && !item[attachmentField].startsWith('[')) {
          try {
            // 将URL字符串转换为JSON格式
            const urls = item[attachmentField].split(',').filter(url => url.trim())
            const fileList = urls.map((url) => {
              return {
                name: getFileNameFromUrl(url),
                url: url
              }
            })

            item[attachmentField] = JSON.stringify(fileList)
            console.log(`初始化子表单字段 ${attachmentField} 从URL为JSON:`, item[attachmentField])
          } catch (e) {
            console.error(`初始化子表单字段 ${attachmentField} 失败:`, e)
            item[attachmentField] = '[]'
          }
        }

        // 如果targetField与attachmentField不同，同步数据
        if (targetField !== attachmentField) {
          item[targetField] = item[attachmentField]
        }
      } else if (mode === 'submit') {
        // 提交模式
        if (typeof item[attachmentField] === 'string' && item[attachmentField].startsWith('[')) {
          try {
            const fileList = JSON.parse(item[attachmentField])
            if (Array.isArray(fileList) && fileList.length > 0) {
              const urls = fileList.map(file => file.url).filter(url => url).join(',')

              // 同步到目标字段
              item[targetField] = urls || ''
              console.log(`提交前转换子表单字段 ${attachmentField} 从JSON为URL:`, urls)
            } else {
              item[targetField] = ''
            }
          } catch (e) {
            console.error(`提交前转换子表单字段 ${attachmentField} 失败:`, e)
            if (!item[attachmentField].includes('http')) {
              item[targetField] = ''
            } else {
              item[targetField] = item[attachmentField]
            }
          }
        }
      }
    })

    // 同步回表单
    if (mode === 'submit') {
      form[arrayField] = JSON.stringify(dataArray)
    }

    return true
  } catch (error) {
    console.error('处理子表单附件失败:', error)
    return false
  }
}

/**
 * 文件处理Mixin - 基础版
 */
export const fileHandlerMixin = {
  methods: {
    /**
     * 处理文件变更事件
     */
    handleFileChange(event, row) {
      return handleFileChange(event, row, this.minioDeleteApi, this)
    }
  }
}

/**
 * 增强版文件处理Mixin - 提供更完整的文件处理功能
 */
export const enhancedFileHandlerMixin = {
  methods: {
    /**
     * 处理文件变更事件
     */
    handleFileChange(event, row) {
      return handleFileChange(event, row, this.minioDeleteApi, this)
    },

    /**
     * 初始化表单附件字段
     * @param {Array} fieldNames - 要初始化的字段名数组
     * @returns {boolean} - 初始化是否成功
     */
    initFormAttachments(fieldNames) {
      console.log('初始化表单附件字段:', fieldNames)
      return processFormAttachments(this.form, fieldNames, 'init')
    },

    /**
     * 准备表单附件字段用于提交
     * @param {Array|String} fields - 字段名或字段名数组
     * @returns {boolean} - 准备是否成功
     */
    prepareFormAttachments(fieldNames) {
      console.log('准备表单附件字段用于提交:', fieldNames)
      return processFormAttachments(this.form, fieldNames, 'submit')
    },

    /**
     * 初始化子表单附件字段
     * @param {string} arrayField - 子表单数组字段名
     * @param {string} attachmentField - 附件字段名
     * @param {string} targetField - 目标字段名(可选)
     * @returns {boolean} - 初始化是否成功
     */
    initSubFormAttachments(arrayField, attachmentField, targetField = attachmentField) {
      console.log('初始化子表单附件字段:', { arrayField, attachmentField, targetField })
      return processSubFormAttachments(this.form, arrayField, attachmentField, targetField, 'init')
    },

    /**
     * 准备子表单附件字段用于提交
     * @param {string} arrayField - 子表单数组字段名
     * @param {string} attachmentField - 附件字段名
     * @param {string} targetField - 目标字段名(可选)
     * @returns {boolean} - 准备是否成功
     */
    prepareSubFormAttachments(arrayField, attachmentField, targetField = attachmentField) {
      console.log('准备子表单附件字段用于提交:', { arrayField, attachmentField, targetField })
      return processSubFormAttachments(this.form, arrayField, attachmentField, targetField, 'submit')
    }
  }
}

/**
 * 简化版附件处理工具 - 专注于JSON和URL字符串的转换及简单的文件变更处理
 */
export const SimpleFileHandler = {
  /**
   * 初始化表单的附件字段（将URL字符串转为JSON格式）
   * @param {Object} form - 表单数据
   * @param {Array|String} fields - 字段名或字段名数组
   * @returns {Object} - 处理后的表单
   */
  initFields(form, fields) {
    if (!form) return form

    // 确保fields是数组
    const fieldArray = Array.isArray(fields) ? fields : [fields]

    fieldArray.forEach(field => {
      if (!form[field]) {
        form[field] = '[]'
        return
      }

      // 如果已经是JSON格式，则验证
      if (typeof form[field] === 'string' && form[field].startsWith('[')) {
        try {
          // 验证JSON有效性
          JSON.parse(form[field])
        } catch (e) {
          console.warn(`字段${field}的JSON格式无效，重置为空数组`)
          form[field] = '[]'
        }
        return
      }

      // 如果是URL字符串，转换为JSON
      if (typeof form[field] === 'string') {
        try {
          const urls = form[field].split(',').filter(url => url.trim())
          if (urls.length === 0) {
            form[field] = '[]'
            return
          }

          const fileList = urls.map((url) => ({
            name: getFileNameFromUrl(url),
            url: url
          }))

          form[field] = JSON.stringify(fileList)
        } catch (e) {
          console.warn(`转换字段${field}失败`, e)
          form[field] = '[]'
        }
      } else {
        form[field] = '[]'
      }
    })

    return form
  },

  /**
   * 准备表单附件字段用于提交（将JSON格式转为URL字符串）
   * @param {Object} form - 表单数据
   * @param {Array|String} fields - 字段名或字段名数组
   * @returns {Object} - 处理后的表单
   */
  prepareFields(form, fields) {
    if (!form) return form

    // 确保fields是数组
    const fieldArray = Array.isArray(fields) ? fields : [fields]

    fieldArray.forEach(field => {
      if (!form[field]) {
        form[field] = ''
        return
      }

      // 如果是JSON格式，转换为URL字符串
      if (typeof form[field] === 'string' && form[field].startsWith('[')) {
        try {
          const fileList = JSON.parse(form[field])
          if (Array.isArray(fileList) && fileList.length > 0) {
            const urls = fileList.map(item => item.url).filter(url => url)
            form[field] = urls.join(',')
          } else {
            form[field] = ''
          }
        } catch (e) {
          console.warn(`转换字段${field}失败`, e)
          form[field] = ''
        }
      }
    })

    return form
  },

  /**
   * 处理文件变更事件
   * @param {Object} event - 文件变更事件
   * @param {Object} data - 要更新的数据对象
   * @param {Function} deleteApi - 删除文件的API
   * @param {Object} vueInstance - Vue实例(可选)
   * @returns {Object} - 更新后的数据对象
   */
  handleChange(event, data, deleteApi, vueInstance) {
    if (!event || !data) return data

    // 记录原始事件对象，便于调试
    console.log('SimpleFileHandler.handleChange接收到事件:', JSON.stringify(event))

    // 从事件中获取信息
    const { file, action } = event

    // 尝试获取字段名
    let fieldName = null

    // 方法1: 从事件中直接获取
    if (event.field || event.fieldName) {
      fieldName = event.field || event.fieldName
    } else if (event.fieldValue !== undefined) {
      // 方法2: 从fieldValue属性获取
      fieldName = 'fieldValue'
    } else if (event.fileList) {
      // 方法3: 对象只有一个可能的附件字段
      // 查找data中可能是JSON数组的字段
      const jsonFields = Object.keys(data).filter(key => {
        return data[key] && typeof data[key] === 'string' && data[key].startsWith('[')
      })

      if (jsonFields.length === 1) {
        fieldName = jsonFields[0]
      }
    }

    if (!fieldName) {
      console.warn('无法确定字段名，不进行处理', event)
      return data
    }

    // 文件上传成功
    if (action === 'success' && file && file.response) {
      // 支持多种响应格式
      let url = null

      // 尝试从不同位置获取URL
      if (typeof file.response === 'string' && file.response.includes('http')) {
        // 直接是URL字符串
        url = file.response
      } else if (file.response.url) {
        // 有url属性
        url = file.response.url
      } else if (file.response.data) {
        // 有data属性
        url = file.response.data
      }

      // 如果找不到URL，再检查事件的其他位置
      if (!url && event.url) {
        url = event.url
      }

      if (!url) {
        console.warn('上传失败：未获取到文件URL', file.response)
        if (vueInstance && vueInstance.$message) {
          vueInstance.$message.warning('未能获取文件URL，请重试')
        }
        return data
      }

      try {
        // 解析现有字段数据
        let fileList = []
        try {
          fileList = JSON.parse(data[fieldName] || '[]')
          if (!Array.isArray(fileList)) fileList = []
        } catch (e) {
          console.warn('解析文件列表失败，重置为空数组', e)
          fileList = []
        }

        // 添加新文件
        fileList.push({
          name: file.name || `附件${fileList.length + 1}`,
          url: url
        })

        // 更新数据
        data[fieldName] = JSON.stringify(fileList)

        // 添加日志
        console.log(`已更新字段 ${fieldName}，添加文件URL: ${url}`)
        console.log(`更新后的字段值: ${data[fieldName]}`)
      } catch (e) {
        console.warn('更新文件列表失败', e)
        if (vueInstance && vueInstance.$message) {
          vueInstance.$message.error('处理上传文件数据失败')
        }
      }
    }

    // 文件删除
    if (action === 'remove' && file) {
      try {
        // 解析现有字段数据
        let fileList = []
        try {
          fileList = JSON.parse(data[fieldName] || '[]')
          if (!Array.isArray(fileList)) fileList = []
        } catch (e) {
          fileList = []
        }

        // 从URL中提取文件路径
        if (file.url && deleteApi) {
          const fileUrl = file.url

          // 重要变更：传递完整URL以便后端正确解析
          deleteApi({ fileName: fileUrl })
            .then(() => {
              if (vueInstance) {
                vueInstance.$message.success('文件删除成功')
              } else {
                console.log('文件删除成功:', fileUrl)
              }
            })
            .catch(error => {
              console.error('删除文件失败:', error)
              if (vueInstance) {
                vueInstance.$message.error('删除文件失败')
              }
            })
        } else {
          console.warn('文件URL不存在或未提供删除API')
        }

        // 删除文件（根据URL匹配）
        fileList = fileList.filter(item => item.url !== file.url)

        // 更新数据
        data[fieldName] = JSON.stringify(fileList)
      } catch (e) {
        console.warn('更新文件列表失败', e)
      }
    }

    return data
  },

  /**
   * 简化版Mixin - 提供附件处理方法
   */
  mixin: {
    methods: {
      /**
       * 初始化表单附件字段
       * @param {Array|String} fields - 字段名或字段名数组
       */
      initAttachmentFields(fields) {
        if (!this.form) return
        SimpleFileHandler.initFields(this.form, fields)
      },

      /**
       * 准备表单附件字段用于提交
       * @param {Array|String} fields - 字段名或字段名数组
       */
      prepareAttachmentFields(fields) {
        if (!this.form) return
        SimpleFileHandler.prepareFields(this.form, fields)
      },

      /**
       * 处理文件变更事件
       * @param {Object} event - 文件变更事件
       * @param {Object} data - 要更新的数据对象
       */
      handleAttachmentChange(event, data) {
        return SimpleFileHandler.handleChange(event, data || this.form, this.minioDeleteApi, this)
      }
    }
  }
}
