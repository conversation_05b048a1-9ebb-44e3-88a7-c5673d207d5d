/**
 * MinIO文件删除工具
 */

import request from '@/utils/request'

/**
 * 删除数据记录关联的MinIO文件
 * @param {Object} data - 要删除的数据记录
 * @param {Array} fileFields - 包含文件的字段名数组
 */
export function deleteRecordFiles(data, fileFields) {
  if (!data || !fileFields) {
    console.warn('删除文件参数不完整')
    return
  }

  fileFields.forEach(field => {
    if (data[field]) {
      try {
        let files = data[field]

        // 如果是URL字符串，处理单个或多个URL（逗号分隔）
        if (typeof files === 'string' && files.startsWith('http')) {
          const urls = files.split(',').map(url => url.trim())
          urls.forEach(url => {
            if (url) {
              request({ url: `/api/minio/delete?fileName=${encodeURIComponent(url)}`, method: 'get' })
                .then(response => {
                  console.log('文件删除成功:', url, response)
                })
                .catch(err => {
                  console.error('删除失败:', url, err)
                })
            }
          })
        } else if (typeof files === 'string' && files.startsWith('[')) {
          files = JSON.parse(files)
          console.log(`解析后的文件:`, files)
          if (Array.isArray(files)) {
            files.forEach(file => {
              if (file.url) {
                request({ url: `/api/minio/delete?fileName=${encodeURIComponent(file.url)}`, method: 'get' })
                  .then(response => console.log('文件删除成功:', file.url))
                  .catch(err => console.error('删除失败:', file.url, err))
              }
            })
          }
        } else if (Array.isArray(files)) {
          files.forEach(file => {
            if (file.url) {
              request({ url: `/api/minio/delete?fileName=${encodeURIComponent(file.url)}`, method: 'get' })
                .then(response => console.log('文件删除成功:', file.url))
                .catch(err => console.error('删除失败:', file.url, err))
            }
          })
        }
      } catch (error) {
        console.error('删除文件失败:', field, error)
      }
    }
  })
}

/**
 * 删除子表单中的文件
 * @param {Object} data - 要删除的数据记录
 * @param {String} subformField - 子表单字段名（如：'drawingVersions'）
 * @param {Array|String} fileFields - 子表单中包含文件的字段名数组或单个字段名（如：['pdfFormatFile'] 或 'pdfFormatFile'）
 */
export function deleteSubformFiles(data, subformField, fileFields) {
  if (!data || !subformField || !fileFields) {
    console.warn('删除子表单文件参数不完整')
    return
  }

  if (!data[subformField]) {
    console.log(`子表单字段 ${subformField} 不存在或为空`)
    return
  }

  try {
    // 解析子表单数据
    const subformData = JSON.parse(data[subformField])
    if (!Array.isArray(subformData)) {
      console.warn(`子表单字段 ${subformField} 不是数组格式`)
      return
    }

    console.log(`开始删除子表单 ${subformField} 中的文件，数据:`, subformData)

    // 确保fileFields是数组
    const fields = Array.isArray(fileFields) ? fileFields : [fileFields]

    // 遍历每个子表单记录，删除其中的文件
    subformData.forEach((item, index) => {
      if (!item) return
      // 删除该项中的所有指定文件字段
      fields.forEach(field => {
        if (item[field]) {
          console.log(`删除子表单项[${index}]的${field}文件:`, item[field])

          // 使用现有的deleteRecordFiles函数处理单个子表单项的文件
          deleteRecordFiles(item, [field])
        }
      })
    })
  } catch (error) {
    console.error(`删除子表单 ${subformField} 文件失败:`, error)
  }
}

/**
 * 删除子表单中指定行的文件
 * @param {Object} rowData - 要删除的子表单行数据
 * @param {Array|String} fileFields - 该行中包含文件的字段名数组或单个字段名（如：['pdfFormaFile1'] 或 'pdfFormaFile1'）
 */
export function deleteSubformRowFiles(rowData, fileFields) {
  if (!rowData || !fileFields) {
    console.warn('删除子表单行文件参数不完整')
    return
  }

  console.log('开始删除子表单行文件，行数据:', rowData)

  // 确保fileFields是数组
  const fields = Array.isArray(fileFields) ? fileFields : [fileFields]

  // 删除该行中的所有指定文件字段
  fields.forEach(field => {
    if (rowData[field]) {
      console.log(`删除子表单行的${field}文件:`, rowData[field])

      // 使用现有的deleteRecordFiles函数处理单个子表单项的文件
      deleteRecordFiles(rowData, [field])
    }
  })
}
