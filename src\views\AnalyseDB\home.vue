<template>
  <div class="main-layout">
    <DatabaseSelector @table-selected="handleTableSelected" />
    <TableData
      :db-name="selectedDbName"
      :table-name="selectedTableName"
      :page="currentPage"
      :size="pageSize"
      @table-data="handleTableData"
      @update:size="handleSizeChange"
      @update:page="handlePageChange"
    />
    <AiChat
      :table-list="tableList"
      :selected-tables="selectedTables"
      :selected-tables-data="selectedTablesData"
      @select-table="handleTableSelected"
      @clear-table="clearSelectedTable"
      @toggle-table-pin="toggleTablePin"
      @update-selected-tables="handleUpdateSelectedTables"
    />
  </div>
</template>

<script>
import DatabaseSelector from './DatabaseSelector.vue'
import TableData from './TableData.vue'
import AiChat from './aichat.vue'

export default {
  name: 'AnalyseDBHome',
  components: {
    DatabaseSelector,
    TableData,
    AiChat
  },
  data() {
    return {
      selectedDbName: '',
      selectedTableName: '', // 保留用于兼容TableData组件
      selectedTables: [], // 存储多个选中的表信息 [{tableName, headers, data, isPinned, isDefault}]
      selectedTablesData: {}, // 存储多个表的数据 {tableName: {headers: [], data: []}}
      tableList: [], // 存储所有表名
      currentPage: 1,
      pageSize: 10
    }
  },
  methods: {
    handlePageChange(newPage) {
      this.currentPage = newPage
    },

    handleSizeChange(newSize) {
      this.pageSize = newSize
    },

    handleTableSelected(dbName, tableName) {
      this.selectedDbName = dbName
      this.selectedTableName = tableName
    },

    handleTableData({ headers, data, tableName }) {
      // 更新当前选中表的数据（用于TableData组件显示）
      if (tableName === this.selectedTableName) {
        // 1. 将表数据添加到selectedTablesData（供表选择弹窗使用）
        // 使用 Vue.set 确保响应式更新
        this.$set(this.selectedTablesData, tableName, {
          headers: headers,
          data: data
        })

        // 2. 移除所有非固定的表（自动选择的默认表），保留用户主动勾选的表
        this.selectedTables = this.selectedTables.filter(table => table.isPinned)

        // 3. 清除所有表格的默认状态
        this.selectedTables.forEach(table => {
          table.isDefault = false
        })

        // 4. 检查当前表格是否已经在固定列表中
        const existingIndex = this.selectedTables.findIndex(table => table.tableName === tableName)
        if (existingIndex >= 0) {
          // 如果已存在（用户之前通过表选择器固定过），更新数据并设为默认
          this.selectedTables[existingIndex] = {
            ...this.selectedTables[existingIndex], // 保持原有属性（如isPinned: true）
            headers: headers,
            data: data,
            isDefault: true
          }
        } else {
          // 如果不存在，添加为新的默认表格（自动选择，未固定）
          this.selectedTables.push({
            tableName: tableName,
            headers: headers,
            data: data,
            isPinned: false, // 自动选择的表，未固定
            isDefault: true // 设为当前默认表
          })
        }
      }

      // 5. 将表名添加到tableList（供表选择弹窗显示选项）
      if (tableName && !this.tableList.includes(tableName)) {
        this.tableList.push(tableName)
      }
    },

    clearSelectedTable() {
      this.selectedTableName = ''
      this.selectedTables = []
      this.selectedTablesData = {}
    },

    // 切换表格固定状态
    toggleTablePin(tableName) {
      const tableIndex = this.selectedTables.findIndex(table => table.tableName === tableName)
      if (tableIndex >= 0) {
        this.selectedTables[tableIndex].isPinned = !this.selectedTables[tableIndex].isPinned
        // 强制更新视图
        this.$forceUpdate()
      }
    },

    // 处理用户在表选择弹窗中确认的表选择或删除表操作
    handleUpdateSelectedTables(userSelectedTables) {
      console.log('handleUpdateSelectedTables 被调用')
      console.log('传入的 userSelectedTables:', userSelectedTables)
      console.log('当前 selectedTables:', this.selectedTables)

      // 直接使用传入的表列表，不再保留默认表
      // 这样可以支持删除默认表
      this.selectedTables = userSelectedTables.map(table => ({
        tableName: table.tableName,
        headers: table.headers,
        data: table.data,
        isPinned: table.isPinned || false,
        isDefault: table.isDefault || false
      }))

      console.log('更新后 selectedTables:', this.selectedTables)
    }
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  flex-direction: row;
  height: 100vh;
  overflow: hidden;
}

:deep(.db-tree) {
  flex-shrink: 0; /* 防止收缩 */
}

:deep(.table-data-container) {
  flex: 1;
  min-width: 300px;
  overflow: hidden;
}

:deep(.ai-chat-container) {
  flex-shrink: 0; /* 防止收缩 */
}
</style>
