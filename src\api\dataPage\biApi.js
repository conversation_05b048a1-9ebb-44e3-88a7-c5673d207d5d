import request from '@/utils/request'

export function topMenu(params) {
  // 确保参数安全
  return request({
    url: 'api/bi/pageData/topMenu',
    method: 'get',
    params: {
      title: params.title
    }
  })
}

export function leftTableOneMenu(params) {
  return request({
    url: 'api/bi/pageData/leftTableOneMenu',
    method: 'get',
    params
  })
}

export function leftTableTwoMenu(params) {
  return request({
    url: 'api/bi/pageData/leftTableTwoMenu',
    method: 'get',
    params
  })
}

export function leftTableThreeMenu(params) {
  return request({
    url: 'api/bi/pageData/leftTableThreeMenu',
    method: 'get',
    params
  })
}

export function pageMiddleButtonMenu(params) {
  return request({
    url: 'api/bi/pageData/pageMiddleButtonMenu',
    method: 'get',
    params
  })
}

export function rightTableOneMenu(params) {
  return request({
    url: 'api/bi/pageData/rightTableOneMenu',
    method: 'get',
    params
  })
}

export function rightTableTwoMenu(params) {
  return request({
    url: 'api/bi/pageData/rightTableTwoMenu',
    method: 'get',
    params
  })
}

export function rightTableThreeMenu(params) {
  return request({
    url: 'api/bi/pageData/rightTableThreeMenu',
    method: 'get',
    params
  })
}
