/**
 * 管理员权限检查工具
 * 用于检查当前用户是否具有管理员权限
 */
import store from '@/store'

export const AdminPermissionChecker = {
  /**
   * 检查当前用户是否为管理员
   * @returns {boolean} 是否为管理员
   */
  isCurrentUserAdmin() {
    // 获取当前用户信息和角色
    const user = store.getters.user
    const roles = store.getters.roles

    // 方法1: 检查用户对象的 isAdmin 属性（最直接的方式）
    if (user && typeof user.isAdmin === 'boolean') {
      return user.isAdmin
    }

    // 方法2: 检查角色数组中是否包含 'admin'
    if (roles && Array.isArray(roles)) {
      return roles.some(role => {
        if (typeof role === 'string') {
          return role === 'admin' || role.toLowerCase().includes('admin')
        } else if (typeof role === 'object') {
          return role.name === 'admin' || role.code === 'admin' ||
                 (role.name && role.name.toLowerCase().includes('admin')) ||
                 (role.code && role.code.toLowerCase().includes('admin'))
        }
        return false
      })
    }

    // 默认返回 false（非管理员）
    return false
  }
}

// 导出默认方法，方便直接调用
export default AdminPermissionChecker.isCurrentUserAdmin
