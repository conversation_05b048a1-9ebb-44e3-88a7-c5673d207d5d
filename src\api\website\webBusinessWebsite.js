import request from '@/utils/request'

export function add(data) {
  return request({
    url: 'api/webBusinessWebsite',
    method: 'post',
    data
  })
}

export function del(ids) {
  return request({
    url: 'api/webBusinessWebsite/',
    method: 'delete',
    data: ids
  })
}

export function edit(data) {
  return request({
    url: 'api/webBusinessWebsite',
    method: 'put',
    data
  })
}

export default { add, edit, del }

export function queryDel(data) {
  return request({
    url: '/api/webBusinessWebsite/beDeleted',
    method: 'get',
    params: data
  })
}

export function recycle(data) {
  return request({
    url: '/api/webBusinessWebsite/recycle',
    method: 'get',
    params: data
  })
}
