import router from './routers'
import store from '@/store'
import Config from '@/settings'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css'// progress bar style
import { getToken } from '@/utils/auth' // getToken from cookie
import { buildMenus } from '@/api/system/menu'
import { filterAsyncRouter } from '@/store/modules/permission'

NProgress.configure({ showSpinner: false })// NProgress Configuration

const whiteList = ['/login']// no redirect whitelist

router.beforeEach((to, from, next) => {
  if (to.meta.title) {
    document.title = to.meta.title + ' - ' + Config.title
  }
  NProgress.start()
  if (getToken()) {
    // 已登录且要跳转的页面是登录页
    if (to.path === '/login') {
      next({ path: '/' })
      NProgress.done()
    } else {
      if (store.getters.roles.length === 0) { // 判断当前用户是否已拉取完user_info信息
        store.dispatch('GetInfo').then(() => { // 拉取user_info
          // 动态路由，拉取菜单
          loadMenus(next, to)
        }).catch(() => {
          store.dispatch('LogOut').then(() => {
            location.reload() // 为了重新实例化vue-router对象 避免bug
          })
        })
      // 登录时未拉取 菜单，在此处拉取
      } else if (store.getters.loadMenus) {
        // 修改成false，防止死循环
        store.dispatch('updateLoadMenus')
        loadMenus(next, to)
      } else {
        next()
      }
    }
  } else {
    /* has no token*/
    if (whiteList.indexOf(to.path) !== -1) { // 在免登录白名单，直接进入
      next()
    } else {
      next(`/login?redirect=${to.fullPath}`) // 否则全部重定向到登录页
      NProgress.done()
    }
  }
})

export const loadMenus = (next, to) => {
  buildMenus().then(res => {
    const sdata = JSON.parse(JSON.stringify(res))
    const rdata = JSON.parse(JSON.stringify(res))

    // 处理AI数据分析菜单项，为其添加独立路由
    processAnalyseDBRoutes(rdata)

    const sidebarRoutes = filterAsyncRouter(sdata)
    const rewriteRoutes = filterAsyncRouter(rdata, false, true)
    rewriteRoutes.push({ path: '*', redirect: '/404', hidden: true })

    store.dispatch('GenerateRoutes', rewriteRoutes).then(() => { // 存储路由
      router.addRoutes(rewriteRoutes) // 动态添加可访问路由表
      next({ ...to, replace: true })
    })
    store.dispatch('SetSidebarRouters', sidebarRoutes)
  })
}

// 处理AI数据分析路由，添加独立的全屏路由
const processAnalyseDBRoutes = (routes) => {
  const processRoute = (route) => {
    // 检查是否是AI数据分析菜单项
    if (route.meta && route.meta.title &&
        (route.meta.title.includes('AI数据分析') || route.meta.title === 'AI数据分析')) {
      // 添加独立的全屏路由
      const fullscreenPath = route.path + '-fullscreen'
      const fullscreenRoute = {
        path: fullscreenPath,
        name: route.name + 'Fullscreen',
        component: route.component,
        meta: {
          ...route.meta,
          title: route.meta.title + '(全屏)',
          noCache: true
        },
        hidden: true
      }
      routes.push(fullscreenRoute)
    }

    // 递归处理子路由
    if (route.children && route.children.length > 0) {
      route.children.forEach(child => processRoute(child))
    }
  }

  routes.forEach(route => processRoute(route))
}

router.afterEach(() => {
  NProgress.done() // finish progress bar
})
