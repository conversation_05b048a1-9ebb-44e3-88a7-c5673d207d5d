import request from '@/utils/request'

// 保存表注释
export function saveTableComment(data) {
  return request({
    url: '/api/table/comment',
    method: 'post',
    data: {
      dbName: data.dbName,
      tableName: data.tableName,
      comment: data.comment
    }
  })
}

// 获取表注释
export function getTableComment(data) {
  return request({
    url: '/api/table/findcomment',
    method: 'get',
    params: {
      tableName: data.tableName
    }
  })
}
