<template>
  <div :class="['file-upload-container', size]">
    <el-upload
      :action="action"
      :list-type="listType"
      :auto-upload="false"
      :file-list="fileList"
      :on-preview="handlePreview"
      :before-remove="handleBeforeRemove"
      :on-remove="handleRemove"
      :on-change="handleChange"
      :limit="limit"
      :disabled="disabled"
      :multiple="multiple"
      :accept="accept"
      :class="{ 'upload-container': true }"
    >
      <slot>
        <i class="el-icon-plus" />
        <div v-if="showUploadText" class="el-upload__text">{{ uploadText }}</div>
      </slot>
    </el-upload>

    <!-- 图片预览对话框 -->
    <el-dialog :visible.sync="dialogVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="">
    </el-dialog>
  </div>
</template>

<script>
import { uploadFile, deleteFile } from '@/utils/fileUpload'
import { mapGetters } from 'vuex'

export default {
  name: 'FileUpload',
  props: {
    // 文件列表
    value: {
      type: Array,
      default: () => []
    },
    // 自定义上传接口地址
    apiUrl: {
      type: String,
      default: '/api/minio/upload' // 默认使用MinIO上传接口
    },
    // 限制上传数量
    limit: {
      type: Number,
      default: 5
    },
    // 上传列表的类型，可选值为text/picture/picture-card
    listType: {
      type: String,
      default: 'picture-card'
    },
    // 是否禁用
    disabled: {
      type: Boolean,
      default: false
    },
    // 组件尺寸：normal/small
    size: {
      type: String,
      default: 'normal'
    },
    // 文件JSON字段字符串，用于和后端交互
    fieldValue: {
      type: String,
      default: ''
    },
    // 是否显示上传文字提示
    showUploadText: {
      type: Boolean,
      default: false
    },
    // 上传文字提示
    uploadText: {
      type: String,
      default: '点击上传'
    },
    // 是否允许多选
    multiple: {
      type: Boolean,
      default: false
    },
    // 接受上传的文件类型
    accept: {
      type: String,
      default: 'image/*'
    },
    // 是否立即上传到服务器
    uploadToServer: {
      type: Boolean,
      default: true // 默认上传到服务器 - 使用MinIO
    },
    // 是否使用base64编码 - 当不想上传到服务器时使用
    useBase64: {
      type: Boolean,
      default: false
    },
    // 是否隐藏删除按钮（查看模式下使用）
    hideRemove: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      fileList: [],
      action: '#', // 占位用，我们不使用el-upload的上传功能
      dialogVisible: false,
      dialogImageUrl: ''
    }
  },
  computed: {
    ...mapGetters(['fileUploadApi', 'imagesUploadApi'])
  },
  watch: {
    value: {
      handler(val) {
        this.initFileList()
      },
      immediate: true
    },
    fieldValue: {
      handler(val) {
        this.initFileListFromField()
      },
      immediate: true
    }

  },

  methods: {
    // 初始化文件列表
    initFileList() {
      if (Array.isArray(this.value)) {
        this.fileList = [...this.value]
      } else {
        this.fileList = []
      }
    },

    // 从字段值初始化文件列表
    initFileListFromField() {
      if (!this.fieldValue) {
        this.fileList = []
        return
      }

      try {
        console.log('初始化文件列表, 字段值:', this.fieldValue)

        // 检查是否是JSON字符串
        if (this.fieldValue.startsWith('[')) {
          // 是JSON格式，尝试解析
          const files = JSON.parse(this.fieldValue)
          if (Array.isArray(files)) {
            this.fileList = files.map(item => {
              return {
                name: item.name || '文件',
                url: item.url,
                id: item.id || ''
              }
            })
          }
        } else if (this.fieldValue.includes('http')) {
          // 是URL或URL列表，转换为文件列表
          const urls = this.fieldValue.split(',').filter(url => url.trim())
          this.fileList = urls.map((url) => {
            const fileName = url.split('/').pop() || '未知文件'
            return {
              name: fileName,
              url: url,
              id: fileName
            }
          })
        }

        console.log('初始化后的文件列表:', this.fileList)
      } catch (e) {
        console.error('解析文件数据失败:', e)

        // 如果解析失败，但值像URL，则直接创建文件对象
        if (typeof this.fieldValue === 'string' && this.fieldValue.includes('http')) {
          this.fileList = [{
            name: '文件1',
            url: this.fieldValue,
            id: this.fieldValue.split('/').pop() || ''
          }]
          console.log('创建了单个文件对象:', this.fileList)
        } else {
          this.fileList = []
        }
      }
    },

    // 处理文件变更
    async handleChange(file, fileList) {
      if (file.status === 'ready') {
        // 如果是新上传的文件
        if (this.uploadToServer && !this.useBase64) {
          // 上传到服务器 (MinIO)
          try {
            console.log('准备上传文件到MinIO:', file.name)
            const result = await uploadFile(file.raw, null, null, this.apiUrl)
            console.log('MinIO上传结果:', result)

            // 处理返回的URL，确保是完整URL
            const fullUrl = this.getFullImageUrl(result.url)

            // 替换文件的url为处理后的完整url
            const newFile = {
              name: file.name,
              url: fullUrl,
              id: result.id || file.name
            }

            // 更新fileList中对应的文件
            const index = fileList.findIndex(f => f.uid === file.uid)
            if (index !== -1) {
              fileList[index] = { ...fileList[index], ...newFile }
            }

            this.fileList = [...fileList]

            // 构建完整的事件对象并触发
            const eventData = {
              fileList: this.fileList,
              action: 'success',
              field: 'fieldValue',
              fieldName: 'fieldValue',
              file: {
                name: file.name,
                response: {
                  url: fullUrl
                }
              }
            }

            // 触发变更事件
            this.$emit('input', this.fileList)
            this.$emit('change', eventData)

            // 更新字段值
            if (this.fieldValue !== undefined) {
              const jsonStr = JSON.stringify(this.fileList.map(file => {
                return {
                  name: file.name,
                  url: file.url,
                  id: file.id || ''
                }
              }))
              console.log('上传成功后更新字段值:', jsonStr)
              this.$emit('update:fieldValue', jsonStr)
            }
          } catch (error) {
            console.error('上传文件失败', error)
            // 移除上传失败的文件
            this.fileList = this.fileList.filter(f => f.uid !== file.uid)
          }
        } else {
          // 使用base64处理的代码保持不变
          // ...
        }
      }
    },
    // 删除前确认
    handleBeforeRemove(file, fileList) {
      // 如果隐藏删除按钮，则阻止删除操作
      if (this.hideRemove) {
        console.log('查看模式下禁止删除文件')
        return false
      }

      // 删除确认提示
      const confirmMessage = `确定要删除吗？\n\n删除后需要点击"提交"按钮保存更改。`

      return this.$confirm(confirmMessage, '⚠️ 删除确认', {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: false,
        customClass: 'delete-confirm-dialog'
      }).then(() => {
        // 用户确认删除，返回true允许删除
        return true
      }).catch(() => {
        // 用户取消删除，抛出错误阻止删除
        console.log('用户取消删除操作')
        throw new Error('用户取消删除')
      })
    },

    // 处理文件移除（删除确认后执行）
    handleRemove(file, fileList) {
      // 如果文件有url并且上传过服务器，尝试删除服务器文件
      if (this.uploadToServer && !this.useBase64 && file.url) {
        // 使用完整URL进行删除
        console.log('删除文件，完整URL:', file.url)
        deleteFile(file.url, null, null, '/api/minio/delete')
          .then(() => {
            this.$notify({
              title: '✅ 删除成功',
              message: '文件已删除，🔔 请记得点击"确认"按钮保存更改！',
              type: 'warning',
              duration: 4000,
              position: 'top-right'
            })
          })
          .catch(error => {
            console.error('删除服务器文件失败', error)
            this.$message.error('文件删除失败，请重试')
          })
      }

      this.fileList = [...fileList]

      // 在事件中包含被删除的文件信息
      const eventParams = {
        fileList: this.fileList,
        removedFile: file
      }

      this.$emit('input', this.fileList)
      this.$emit('change', eventParams)

      // 如果需要以JSON字符串形式返回
      if (this.fieldValue !== undefined) {
        const jsonStr = JSON.stringify(this.fileList.map(file => {
          return {
            name: file.name,
            url: file.url,
            id: file.id || ''
          }
        }))
        console.log('更新字段值:', jsonStr)
        this.$emit('update:fieldValue', jsonStr)
      }
    },

    // 获取文件完整URL
    getFullImageUrl(url) {
      console.log('原始URL:', url)

      if (!url) return ''

      // 如果已经是完整URL则直接返回
      if (url.startsWith('http://') || url.startsWith('https://')) {
        console.log('已是完整URL，直接返回:', url)
        return url
      }

      // 如果URL包含域名但没有协议，添加HTTPS协议（避免重定向）
      if (url.includes('.com/') || url.includes('.cn/') || url.includes('.net/')) {
        const fullUrl = `https://${url}` // 改为https
        console.log('添加HTTPS协议后的URL:', fullUrl)
        return fullUrl
      }

      // 如果是相对路径，通过后端API获取
      const apiUrl = `/api/minio/url?fileName=${encodeURIComponent(url)}`
      console.log('使用API URL:', apiUrl)
      return apiUrl
    },

    // 处理预览
    handlePreview(file) {
      // 确保URL是完整的访问路径
      this.dialogImageUrl = this.getFullImageUrl(file.url)
      this.dialogVisible = true
    },

    // 发送变更事件
    emitChange() {
      // 构建事件对象
      const eventData = {
        fileList: this.fileList,
        action: 'success',
        field: 'fieldValue', // 添加字段名
        fieldName: 'fieldValue', // 添加一致的字段名
        file: {
          response: {
            url: this.fileList.length > 0 ? this.fileList[this.fileList.length - 1].url : ''
          }
        }
      }

      this.$emit('input', this.fileList)
      this.$emit('change', eventData) // 传递完整的事件对象

      // 如果需要以JSON字符串形式返回
      if (this.fieldValue !== undefined) {
        const jsonStr = JSON.stringify(this.fileList.map(file => {
          return {
            name: file.name,
            url: file.url,
            id: file.id || ''
          }
        }))
        console.log('更新字段值:', jsonStr)
        this.$emit('update:fieldValue', jsonStr)
      }
    },

    // 手动清空文件列表
    clearFiles() {
      this.fileList = []
      this.emitChange()
    },

    // 获取文件列表
    getFileList() {
      return this.fileList
    }
  }
}
</script>

<style scoped>
.file-upload-container {
  width: 100%;
}

.file-upload-container.small .el-upload--picture-card {
  width: 80px;
  height: 80px;
  line-height: 84px;
}

.file-upload-container.small .el-upload-list--picture-card .el-upload-list__item {
  width: 80px;
  height: 80px;
}

.file-upload-container.small .el-icon-plus {
  font-size: 20px;
}

.file-upload-container.normal .el-upload--picture-card {
  width: 148px;
  height: 148px;
  line-height: 148px;
}

.file-upload-container.normal .el-upload-list--picture-card .el-upload-list__item {
  width: 148px;
  height: 148px;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
}
</style>
