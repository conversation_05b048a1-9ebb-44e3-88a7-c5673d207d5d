<template>
  <div class="app-container">
    <!--工具栏-->
    <div class="head-container">
      <div v-if="crud.props.searchToggle">
        <!-- 搜索 -->
        <label class="el-form-item-label">户型编号</label>
        <el-input v-model="query.doorNumber" clearable placeholder="户型编号" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">户型名称</label>
        <el-input v-model="query.familyName" clearable placeholder="户型名称" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">归属楼盘</label>
        <el-input v-model="query.belongstoThebuilding" clearable placeholder="归属楼盘" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <label class="el-form-item-label">归属楼栋</label>
        <el-input v-model="query.belongtoThemuilding" clearable placeholder="归属楼栋" style="width: 185px;" class="filter-item" @keyup.enter.native="crud.toQuery" />
        <rrOperation :crud="crud" />
      </div>
      <!--如果想在工具栏加入更多按钮，可以使用插槽方式， slot = 'left' or 'right'-->
      <crudOperation :permission="permission" />
      <!--表单组件-->
      <el-dialog :close-on-click-modal="false" :before-close="crud.cancelCU" :visible.sync="crud.status.cu > 0" :title="crud.status.title" width="500px">
        <el-form ref="form" :model="form" :rules="rules" size="small" label-width="80px">
          <el-form-item label="序号" prop="id">
            <el-input v-model="form.id" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="户型编号">
            <el-input v-model="form.doorNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="户型名称">
            <el-input v-model="form.familyName" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="户型格局">
            <el-input v-model="form.doorPattern" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="户型建筑面积">
            <el-input v-model="form.buildingareaOfhouseType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="楼盘编号">
            <el-input v-model="form.buildingNumber" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="归属楼盘">
            <el-input v-model="form.belongstoThebuilding" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="归属楼栋">
            <el-input v-model="form.belongtoThemuilding" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="总户数">
            <el-input v-model="form.totalnumberOfhouseType" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="参考均价（元/平）">
            <el-input v-model="form.referenceAveragePrice" style="width: 370px;" />
          </el-form-item>
          <el-form-item label="总价">
            <el-input v-model="form.totalPrice" style="width: 370px;" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button type="text" @click="crud.cancelCU">取消</el-button>
          <el-button :loading="crud.status.cu === 2" type="primary" @click="crud.submitCU">确认</el-button>
        </div>
      </el-dialog>
      <!--表格渲染-->
      <el-table ref="table" v-loading="crud.loading" :data="crud.data" size="small" style="width: 100%;" @selection-change="crud.selectionChangeHandler">
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="序号" />
        <el-table-column prop="doorNumber" label="户型编号" />
        <el-table-column prop="familyName" label="户型名称" />
        <el-table-column prop="doorPattern" label="户型格局" />
        <el-table-column prop="buildingareaOfhouseType" label="户型建筑面积" />
        <el-table-column prop="buildingNumber" label="楼盘编号" />
        <el-table-column prop="belongstoThebuilding" label="归属楼盘" />
        <el-table-column prop="belongtoThemuilding" label="归属楼栋" />
        <el-table-column prop="totalnumberOfhouseType" label="总户数" />
        <el-table-column prop="referenceAveragePrice" label="参考均价（元/平）" />
        <el-table-column prop="totalPrice" label="总价" />
        <el-table-column v-if="checkPer(['admin','loupanHousetype:edit','loupanHousetype:del'])" label="操作" width="150px" align="center">
          <template slot-scope="scope">
            <udOperation
              :data="scope.row"
              :permission="permission"
            />
          </template>
        </el-table-column>
      </el-table>
      <!--分页组件-->
      <pagination />
    </div>
  </div>
</template>

<script>
import crudLoupanHousetype from '@/api/loupan/loupanHousetype'
import CRUD, { presenter, header, form, crud } from '@crud/crud'
import rrOperation from '@crud/RR.operation'
import crudOperation from '@crud/CRUD.operation'
import udOperation from '@crud/UD.operation'
import pagination from '@crud/Pagination'

const defaultForm = { id: null, doorNumber: null, familyName: null, doorPattern: null, buildingareaOfhouseType: null, buildingNumber: null, belongstoThebuilding: null, belongtoThemuilding: null, totalnumberOfhouseType: null, referenceAveragePrice: null, totalPrice: null }
export default {
  name: 'LoupanHousetype',
  components: { pagination, crudOperation, rrOperation, udOperation },
  mixins: [presenter(), header(), form(defaultForm), crud()],
  cruds() {
    return CRUD({ title: '楼盘户型汇总表', url: 'api/loupanHousetype', idField: 'id', sort: 'id,desc', crudMethod: { ...crudLoupanHousetype }})
  },
  data() {
    return {
      permission: {
        add: ['admin', 'loupanHousetype:add'],
        edit: ['admin', 'loupanHousetype:edit'],
        del: ['admin', 'loupanHousetype:del']
      },
      rules: {
        id: [
          { required: true, message: '序号不能为空', trigger: 'blur' }
        ]
      },
      queryTypeOptions: [
        { key: 'doorNumber', display_name: '户型编号' },
        { key: 'familyName', display_name: '户型名称' },
        { key: 'belongstoThebuilding', display_name: '归属楼盘' },
        { key: 'belongtoThemuilding', display_name: '归属楼栋' }
      ]
    }
  },
  methods: {
    // 钩子：在获取表格数据之前执行，false 则代表不获取数据
    [CRUD.HOOK.beforeRefresh]() {
      return true
    }
  }
}
</script>

<style scoped>

</style>
